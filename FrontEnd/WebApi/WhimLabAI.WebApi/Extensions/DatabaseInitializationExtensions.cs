using Microsoft.EntityFrameworkCore;
using WhimLabAI.Infrastructure.Data;
using WhimLabAI.Infrastructure.Data.Seeding;

namespace Microsoft.Extensions.Hosting;

/// <summary>
/// 数据库初始化扩展方法
/// </summary>
public static class DatabaseInitializationExtensions
{
    /// <summary>
    /// 初始化数据库
    /// </summary>
    public static async Task<IHost> InitializeDatabaseAsync(this IHost host)
    {
        using var scope = host.Services.CreateScope();
        var services = scope.ServiceProvider;
        var logger = services.GetRequiredService<ILogger<WhimLabAIDbContext>>();

        try
        {
            logger.LogInformation("Initializing database...");

            var context = services.GetRequiredService<WhimLabAIDbContext>();

            // 应用迁移
            logger.LogInformation("Starting database migration...");
            await context.Database.MigrateAsync();
            logger.LogInformation("Database migration completed");

            // 种子数据
            logger.LogInformation("Starting database seeding...");
            var unifiedSeeder = new UnifiedDataSeeder(
                services,
                services.GetRequiredService<ILogger<UnifiedDataSeeder>>()
            );

            // 在开发环境包含示例数据
            var env = services.GetRequiredService<IHostEnvironment>();
            var includeSampleData = env.IsDevelopment();
            await unifiedSeeder.SeedAllAsync(includeSampleData);
            logger.LogInformation("Database seeding completed");

            logger.LogInformation("Database initialization completed successfully");
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "An error occurred while initializing the database");
            throw;
        }

        return host;
    }

    /// <summary>
    /// 确保数据库已创建
    /// </summary>
    public static async Task<IHost> EnsureDatabaseCreatedAsync(this IHost host)
    {
        using var scope = host.Services.CreateScope();
        var context = scope.ServiceProvider.GetRequiredService<WhimLabAIDbContext>();

        await context.Database.EnsureCreatedAsync();

        return host;
    }

    /// <summary>
    /// 删除并重新创建数据库（仅用于开发环境）
    /// </summary>
    public static async Task<IHost> RecreateDatabaseAsync(this IHost host)
    {
        using var scope = host.Services.CreateScope();
        var services = scope.ServiceProvider;
        var env = services.GetRequiredService<IHostEnvironment>();

        if (!env.IsDevelopment())
        {
            throw new InvalidOperationException("Database recreation is only allowed in development environment");
        }

        var context = services.GetRequiredService<WhimLabAIDbContext>();
        var logger = services.GetRequiredService<ILogger<WhimLabAIDbContext>>();

        logger.LogWarning("Deleting existing database...");
        await context.Database.EnsureDeletedAsync();

        logger.LogInformation("Creating new database...");
        await context.Database.EnsureCreatedAsync();

        logger.LogInformation("Running migrations and seed data...");
        // 应用迁移
        await context.Database.MigrateAsync();

        // 种子数据
        var unifiedSeeder = new UnifiedDataSeeder(
            services,
            services.GetRequiredService<ILogger<UnifiedDataSeeder>>()
        );
        await unifiedSeeder.SeedAllAsync(true); // 包含示例数据

        return host;
    }
}
