using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.AspNetCore.Authorization;
using Microsoft.IdentityModel.Tokens;
using Microsoft.IdentityModel.JsonWebTokens;
using Microsoft.OpenApi.Models;
using Serilog;
using Serilog.Events;
using System.Text;
using WhimLabAI.Application;
using WhimLabAI.Infrastructure;
using WhimLabAI.Infrastructure.Data;
using WhimLabAI.Infrastructure.Security;
using WhimLabAI.Infrastructure.ApiSecurity;
using Microsoft.AspNetCore.RateLimiting;
using WhimLabAI.WebApi.Extensions;
using WhimLabAI.WebApi.Middleware;
using WhimLabAI.WebApi.Filters;
using Prometheus;
using Hangfire;
using Hangfire.PostgreSql;
using WhimLabAI.Infrastructure.PaymentGateways;
using WhimLabAI.Infrastructure.Logging;
using Microsoft.Extensions.Diagnostics.HealthChecks;

// 配置 Serilog - 初始化启动日志（启动阶段使用）
Log.Logger = new LoggerConfiguration()
    .MinimumLevel.Override("Microsoft", LogEventLevel.Information)
    .MinimumLevel.Override("Microsoft.AspNetCore", LogEventLevel.Warning)
    .Enrich.FromLogContext()
    .WriteTo.Console()
    .WriteTo.File("logs/whimlabai-.txt", rollingInterval: RollingInterval.Day)
    .CreateBootstrapLogger();

try
{
    Log.Information("Starting WhimLabAI Web API");

    var builder = WebApplication.CreateBuilder(args);
    Log.Information("WebApplication builder created");

    // 配置服务启动超时
    builder.Services.Configure<HostOptions>(options =>
    {
        options.ShutdownTimeout = TimeSpan.FromSeconds(30);
    });

    // Clear existing configuration sources to avoid conflicts
    builder.Configuration.Sources.Clear();

    // 配置文件加载顺序
    Log.Information("Loading configuration files...");
    try
    {
        builder.Configuration
            .AddJsonFile("appsettings.json", optional: false, reloadOnChange: true)
            .AddJsonFile($"appsettings.{builder.Environment.EnvironmentName}.json", optional: true, reloadOnChange: true)
            .AddJsonFile("appsettings.LogSanitization.json", optional: true, reloadOnChange: true)
            .AddJsonFile("appsettings.QuickDev.json", optional: true, reloadOnChange: true)
            .AddEnvironmentVariables();
        Log.Information("Environment variables loaded");

        // Re-enable environment variable replacements with error handling
        builder.Configuration.AddEnvironmentVariableReplacements();
        Log.Information("Environment variable replacements loaded");

        // // Test configuration
        // var testConnection = builder.Configuration.GetConnectionString("DefaultConnection");
        // Log.Information("Test connection string (masked): {ConnectionString}",
        //     testConnection?.Replace("Password=", "Password=***") ?? "NULL");
    }
    catch (Exception ex)
    {
        Log.Error(ex, "Error loading configuration");
        throw;
    }
    Log.Information("Configuration loaded");

    // 配置 Serilog with sanitization support
    builder.Host.UseSerilog((context, services, configuration) =>
    {
        var sanitizer = services.GetService<ILogSanitizer>();
        var sanitizationOptions = context.Configuration.GetSection(LogSanitizationOptions.SectionName)
            .Get<LogSanitizationOptions>() ?? new LogSanitizationOptions();

        configuration
            .ReadFrom.Configuration(context.Configuration)
            .ReadFrom.Services(services)
            .MinimumLevel.Override("Microsoft", LogEventLevel.Information)
            .MinimumLevel.Override("Microsoft.AspNetCore", LogEventLevel.Warning)
            .Enrich.FromLogContext()
            .Enrich.WithMachineName()
            .Enrich.WithThreadId()
            .Enrich.WithProperty("ApplicationName", "WhimLabAI.WebApi");

        // 如果启用了脱敏且在生产环境或配置了在开发环境也启用
        if (sanitizer != null && sanitizationOptions.Enabled &&
            (context.HostingEnvironment.IsProduction() || sanitizationOptions.EnabledInDevelopment))
        {
            configuration.Enrich.With(new SanitizingLogEventEnricher(sanitizer, sanitizationOptions));

            // 使用脱敏的输出
            configuration.WriteTo.SanitizedConsole(sanitizer, sanitizationOptions);
            configuration.WriteTo.SanitizedFile(
                sanitizer,
                "logs/whimlabai-.txt",
                sanitizationOptions,
                rollingInterval: RollingInterval.Day);
            configuration.WriteTo.SanitizedJsonFile(
                sanitizer,
                "logs/whimlabai-.json",
                sanitizationOptions,
                rollingInterval: RollingInterval.Day);
        }
        else
        {
            // 不脱敏的输出（开发环境默认）
            configuration.WriteTo.Console();
            configuration.WriteTo.File("logs/whimlabai-.txt", rollingInterval: RollingInterval.Day);
        }
    });

    // Add services to the container.
    builder.Services.AddControllers(options =>
    {
        // 添加全局CSRF保护（仅对需要认证的端点）
        if (!builder.Environment.IsDevelopment())
        {
            options.Filters.Add<WhimLabAI.WebApi.Filters.GlobalCsrfProtectionFilter>();
        }
    });

    // 配置 CORS
    builder.Services.AddCors(options =>
    {
        options.AddPolicy("AllowFrontends", policy =>
        {
            policy.WithOrigins(
                "https://localhost:7216", // Admin HTTPS
                "http://localhost:5139",  // Admin HTTP
                "https://localhost:7040", // Customer HTTPS
                "http://localhost:5076"   // Customer HTTP
            )
            .AllowAnyMethod()
            .AllowAnyHeader()
            .AllowCredentials()
            .WithExposedHeaders("Token-Expired", "X-Correlation-ID");
        });

        options.AddPolicy("AllowFrontend", policy =>
        {
            var corsSettings = builder.Configuration.GetSection("CorsSettings");
            var allowedOriginsConfig = corsSettings["AllowedOrigins"];

            // Support both array format and comma-separated string from environment variables
            string[] allowedOrigins;
            if (!string.IsNullOrEmpty(allowedOriginsConfig) && allowedOriginsConfig.Contains(','))
            {
                allowedOrigins = EnvironmentVariableHelper.ParseArray(allowedOriginsConfig);
            }
            else
            {
                allowedOrigins = corsSettings.GetSection("AllowedOrigins").Get<string[]>()
                    ?? new[] { "https://localhost:7216", "https://localhost:7040" };
            }

            policy.WithOrigins(allowedOrigins)
            .AllowAnyHeader()
            .AllowAnyMethod()
            .AllowCredentials() // 对于 SignalR 必需
            .WithExposedHeaders("Token-Expired", "X-Correlation-ID");
        });

        // 开发环境允许所有来源
        if (builder.Environment.IsDevelopment())
        {
            options.AddPolicy("DevelopmentCors", policy =>
            {
                policy.AllowAnyOrigin()
                      .AllowAnyHeader()
                      .AllowAnyMethod();
            });
        }
    });

    // 添加 HttpClient
    builder.Services.AddHttpClient();

    // 添加内存缓存（会话超时中间件需要）
    builder.Services.AddMemoryCache();

    // 添加响应缓存服务
    builder.Services.AddResponseCaching(options =>
    {
        options.MaximumBodySize = 10 * 1024 * 1024; // 10MB max cacheable response size
        options.SizeLimit = 100 * 1024 * 1024; // 100MB total cache size
        options.UseCaseSensitivePaths = false;
    });

    // 添加输出缓存（.NET 7+性能更好的替代方案）
    builder.Services.AddOutputCache(options =>
    {
        // 默认策略：缓存5分钟
        options.AddBasePolicy(builder => builder
            .Expire(TimeSpan.FromMinutes(5))
            .SetVaryByQuery("page", "pageSize", "search", "sort", "filter"));

        // 为公共数据定义特定策略
        options.AddPolicy("PublicData", builder => builder
            .Expire(TimeSpan.FromMinutes(10))
            .SetVaryByQuery("page", "pageSize", "search", "sort", "filter")
            .Tag("public"));

        // 为用户特定数据定义策略
        options.AddPolicy("UserSpecific", builder => builder
            .Expire(TimeSpan.FromMinutes(5))
            .SetVaryByHeader("Authorization")
            .SetVaryByQuery("page", "pageSize"));

        // 为静态数据定义长期缓存策略
        options.AddPolicy("StaticData", builder => builder
            .Expire(TimeSpan.FromHours(1))
            .Tag("static"));
    });

    // 配置 SignalR
    builder.Services.AddSignalR(options =>
    {
        options.EnableDetailedErrors = builder.Environment.IsDevelopment();
        options.MaximumReceiveMessageSize = 102400; // 100KB
    }).AddJsonProtocol(options =>
    {
        options.PayloadSerializerOptions.PropertyNamingPolicy = System.Text.Json.JsonNamingPolicy.CamelCase;
    });

    // 注册 Infrastructure 和 Application 层服务
    Log.Information("Registering Infrastructure services...");
    builder.Services.AddInfrastructure(builder.Configuration);
    Log.Information("Infrastructure services registered");

    Log.Information("Registering Application services...");
    builder.Services.AddApplication(builder.Configuration);
    Log.Information("Application services registered");

    // 注册实时通知服务
    Log.Information("Registering RealtimeNotificationService...");
    builder.Services.AddScoped<WhimLabAI.Abstractions.Application.IRealtimeNotificationService,
        WhimLabAI.WebApi.Services.RealtimeNotificationService>();
    Log.Information("RealtimeNotificationService registered");

    // 注册安全服务
    Log.Information("Registering security services...");
    WhimLabAI.Infrastructure.Security.SecurityExtensions.AddSecurityServices(builder.Services, builder.Configuration);
    Log.Information("Security services registered");

    // 配置API版本控制
    Log.Information("Configuring API versioning...");
    builder.Services.AddApiVersioningConfiguration();
    Log.Information("API versioning configured");

    // 配置输入验证
    Log.Information("Configuring input validation...");
    builder.Services.AddInputValidation();
    Log.Information("Input validation configured");

    // 配置速率限制
    Log.Information("Configuring rate limiting...");
    builder.Services.AddRateLimitingConfiguration(builder.Configuration);
    Log.Information("Rate limiting configured");

    // 配置会话超时
    builder.Services.Configure<WhimLabAI.Shared.Options.SessionTimeoutOptions>(
        builder.Configuration.GetSection(WhimLabAI.Shared.Options.SessionTimeoutOptions.SectionName));

    // 配置订阅定价
    builder.Services.Configure<WhimLabAI.Shared.Options.SubscriptionPricingOptions>(
        builder.Configuration.GetSection("SubscriptionPricing"));

    // 配置AI模型定价
    builder.Services.Configure<WhimLabAI.Shared.Options.AIModelPricingOptions>(
        builder.Configuration.GetSection("AIModelPricing"));

    // 配置营销成本
    builder.Services.Configure<WhimLabAI.Shared.Options.MarketingCostOptions>(
        builder.Configuration.GetSection("MarketingCost"));

    // 配置外部服务
    builder.Services.Configure<WhimLabAI.Shared.Options.ExternalServiceOptions>(
        builder.Configuration.GetSection("ExternalServices"));

    // 配置 API 文档
    builder.Services.AddEndpointsApiExplorer();
    builder.Services.AddSwaggerGen(options =>
    {
        options.SwaggerDoc("v1", new OpenApiInfo
        {
            Title = "WhimLabAI API",
            Version = "v1",
            Description = "Enterprise AI Agent Platform API"
        });

        // 解决控制器路由冲突
        options.ResolveConflictingActions(apiDescriptions => apiDescriptions.First());

        // 配置自定义 SchemaId 以避免类型名称冲突
        options.CustomSchemaIds(type =>
        {
            // 定义冲突类型集合
            var conflictingTypes = new HashSet<string>
            {
                "DeviceListDto", "DeviceDto", "PerformanceAlert", "PerformanceMetrics",
                "PermissionDto", "RoleDto", "UserDto", "AdminListDto", "AdminCustomerListDto"
            };

            // 递归函数来生成类型名称，包括处理冲突
            string GetTypeName(Type t)
            {
                if (!t.IsGenericType)
                {
                    // 非泛型类型
                    var name = t.Name;

                    // 检查是否为冲突类型
                    if (conflictingTypes.Contains(name) && t.Namespace != null)
                    {
                        if (t.Namespace.Contains("Admin.Rbac"))
                            return $"Rbac_{name}";
                        else if (t.Namespace.Contains("Admin.Permission"))
                            return $"Permission_{name}";
                        else if (t.Namespace.Contains("Admin.Role"))
                            return $"Role_{name}";
                        else if (t.Namespace.Contains("Admin.User"))
                            return $"AdminUser_{name}";
                        else if (t.Namespace.Contains("Admin.Customer"))
                            return $"AdminCustomer_{name}";
                        else if (t.Namespace.Contains("Customer.Security"))
                            return $"CustomerSecurity_{name}";
                        else if (t.Namespace.Contains("Device") && !t.Namespace.Contains("Customer"))
                            return $"Device_{name}";
                        else if (t.Namespace.Contains("Performance"))
                            return $"Performance_{name}";
                        else
                        {
                            var namespaceParts = t.Namespace.Split('.');
                            if (namespaceParts.Length >= 2)
                            {
                                var prefix = namespaceParts[^2];
                                return $"{prefix}_{name}";
                            }
                        }
                    }

                    return name;
                }
                else
                {
                    // 泛型类型
                    var genericTypeName = t.GetGenericTypeDefinition().Name.Split('`')[0];
                    var genericArguments = t.GetGenericArguments();

                    // 递归处理泛型参数
                    var genericNames = genericArguments.Select(arg => GetTypeName(arg));

                    return $"{genericTypeName}Of{string.Join("And", genericNames)}";
                }
            }

            return GetTypeName(type);
        });

        // 添加 JWT 认证
        options.AddSecurityDefinition("Bearer", new OpenApiSecurityScheme
        {
            Description = "JWT Authorization header using the Bearer scheme. Example: \"Authorization: Bearer {token}\"",
            Name = "Authorization",
            In = ParameterLocation.Header,
            Type = SecuritySchemeType.ApiKey,
            Scheme = "Bearer"
        });

        // 添加 API Key 认证
        options.AddSecurityDefinition("ApiKey", new OpenApiSecurityScheme
        {
            Description = "API Key Authorization. Example: \"X-API-Key: {api_key}\"",
            Name = "X-API-Key",
            In = ParameterLocation.Header,
            Type = SecuritySchemeType.ApiKey
        });

        options.AddSecurityRequirement(new OpenApiSecurityRequirement
        {
            {
                new OpenApiSecurityScheme
                {
                    Reference = new OpenApiReference
                    {
                        Type = ReferenceType.SecurityScheme,
                        Id = "Bearer"
                    }
                },
                Array.Empty<string>()
            },
            {
                new OpenApiSecurityScheme
                {
                    Reference = new OpenApiReference
                    {
                        Type = ReferenceType.SecurityScheme,
                        Id = "ApiKey"
                    }
                },
                Array.Empty<string>()
            }
        });
    });


    // 配置健康检查
    // Configure health checks
    // TODO: Temporarily disabled health checks to debug startup hang issue
    var healthChecksBuilder = builder.Services.AddHealthChecks()
        .AddNpgSql(
            builder.Configuration.GetConnectionString("DefaultConnection")!,
            name: "postgresql",
            timeout: TimeSpan.FromSeconds(10),
            tags: new[] { "db", "sql", "postgresql" })
        .AddRedis(
            builder.Configuration.GetConnectionString("Redis")!,
            name: "redis",
            timeout: TimeSpan.FromSeconds(10),
            tags: new[] { "cache", "redis" });

    // Add RabbitMQ health check - MassTransit already handles RabbitMQ connection
    // The health check will use MassTransit's built-in health checks instead
    // TODO: Temporarily disabled RabbitMQ health check - service not running
    var rabbitMQConfig = builder.Configuration.GetSection("RabbitMQ");
    if (rabbitMQConfig.Exists())
    {
        var host = rabbitMQConfig["Host"];
    
        // Only add health check if configuration is valid
        if (!string.IsNullOrEmpty(host) && host != "REPLACE_WITH_PRODUCTION_HOST")
        {
            // MassTransit provides its own health checks that we can use
            healthChecksBuilder.AddTypeActivatedCheck<WhimLabAI.WebApi.HealthChecks.MassTransitHealthCheck>(
                "rabbitmq",
                HealthStatus.Unhealthy,
                tags: new[] { "messaging", "rabbitmq" });
        }
    }

    // Add custom MinIO health check
    healthChecksBuilder.AddTypeActivatedCheck<WhimLabAI.WebApi.HealthChecks.MinioHealthCheck>(
        "minio", HealthStatus.Unhealthy,
        tags: new[] { "storage", "minio" });

    // Add business logic health check
    healthChecksBuilder.AddTypeActivatedCheck<WhimLabAI.WebApi.HealthChecks.BusinessHealthCheck>(
        "business", HealthStatus.Unhealthy,
        tags: new[] { "business", "ready" });

    // 配置响应压缩
    builder.Services.AddResponseCompression(options =>
    {
        options.EnableForHttps = true;
    });

    // 注册API安全服务
    builder.Services.AddApiSecurityServices(builder.Configuration);

    // 配置 Hangfire - defer connection string resolution
    builder.Services.AddHangfire((serviceProvider, configuration) =>
    {
        var config = serviceProvider.GetRequiredService<IConfiguration>();
        var connectionString = config.GetConnectionString("DefaultConnection");

        // Configure Hangfire without immediately connecting to database
        configuration
            .SetDataCompatibilityLevel(CompatibilityLevel.Version_180)
            .UseSimpleAssemblyNameTypeSerializer()
            .UseRecommendedSerializerSettings();

        // Only configure storage if connection string is valid
        if (!string.IsNullOrEmpty(connectionString) && connectionString != "REPLACE_WITH_YOUR_CONNECTION_STRING")
        {
            var connectionStringBuilder = new Npgsql.NpgsqlConnectionStringBuilder(connectionString)
            {
                CommandTimeout = 30,
                Timeout = 30,
                ConnectionIdleLifetime = 300
            };

            configuration.UsePostgreSqlStorage(options => options
                .UseNpgsqlConnection(connectionStringBuilder.ToString()));
        }
    });

    // 添加 Hangfire 服务器
    // TODO: Temporarily disabled to debug startup hang issue
    builder.Services.AddHangfireServer(options =>
    {
        options.WorkerCount = Environment.ProcessorCount * 2;
        options.Queues = new[] { "critical", "default", "low" };
        options.ServerName = $"WhimLabAI-{Environment.MachineName}";
    });

    // 清除默认的JWT Claim类型映射，保持claim类型的一致性
    System.IdentityModel.Tokens.Jwt.JwtSecurityTokenHandler.DefaultInboundClaimTypeMap.Clear();
    System.IdentityModel.Tokens.Jwt.JwtSecurityTokenHandler.DefaultOutboundClaimTypeMap.Clear();

    // 配置 JWT 认证
    var jwtSettings = builder.Configuration.GetSection("Jwt");
    // Use Serilog logger instead of creating a temporary service provider
    var secretKey = WhimLabAI.WebApi.Configuration.JwtSecurityKeyGenerator.GetOrGenerateSecretKey(builder.Configuration, null);
    var key = Encoding.UTF8.GetBytes(secretKey);

    builder.Services.AddAuthentication(options =>
    {
        options.DefaultAuthenticateScheme = JwtBearerDefaults.AuthenticationScheme;
        options.DefaultChallengeScheme = JwtBearerDefaults.AuthenticationScheme;
        options.DefaultScheme = JwtBearerDefaults.AuthenticationScheme;
    })
    .AddJwtBearer(options =>
    {
        options.RequireHttpsMetadata = !builder.Environment.IsDevelopment();
        options.SaveToken = true;
        options.TokenValidationParameters = new TokenValidationParameters
        {
            ValidateIssuerSigningKey = true,
            IssuerSigningKey = new SymmetricSecurityKey(key),
            ValidateIssuer = true,
            ValidIssuer = jwtSettings["Issuer"],
            ValidateAudience = true,
            ValidAudience = jwtSettings["Audience"],
            ValidateLifetime = true,
            ClockSkew = TimeSpan.Zero
        };

        // SignalR JWT 配置和会话验证
        options.Events = new JwtBearerEvents
        {
            OnMessageReceived = context =>
            {
                var accessToken = context.Request.Query["access_token"];
                var path = context.HttpContext.Request.Path;

                if (!string.IsNullOrEmpty(accessToken) && path.StartsWithSegments("/hubs"))
                {
                    context.Token = accessToken;
                }

                return Task.CompletedTask;
            },
            OnTokenValidated = async context =>
            {
                var sessionValidationHandler = context.HttpContext.RequestServices
                    .GetRequiredService<WhimLabAI.WebApi.Authentication.JwtSessionValidationHandler>();
                await sessionValidationHandler.OnTokenValidated(context);
            },
            OnAuthenticationFailed = async context =>
            {
                var sessionValidationHandler = context.HttpContext.RequestServices
                    .GetRequiredService<WhimLabAI.WebApi.Authentication.JwtSessionValidationHandler>();
                await sessionValidationHandler.OnAuthenticationFailed(context);
            },
            OnChallenge = async context =>
            {
                // Prevent the default behavior of JWT Bearer which returns empty body
                context.HandleResponse();

                var sessionValidationHandler = context.HttpContext.RequestServices
                    .GetRequiredService<WhimLabAI.WebApi.Authentication.JwtSessionValidationHandler>();
                await sessionValidationHandler.OnChallenge(context);

                // Write custom error response
                context.Response.StatusCode = 401;
                context.Response.ContentType = "application/json";

                var error = new
                {
                    success = false,
                    message = context.ErrorDescription ?? "未授权访问",
                    errorCode = "UNAUTHORIZED"
                };

                await context.Response.WriteAsync(System.Text.Json.JsonSerializer.Serialize(error));
            }
        };
    })
    .AddJwtBearer("AdminJwt", options =>
    {
        options.RequireHttpsMetadata = !builder.Environment.IsDevelopment();
        options.SaveToken = true;
        options.TokenValidationParameters = new TokenValidationParameters
        {
            ValidateIssuerSigningKey = true,
            IssuerSigningKey = new SymmetricSecurityKey(key),
            ValidateIssuer = true,
            ValidIssuer = jwtSettings["Issuer"],
            ValidateAudience = true,
            ValidAudience = jwtSettings["Audience"],
            ValidateLifetime = true,
            ClockSkew = TimeSpan.Zero,
            RoleClaimType = "role" // Ensure role claims are recognized
        };

        options.Events = new JwtBearerEvents
        {
            OnTokenValidated = async context =>
            {
                var sessionValidationHandler = context.HttpContext.RequestServices
                    .GetRequiredService<WhimLabAI.WebApi.Authentication.JwtSessionValidationHandler>();
                await sessionValidationHandler.OnTokenValidated(context);
            },
            OnAuthenticationFailed = async context =>
            {
                var sessionValidationHandler = context.HttpContext.RequestServices
                    .GetRequiredService<WhimLabAI.WebApi.Authentication.JwtSessionValidationHandler>();
                await sessionValidationHandler.OnAuthenticationFailed(context);
            },
            OnChallenge = async context =>
            {
                // Prevent the default behavior of JWT Bearer which returns empty body
                context.HandleResponse();

                var sessionValidationHandler = context.HttpContext.RequestServices
                    .GetRequiredService<WhimLabAI.WebApi.Authentication.JwtSessionValidationHandler>();
                await sessionValidationHandler.OnChallenge(context);

                // Write custom error response
                context.Response.StatusCode = 401;
                context.Response.ContentType = "application/json";

                var error = new
                {
                    success = false,
                    message = context.ErrorDescription ?? "未授权访问",
                    errorCode = "UNAUTHORIZED"
                };

                await context.Response.WriteAsync(System.Text.Json.JsonSerializer.Serialize(error));
            }
        };
    });

    builder.Services.AddAuthorization(options =>
    {
        // Add Admin policy
        options.AddPolicy("Admin", policy =>
        {
            policy.RequireAuthenticatedUser();
            policy.RequireRole("Admin", "SuperAdmin");
        });

        // Add SuperAdmin policy
        options.AddPolicy("SuperAdmin", policy =>
        {
            policy.RequireAuthenticatedUser();
            policy.RequireRole("SuperAdmin");
        });

        // Add Customer policy
        options.AddPolicy("Customer", policy =>
        {
            policy.RequireAuthenticatedUser();
            policy.RequireClaim("UserType", "Customer");
        });
    });

    // 添加权限授权处理器
    builder.Services.AddSingleton<IAuthorizationPolicyProvider, WhimLabAI.WebApi.Authorization.PermissionPolicyProvider>();
    builder.Services.AddScoped<IAuthorizationHandler, WhimLabAI.WebApi.Authorization.PermissionAuthorizationHandler>();

    // 注册JWT会话验证处理器
    builder.Services.AddScoped<WhimLabAI.WebApi.Authentication.JwtSessionValidationHandler>();

    Log.Information("All services registered, building application...");

    // Test if we can access configuration before building
    // var testConfig = builder.Configuration.GetConnectionString("DefaultConnection");
    // Log.Information("Configuration test passed: {ConnectionString}", testConfig?.Replace("Password=", "Password=***"));

    WebApplication app;
    try
    {
        Log.Information("Calling builder.Build()...");
        app = builder.Build();
        Log.Information("Application built successfully");
    }
    catch (Exception ex)
    {
        Log.Fatal(ex, "Failed to build application");
        throw;
    }

    // Configure the HTTP request pipeline.
    Log.Information("Configuring middleware pipeline...");
    app.UseSerilogRequestLogging();
    Log.Information("Serilog request logging configured");

    // Use security headers middleware - should be early in the pipeline
    app.UseSecurityHeaders();
    Log.Information("Security headers configured");

    // Use global exception handler
    app.UseGlobalExceptionHandler();

    // Add HTTPS redirection
    app.UseHttpsRedirection();

    // 启用 CORS - 必须在认证和路由之前
    if (app.Environment.IsDevelopment())
    {
        app.UseCors("AllowFrontends");
    }
    else
    {
        app.UseCors("AllowFrontends");
    }

    // Enable Swagger in Development
    if (app.Environment.IsDevelopment())
    {
        app.UseSwagger();
        app.UseSwaggerUI(c =>
        {
            c.SwaggerEndpoint("/swagger/v1/swagger.json", "WhimLabAI API v1");
            c.RoutePrefix = "swagger";
        });

        // Redirect root to Swagger
        app.MapGet("/", () => Results.Redirect("/swagger")).ExcludeFromDescription();
    }

    // 使用安全中间件
    WhimLabAI.Infrastructure.Security.SecurityExtensions.UseSecurityMiddleware(app, builder.Configuration);

    // 使用速率限制
    app.UseRateLimiter();

    app.UseResponseCompression();   // ① 先压缩响应

    app.UseETags();                 // ② 再生成 ETag，基于压缩后响应体生成

    app.UseResponseCaching();       // ③ 缓存压缩后响应（不建议和 OutputCache 同时使用）

    app.UseOutputCache();           // ④ 或者使用现代输出缓存（建议用这个，替代上面两项）

    // 配置静态文件服务（用于提供上传的文件）
    var uploadsPath = Path.Combine(builder.Environment.ContentRootPath, "wwwroot", "uploads");

    // 确保上传目录存在
    if (!Directory.Exists(uploadsPath))
    {
        Directory.CreateDirectory(uploadsPath);
        Log.Information("Created uploads directory at: {UploadsPath}", uploadsPath);
    }

    app.UseStaticFiles(new StaticFileOptions
    {
        FileProvider = new Microsoft.Extensions.FileProviders.PhysicalFileProvider(uploadsPath),
        RequestPath = "/uploads"
    });

    // 使用API安全中间件
    app.UseApiSecurity();

    // 配置 Hangfire Dashboard - only in non-container environment or after initialization
    if (app.Environment.IsDevelopment() || Environment.GetEnvironmentVariable("DOTNET_RUNNING_IN_CONTAINER") != "true")
    {
        try
        {
            var connectionString = app.Configuration.GetConnectionString("DefaultConnection");

            // Only configure dashboard if we have a valid connection string
            if (!string.IsNullOrEmpty(connectionString) && connectionString != "REPLACE_WITH_YOUR_CONNECTION_STRING")
            {
                app.UseHangfireDashboard("/hangfire", new DashboardOptions
                {
                    DashboardTitle = "WhimLabAI Background Jobs",
                    Authorization = new[] { new WhimLabAI.WebApi.Authorization.HangfireAuthorizationFilter() },
                    DisplayStorageConnectionString = false
                });
            }
            else
            {
                Log.Warning("Hangfire Dashboard not configured due to missing or invalid database connection string");
            }
        }
        catch (Exception ex)
        {
            Log.Warning(ex, "Failed to configure Hangfire Dashboard");
        }
    }

    app.UseAuthentication();

    // 使用会话活动跟踪中间件
    app.UseMiddleware<WhimLabAI.WebApi.Middleware.SessionActivityMiddleware>();

    app.UseAuthorization();

    // 使用支付签名验证中间件（必须在支付安全中间件之前）
    app.UsePaymentCallbackSignatureVerification();

    // 使用支付安全中间件
    app.UsePaymentSecurity();

    // 使用审计日志中间件
    app.UseMiddleware<WhimLabAI.Infrastructure.Auditing.AuditLogMiddleware>();

    // 使用领域事件分发中间件
    app.UseMiddleware<WhimLabAI.Infrastructure.Middleware.DomainEventDispatcherMiddleware>();

    // 使用指标收集中间件
    app.UseMiddleware<WhimLabAI.WebApi.Middleware.MetricsMiddleware>();

    // 配置Prometheus端点
    app.UseHttpMetrics(); // 自动收集HTTP指标

    app.MapControllers();

    // 映射Prometheus指标端点
    app.MapMetrics();

    // 映射 SignalR Hub
    app.MapHub<WhimLabAI.WebApi.Hubs.ConversationHub>("/hubs/conversation");
    app.MapHub<WhimLabAI.WebApi.Hubs.NotificationHub>("/hubs/notification");
    app.MapHub<WhimLabAI.WebApi.Hubs.QRCodeHub>("/hubs/qrcode");

    // 健康检查端点
    app.MapHealthChecks("/health", new Microsoft.AspNetCore.Diagnostics.HealthChecks.HealthCheckOptions
    {
        ResponseWriter = WhimLabAI.WebApi.HealthChecks.HealthCheckResponseWriter.WriteResponse,
        AllowCachingResponses = false
    });

    app.MapHealthChecks("/health/ready", new Microsoft.AspNetCore.Diagnostics.HealthChecks.HealthCheckOptions
    {
        Predicate = check => check.Tags.Contains("db") || check.Tags.Contains("cache") || check.Tags.Contains("messaging") || check.Tags.Contains("storage"),
        ResponseWriter = WhimLabAI.WebApi.HealthChecks.HealthCheckResponseWriter.WriteResponse,
        AllowCachingResponses = false
    });

    app.MapHealthChecks("/health/live", new Microsoft.AspNetCore.Diagnostics.HealthChecks.HealthCheckOptions
    {
        Predicate = _ => false,
        ResponseWriter = WhimLabAI.WebApi.HealthChecks.HealthCheckResponseWriter.WriteResponse,
        AllowCachingResponses = false
    });

    // 初始化数据库
    // Temporarily disabled due to seeding issues
    if (app.Environment.IsDevelopment())
    {
        await app.MigrateAndSeedAsync();
    }

    // Validate JWT configuration on startup
    var startupLogger = app.Services.GetRequiredService<ILogger<Program>>();
    var jwtConfig = app.Configuration.GetSection("Jwt");
    var jwtSecretKey = jwtConfig["SecretKey"];

    if (!WhimLabAI.WebApi.Configuration.JwtSecurityKeyGenerator.IsSecureKey(jwtSecretKey))
    {
        if (app.Environment.IsProduction())
        {
            startupLogger.LogError("JWT secret key does not meet security requirements for production environment");
            throw new InvalidOperationException("JWT configuration is not secure for production");
        }
        else
        {
            startupLogger.LogWarning("JWT secret key does not meet security requirements. This is acceptable for development only.");
        }
    }

    // 配置 Hangfire 定时任务
    try
    {
        var connectionString = app.Configuration.GetConnectionString("DefaultConnection");

        // Only configure recurring jobs if we have a valid connection string
        if (!string.IsNullOrEmpty(connectionString) && connectionString != "REPLACE_WITH_YOUR_CONNECTION_STRING")
        {
            var recurringJobManager = app.Services.GetRequiredService<IRecurringJobManager>();

            // 订阅自动续费任务 - 每天凌晨2点执行
            recurringJobManager.AddOrUpdate<WhimLabAI.Application.BackgroundJobs.SubscriptionRenewalJob>(
                "subscription-renewal",
                job => job.ProcessAutoRenewalsAsync(CancellationToken.None),
                "0 2 * * *", // CRON表达式：每天凌晨2点
                new RecurringJobOptions
                {
                    TimeZone = TimeZoneInfo.FindSystemTimeZoneById("China Standard Time")
                });

            // 配额月度重置任务 - 每月1日凌晨0点执行
            recurringJobManager.AddOrUpdate<WhimLabAI.Application.BackgroundJobs.QuotaResetJob>(
                "quota-monthly-reset",
                job => job.ResetMonthlyQuotasAsync(CancellationToken.None),
                "0 0 1 * *", // CRON表达式：每月1日凌晨0点
                new RecurringJobOptions
                {
                    TimeZone = TimeZoneInfo.FindSystemTimeZoneById("China Standard Time")
                });

            // 支付超时检查任务 - 每5分钟执行一次
            recurringJobManager.AddOrUpdate<WhimLabAI.Application.BackgroundJobs.PaymentTimeoutJob>(
                "payment-timeout-check",
                job => job.CheckPaymentTimeoutsAsync(CancellationToken.None),
                "*/5 * * * *", // CRON表达式：每5分钟
                new RecurringJobOptions
                {
                });

            // 清理过期数据任务 - 每天凌晨3点执行
            recurringJobManager.AddOrUpdate<WhimLabAI.Application.BackgroundJobs.DataCleanupJob>(
                "data-cleanup",
                job => job.CleanupExpiredDataAsync(CancellationToken.None),
                "0 3 * * *", // CRON表达式：每天凌晨3点
                new RecurringJobOptions
                {
                    TimeZone = TimeZoneInfo.FindSystemTimeZoneById("China Standard Time")
                });

            // 账号注销相关任务
            WhimLabAI.Application.BackgroundJobs.AccountDeletionJob.Configure();

            Log.Information("Hangfire recurring jobs configured");
        }
        else
        {
            Log.Warning("Hangfire recurring jobs not configured due to missing or invalid database connection string");
        }
    }
    catch (Exception ex)
    {
        Log.Warning(ex, "Failed to configure Hangfire recurring jobs");
    }

    Log.Information("Starting application...");

    app.Run();
}
catch (Exception ex)
{
    Log.Fatal(ex, "Application terminated unexpectedly");
}
finally
{
    Log.CloseAndFlush();
}

// 使Program类对集成测试可见
public partial class Program { }
