{"Logging": {"LogLevel": {"Default": "Warning", "Microsoft": "Warning", "Microsoft.AspNetCore": "Warning", "Microsoft.EntityFrameworkCore": "Warning", "System": "Warning"}}, "Development": {"DisableCaptcha": true, "DisableRateLimiting": true, "UseMockPayment": true, "EnableDetailedErrors": true, "AutoSeedData": true, "SkipDatabaseInitialization": true}, "ConnectionStrings": {"DefaultConnection": "Host=${POSTGRES_HOST:localhost};Port=${POSTGRES_PORT:5432};Database=${POSTGRES_DB:whimlabai_dev};Username=${POSTGRES_USER:postgres};Password=${POSTGRES_PASSWORD:postgres123};Pooling=true;MinPoolSize=0;MaxPoolSize=20;ConnectionLifetime=300;CommandTimeout=30;Timeout=30", "Redis": "${REDIS_HOST:localhost}:${REDIS_PORT:6379},password=${REDIS_PASSWORD:redis123},defaultDatabase=0,abortConnect=false,connectTimeout=5000,syncTimeout=5000,asyncTimeout=5000,keepAlive=30,connectRetry=3"}, "Jwt": {"ExpirationMinutes": 10080, "RefreshExpirationDays": 30}, "Verification": {"AdminCaptchaPolicy": {"Enabled": false, "AlwaysRequired": false, "DisableInDevelopment": true}, "CustomerCaptchaPolicy": {"Enabled": false, "AlwaysRequired": false, "FailedAttemptsThreshold": 999, "HourlyAttemptsThreshold": 999, "DisableInDevelopment": true}}, "Payment": {"UseMockProvider": true, "Security": {"EnableIpWhitelist": false, "EnableSignatureValidation": false}}, "RateLimiting": {"DisableAllLimits": true}, "RabbitMQ": {"Host": "${RABBITMQ_HOST:localhost}", "Port": 5672, "Username": "${RABBITMQ_USER:whimlab}", "Password": "${RABBITMQ_PASS:rabbitmq123}", "VirtualHost": "/"}}