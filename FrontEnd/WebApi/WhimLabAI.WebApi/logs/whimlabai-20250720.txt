2025-07-20 20:47:56.698 +08:00 [INF] Starting WhimLabAI Web API
2025-07-20 20:47:57.040 +08:00 [INF] WebApplication builder created
2025-07-20 20:47:57.040 +08:00 [INF] Loading configuration files...
2025-07-20 20:47:57.041 +08:00 [INF] Environment variables loaded
2025-07-20 20:47:57.052 +08:00 [INF] Environment variable replacements loaded
2025-07-20 20:47:57.052 +08:00 [INF] Configuration loaded
2025-07-20 20:47:57.066 +08:00 [INF] Registering Infrastructure services...
2025-07-20 20:47:57.404 +08:00 [INF] Infrastructure services registered
2025-07-20 20:47:57.404 +08:00 [INF] Registering Application services...
2025-07-20 20:47:57.407 +08:00 [INF] Application services registered
2025-07-20 20:47:57.407 +08:00 [INF] Registering RealtimeNotificationService...
2025-07-20 20:47:57.407 +08:00 [INF] RealtimeNotificationService registered
2025-07-20 20:47:57.407 +08:00 [INF] Registering security services...
2025-07-20 20:47:57.409 +08:00 [INF] Security services registered
2025-07-20 20:47:57.409 +08:00 [INF] Configuring API versioning...
2025-07-20 20:47:57.410 +08:00 [INF] API versioning configured
2025-07-20 20:47:57.410 +08:00 [INF] Configuring input validation...
2025-07-20 20:47:57.410 +08:00 [INF] Input validation configured
2025-07-20 20:47:57.410 +08:00 [INF] Configuring rate limiting...
2025-07-20 20:47:57.410 +08:00 [INF] Rate limiting configured
2025-07-20 20:47:57.417 +08:00 [INF] All services registered, building application...
2025-07-20 20:47:57.417 +08:00 [INF] Calling builder.Build()...
2025-07-20 22:58:23.981 +08:00 [INF] Starting WhimLabAI Web API
2025-07-20 22:58:24.298 +08:00 [INF] WebApplication builder created
2025-07-20 22:58:24.298 +08:00 [INF] Loading configuration files...
2025-07-20 22:58:24.299 +08:00 [INF] Environment variables loaded
2025-07-20 22:58:24.309 +08:00 [INF] Environment variable replacements loaded
2025-07-20 22:58:24.309 +08:00 [INF] Configuration loaded
2025-07-20 22:58:24.314 +08:00 [INF] Registering Infrastructure services...
2025-07-20 22:58:24.595 +08:00 [INF] Infrastructure services registered
2025-07-20 22:58:24.595 +08:00 [INF] Registering Application services...
2025-07-20 22:58:24.597 +08:00 [INF] Application services registered
2025-07-20 22:58:24.597 +08:00 [INF] Registering RealtimeNotificationService...
2025-07-20 22:58:24.597 +08:00 [INF] RealtimeNotificationService registered
2025-07-20 22:58:24.597 +08:00 [INF] Registering security services...
2025-07-20 22:58:24.598 +08:00 [INF] Security services registered
2025-07-20 22:58:24.598 +08:00 [INF] Configuring API versioning...
2025-07-20 22:58:24.598 +08:00 [INF] API versioning configured
2025-07-20 22:58:24.598 +08:00 [INF] Configuring input validation...
2025-07-20 22:58:24.598 +08:00 [INF] Input validation configured
2025-07-20 22:58:24.598 +08:00 [INF] Configuring rate limiting...
2025-07-20 22:58:24.598 +08:00 [INF] Rate limiting configured
2025-07-20 22:58:24.602 +08:00 [INF] All services registered, building application...
2025-07-20 22:58:24.602 +08:00 [INF] Calling builder.Build()...
2025-07-20 23:01:34.412 +08:00 [INF] Starting WhimLabAI Web API
2025-07-20 23:01:34.737 +08:00 [INF] WebApplication builder created
2025-07-20 23:01:34.737 +08:00 [INF] Loading configuration files...
2025-07-20 23:01:34.737 +08:00 [INF] Environment variables loaded
2025-07-20 23:01:34.748 +08:00 [INF] Environment variable replacements loaded
2025-07-20 23:01:34.748 +08:00 [INF] Configuration loaded
2025-07-20 23:01:34.753 +08:00 [INF] Registering Infrastructure services...
2025-07-20 23:01:35.026 +08:00 [INF] Infrastructure services registered
2025-07-20 23:01:35.026 +08:00 [INF] Registering Application services...
2025-07-20 23:01:35.028 +08:00 [INF] Application services registered
2025-07-20 23:01:35.028 +08:00 [INF] Registering RealtimeNotificationService...
2025-07-20 23:01:35.028 +08:00 [INF] RealtimeNotificationService registered
2025-07-20 23:01:35.028 +08:00 [INF] Registering security services...
2025-07-20 23:01:35.029 +08:00 [INF] Security services registered
2025-07-20 23:01:35.029 +08:00 [INF] Configuring API versioning...
2025-07-20 23:01:35.029 +08:00 [INF] API versioning configured
2025-07-20 23:01:35.029 +08:00 [INF] Configuring input validation...
2025-07-20 23:01:35.029 +08:00 [INF] Input validation configured
2025-07-20 23:01:35.029 +08:00 [INF] Configuring rate limiting...
2025-07-20 23:01:35.029 +08:00 [INF] Rate limiting configured
2025-07-20 23:01:35.033 +08:00 [INF] All services registered, building application...
2025-07-20 23:01:35.033 +08:00 [INF] Calling builder.Build()...
2025-07-20 23:04:09.895 +08:00 [INF] Starting WhimLabAI Web API
2025-07-20 23:04:10.229 +08:00 [INF] WebApplication builder created
2025-07-20 23:04:10.229 +08:00 [INF] Loading configuration files...
2025-07-20 23:04:10.229 +08:00 [INF] Environment variables loaded
2025-07-20 23:04:10.241 +08:00 [INF] Environment variable replacements loaded
2025-07-20 23:04:10.241 +08:00 [INF] Configuration loaded
2025-07-20 23:04:10.249 +08:00 [INF] Registering Infrastructure services...
2025-07-20 23:04:10.532 +08:00 [INF] Infrastructure services registered
2025-07-20 23:04:10.532 +08:00 [INF] Registering Application services...
2025-07-20 23:04:10.534 +08:00 [INF] Application services registered
2025-07-20 23:04:10.534 +08:00 [INF] Registering RealtimeNotificationService...
2025-07-20 23:04:10.534 +08:00 [INF] RealtimeNotificationService registered
2025-07-20 23:04:10.534 +08:00 [INF] Registering security services...
2025-07-20 23:04:10.535 +08:00 [INF] Security services registered
2025-07-20 23:04:10.535 +08:00 [INF] Configuring API versioning...
2025-07-20 23:04:10.535 +08:00 [INF] API versioning configured
2025-07-20 23:04:10.535 +08:00 [INF] Configuring input validation...
2025-07-20 23:04:10.535 +08:00 [INF] Input validation configured
2025-07-20 23:04:10.535 +08:00 [INF] Configuring rate limiting...
2025-07-20 23:04:10.536 +08:00 [INF] Rate limiting configured
2025-07-20 23:04:10.539 +08:00 [INF] All services registered, building application...
2025-07-20 23:04:10.539 +08:00 [INF] Calling builder.Build()...
