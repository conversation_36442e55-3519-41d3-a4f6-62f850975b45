2025-07-21 00:01:34.049 +08:00 [INF] Starting WhimLabAI Web API
2025-07-21 00:01:34.369 +08:00 [INF] WebApplication builder created
2025-07-21 00:01:34.370 +08:00 [INF] Loading configuration files...
2025-07-21 00:01:34.370 +08:00 [INF] Environment variables loaded
2025-07-21 00:01:34.382 +08:00 [INF] Environment variable replacements loaded
2025-07-21 00:01:34.382 +08:00 [INF] Configuration loaded
2025-07-21 00:01:34.389 +08:00 [INF] Registering Infrastructure services...
2025-07-21 00:01:34.678 +08:00 [INF] Infrastructure services registered
2025-07-21 00:01:34.678 +08:00 [INF] Registering Application services...
2025-07-21 00:01:34.681 +08:00 [INF] Application services registered
2025-07-21 00:01:34.681 +08:00 [INF] Registering RealtimeNotificationService...
2025-07-21 00:01:34.681 +08:00 [INF] RealtimeNotificationService registered
2025-07-21 00:01:34.681 +08:00 [INF] Registering security services...
2025-07-21 00:01:34.682 +08:00 [INF] Security services registered
2025-07-21 00:01:34.682 +08:00 [INF] Configuring API versioning...
2025-07-21 00:01:34.682 +08:00 [INF] API versioning configured
2025-07-21 00:01:34.682 +08:00 [INF] Configuring input validation...
2025-07-21 00:01:34.683 +08:00 [INF] Input validation configured
2025-07-21 00:01:34.683 +08:00 [INF] Configuring rate limiting...
2025-07-21 00:01:34.683 +08:00 [INF] Rate limiting configured
2025-07-21 00:01:34.687 +08:00 [INF] All services registered, building application...
2025-07-21 00:01:34.687 +08:00 [INF] Calling builder.Build()...
2025-07-21 00:02:39.920 +08:00 [INF] Starting WhimLabAI Web API
2025-07-21 00:02:40.240 +08:00 [INF] WebApplication builder created
2025-07-21 00:02:40.240 +08:00 [INF] Loading configuration files...
2025-07-21 00:02:40.241 +08:00 [INF] Environment variables loaded
2025-07-21 00:02:40.252 +08:00 [INF] Environment variable replacements loaded
2025-07-21 00:02:40.252 +08:00 [INF] Configuration loaded
2025-07-21 00:02:40.258 +08:00 [INF] Registering Infrastructure services...
2025-07-21 00:02:40.535 +08:00 [INF] Infrastructure services registered
2025-07-21 00:02:40.536 +08:00 [INF] Registering Application services...
2025-07-21 00:02:40.537 +08:00 [INF] Application services registered
2025-07-21 00:02:40.537 +08:00 [INF] Registering RealtimeNotificationService...
2025-07-21 00:02:40.538 +08:00 [INF] RealtimeNotificationService registered
2025-07-21 00:02:40.538 +08:00 [INF] Registering security services...
2025-07-21 00:02:40.538 +08:00 [INF] Security services registered
2025-07-21 00:02:40.539 +08:00 [INF] Configuring API versioning...
2025-07-21 00:02:40.539 +08:00 [INF] API versioning configured
2025-07-21 00:02:40.539 +08:00 [INF] Configuring input validation...
2025-07-21 00:02:40.539 +08:00 [INF] Input validation configured
2025-07-21 00:02:40.539 +08:00 [INF] Configuring rate limiting...
2025-07-21 00:02:40.539 +08:00 [INF] Rate limiting configured
2025-07-21 00:02:40.543 +08:00 [INF] All services registered, building application...
2025-07-21 00:02:40.543 +08:00 [INF] Calling builder.Build()...
2025-07-21 00:08:30.866 +08:00 [INF] Starting WhimLabAI Web API
2025-07-21 00:08:31.174 +08:00 [INF] WebApplication builder created
2025-07-21 00:08:31.175 +08:00 [INF] Loading configuration files...
2025-07-21 00:08:31.175 +08:00 [INF] Environment variables loaded
2025-07-21 00:08:31.185 +08:00 [INF] Environment variable replacements loaded
2025-07-21 00:08:31.185 +08:00 [INF] Configuration loaded
2025-07-21 00:08:31.190 +08:00 [INF] Registering Infrastructure services...
2025-07-21 00:08:31.452 +08:00 [INF] Infrastructure services registered
2025-07-21 00:08:31.452 +08:00 [INF] Registering Application services...
2025-07-21 00:08:31.454 +08:00 [INF] Application services registered
2025-07-21 00:08:31.454 +08:00 [INF] Registering RealtimeNotificationService...
2025-07-21 00:08:31.454 +08:00 [INF] RealtimeNotificationService registered
2025-07-21 00:08:31.454 +08:00 [INF] Registering security services...
2025-07-21 00:08:31.455 +08:00 [INF] Security services registered
2025-07-21 00:08:31.455 +08:00 [INF] Configuring API versioning...
2025-07-21 00:08:31.455 +08:00 [INF] API versioning configured
2025-07-21 00:08:31.455 +08:00 [INF] Configuring input validation...
2025-07-21 00:08:31.455 +08:00 [INF] Input validation configured
2025-07-21 00:08:31.455 +08:00 [INF] Configuring rate limiting...
2025-07-21 00:08:31.456 +08:00 [INF] Rate limiting configured
2025-07-21 00:08:31.459 +08:00 [INF] All services registered, building application...
2025-07-21 00:08:31.459 +08:00 [INF] Calling builder.Build()...
2025-07-21 00:12:53.543 +08:00 [INF] Starting WhimLabAI Web API
2025-07-21 00:12:53.876 +08:00 [INF] WebApplication builder created
2025-07-21 00:12:53.876 +08:00 [INF] Loading configuration files...
2025-07-21 00:12:53.876 +08:00 [INF] Environment variables loaded
2025-07-21 00:12:53.887 +08:00 [INF] Environment variable replacements loaded
2025-07-21 00:12:53.887 +08:00 [INF] Configuration loaded
2025-07-21 00:12:53.891 +08:00 [INF] Registering Infrastructure services...
2025-07-21 00:12:54.104 +08:00 [INF] Infrastructure services registered
2025-07-21 00:12:54.104 +08:00 [INF] Registering Application services...
2025-07-21 00:12:54.106 +08:00 [INF] Application services registered
2025-07-21 00:12:54.106 +08:00 [INF] Registering RealtimeNotificationService...
2025-07-21 00:12:54.106 +08:00 [INF] RealtimeNotificationService registered
2025-07-21 00:12:54.106 +08:00 [INF] Registering security services...
2025-07-21 00:12:54.107 +08:00 [INF] Security services registered
2025-07-21 00:12:54.107 +08:00 [INF] Configuring API versioning...
2025-07-21 00:12:54.107 +08:00 [INF] API versioning configured
2025-07-21 00:12:54.107 +08:00 [INF] Configuring input validation...
2025-07-21 00:12:54.107 +08:00 [INF] Input validation configured
2025-07-21 00:12:54.107 +08:00 [INF] Configuring rate limiting...
2025-07-21 00:12:54.107 +08:00 [INF] Rate limiting configured
2025-07-21 00:12:54.111 +08:00 [INF] All services registered, building application...
2025-07-21 00:12:54.111 +08:00 [INF] Calling builder.Build()...
2025-07-21 00:21:53.264 +08:00 [INF] Starting WhimLabAI Web API
2025-07-21 00:21:53.583 +08:00 [INF] WebApplication builder created
2025-07-21 00:21:53.583 +08:00 [INF] Loading configuration files...
2025-07-21 00:21:53.583 +08:00 [INF] Environment variables loaded
2025-07-21 00:21:53.594 +08:00 [INF] Environment variable replacements loaded
2025-07-21 00:21:53.594 +08:00 [INF] Configuration loaded
2025-07-21 00:21:53.599 +08:00 [INF] Registering Infrastructure services...
2025-07-21 00:21:53.780 +08:00 [INF] Infrastructure services registered
2025-07-21 00:21:53.781 +08:00 [INF] Registering Application services...
2025-07-21 00:21:53.786 +08:00 [INF] Application services registered
2025-07-21 00:21:53.786 +08:00 [INF] Registering RealtimeNotificationService...
2025-07-21 00:21:53.786 +08:00 [INF] RealtimeNotificationService registered
2025-07-21 00:21:53.786 +08:00 [INF] Registering security services...
2025-07-21 00:21:53.787 +08:00 [INF] Security services registered
2025-07-21 00:21:53.787 +08:00 [INF] Configuring API versioning...
2025-07-21 00:21:53.787 +08:00 [INF] API versioning configured
2025-07-21 00:21:53.787 +08:00 [INF] Configuring input validation...
2025-07-21 00:21:53.787 +08:00 [INF] Input validation configured
2025-07-21 00:21:53.787 +08:00 [INF] Configuring rate limiting...
2025-07-21 00:21:53.787 +08:00 [INF] Rate limiting configured
2025-07-21 00:21:53.792 +08:00 [INF] All services registered, building application...
2025-07-21 00:21:53.792 +08:00 [INF] Calling builder.Build()...
2025-07-21 00:21:53.792 +08:00 [INF] About to call builder.Build() - checking for blocking services...
2025-07-21 00:52:47.121 +08:00 [INF] Starting WhimLabAI Web API
2025-07-21 00:52:47.460 +08:00 [INF] WebApplication builder created
2025-07-21 00:52:47.460 +08:00 [INF] Loading configuration files...
2025-07-21 00:52:47.461 +08:00 [INF] Environment variables loaded
2025-07-21 00:52:47.473 +08:00 [INF] Environment variable replacements loaded
2025-07-21 00:52:47.473 +08:00 [INF] Configuration loaded
2025-07-21 00:52:47.479 +08:00 [INF] Skipping Infrastructure services for debugging...
2025-07-21 00:52:47.479 +08:00 [INF] Infrastructure services skipped
2025-07-21 00:52:47.479 +08:00 [INF] Skipping Application services for debugging...
2025-07-21 00:52:47.479 +08:00 [INF] Application services skipped
2025-07-21 00:52:47.479 +08:00 [INF] Skipping RealtimeNotificationService for debugging...
2025-07-21 00:52:47.479 +08:00 [INF] RealtimeNotificationService skipped
2025-07-21 00:52:47.479 +08:00 [INF] Skipping security services for debugging...
2025-07-21 00:52:47.479 +08:00 [INF] Security services skipped
2025-07-21 00:52:47.479 +08:00 [INF] Configuring API versioning...
2025-07-21 00:52:47.479 +08:00 [INF] API versioning configured
2025-07-21 00:52:47.480 +08:00 [INF] Configuring input validation...
2025-07-21 00:52:47.480 +08:00 [INF] Input validation configured
2025-07-21 00:52:47.480 +08:00 [INF] Configuring rate limiting...
2025-07-21 00:52:47.481 +08:00 [INF] Rate limiting configured
2025-07-21 00:52:47.488 +08:00 [INF] All services registered, building application...
2025-07-21 00:52:47.488 +08:00 [INF] Calling builder.Build()...
2025-07-21 00:52:47.488 +08:00 [INF] About to call builder.Build() - checking for blocking services...
2025-07-21 00:52:47.505 +08:00 [FTL] Failed to build application
System.AggregateException: Some services are not able to be constructed (Error while validating the service descriptor 'ServiceType: Microsoft.AspNetCore.Authorization.IAuthorizationService Lifetime: Transient ImplementationType: Microsoft.AspNetCore.Authorization.DefaultAuthorizationService': Unable to resolve service for type 'WhimLabAI.Domain.Repositories.IAdminUserRepository' while attempting to activate 'WhimLabAI.WebApi.Authorization.PermissionAuthorizationHandler'.) (Error while validating the service descriptor 'ServiceType: Microsoft.AspNetCore.Authorization.IAuthorizationHandlerProvider Lifetime: Transient ImplementationType: Microsoft.AspNetCore.Authorization.DefaultAuthorizationHandlerProvider': Unable to resolve service for type 'WhimLabAI.Domain.Repositories.IAdminUserRepository' while attempting to activate 'WhimLabAI.WebApi.Authorization.PermissionAuthorizationHandler'.) (Error while validating the service descriptor 'ServiceType: Microsoft.AspNetCore.Authorization.Policy.IPolicyEvaluator Lifetime: Transient ImplementationType: Microsoft.AspNetCore.Authorization.Policy.PolicyEvaluator': Unable to resolve service for type 'WhimLabAI.Domain.Repositories.IAdminUserRepository' while attempting to activate 'WhimLabAI.WebApi.Authorization.PermissionAuthorizationHandler'.) (Error while validating the service descriptor 'ServiceType: WhimLabAI.Infrastructure.ApiSecurity.IRateLimitingService Lifetime: Singleton ImplementationType: WhimLabAI.Infrastructure.ApiSecurity.RateLimitingService': Unable to resolve service for type 'Microsoft.Extensions.Caching.Distributed.IDistributedCache' while attempting to activate 'WhimLabAI.Infrastructure.ApiSecurity.RateLimitingService'.) (Error while validating the service descriptor 'ServiceType: WhimLabAI.Infrastructure.ApiSecurity.IApiKeyManagementService Lifetime: Scoped ImplementationType: WhimLabAI.Infrastructure.ApiSecurity.ApiKeyManagementService': Unable to resolve service for type 'WhimLabAI.Infrastructure.Data.WhimLabAIDbContext' while attempting to activate 'WhimLabAI.Infrastructure.ApiSecurity.ApiKeyManagementService'.) (Error while validating the service descriptor 'ServiceType: WhimLabAI.Infrastructure.ApiSecurity.ApiKeyAuthenticationHandler Lifetime: Transient ImplementationType: WhimLabAI.Infrastructure.ApiSecurity.ApiKeyAuthenticationHandler': Unable to resolve service for type 'WhimLabAI.Infrastructure.Data.WhimLabAIDbContext' while attempting to activate 'WhimLabAI.Infrastructure.ApiSecurity.ApiKeyAuthenticationHandler'.) (Error while validating the service descriptor 'ServiceType: Microsoft.AspNetCore.Authorization.IAuthorizationHandler Lifetime: Scoped ImplementationType: WhimLabAI.WebApi.Authorization.PermissionAuthorizationHandler': Unable to resolve service for type 'WhimLabAI.Domain.Repositories.IAdminUserRepository' while attempting to activate 'WhimLabAI.WebApi.Authorization.PermissionAuthorizationHandler'.)
 ---> System.InvalidOperationException: Error while validating the service descriptor 'ServiceType: Microsoft.AspNetCore.Authorization.IAuthorizationService Lifetime: Transient ImplementationType: Microsoft.AspNetCore.Authorization.DefaultAuthorizationService': Unable to resolve service for type 'WhimLabAI.Domain.Repositories.IAdminUserRepository' while attempting to activate 'WhimLabAI.WebApi.Authorization.PermissionAuthorizationHandler'.
 ---> System.InvalidOperationException: Unable to resolve service for type 'WhimLabAI.Domain.Repositories.IAdminUserRepository' while attempting to activate 'WhimLabAI.WebApi.Authorization.PermissionAuthorizationHandler'.
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteFactory.CreateArgumentCallSites(ServiceIdentifier serviceIdentifier, Type implementationType, CallSiteChain callSiteChain, ParameterInfo[] parameters, Boolean throwIfCallSiteNotFound)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteFactory.CreateConstructorCallSite(ResultCache lifetime, ServiceIdentifier serviceIdentifier, Type implementationType, CallSiteChain callSiteChain)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteFactory.TryCreateExact(ServiceDescriptor descriptor, ServiceIdentifier serviceIdentifier, CallSiteChain callSiteChain, Int32 slot)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteFactory.TryCreateEnumerable(ServiceIdentifier serviceIdentifier, CallSiteChain callSiteChain)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteFactory.CreateCallSite(ServiceIdentifier serviceIdentifier, CallSiteChain callSiteChain)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteFactory.CreateArgumentCallSites(ServiceIdentifier serviceIdentifier, Type implementationType, CallSiteChain callSiteChain, ParameterInfo[] parameters, Boolean throwIfCallSiteNotFound)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteFactory.CreateConstructorCallSite(ResultCache lifetime, ServiceIdentifier serviceIdentifier, Type implementationType, CallSiteChain callSiteChain)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteFactory.TryCreateExact(ServiceDescriptor descriptor, ServiceIdentifier serviceIdentifier, CallSiteChain callSiteChain, Int32 slot)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteFactory.TryCreateExact(ServiceIdentifier serviceIdentifier, CallSiteChain callSiteChain)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteFactory.CreateCallSite(ServiceIdentifier serviceIdentifier, CallSiteChain callSiteChain)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteFactory.CreateArgumentCallSites(ServiceIdentifier serviceIdentifier, Type implementationType, CallSiteChain callSiteChain, ParameterInfo[] parameters, Boolean throwIfCallSiteNotFound)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteFactory.CreateConstructorCallSite(ResultCache lifetime, ServiceIdentifier serviceIdentifier, Type implementationType, CallSiteChain callSiteChain)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteFactory.TryCreateExact(ServiceDescriptor descriptor, ServiceIdentifier serviceIdentifier, CallSiteChain callSiteChain, Int32 slot)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteFactory.GetCallSite(ServiceDescriptor serviceDescriptor, CallSiteChain callSiteChain)
   at Microsoft.Extensions.DependencyInjection.ServiceProvider.ValidateService(ServiceDescriptor descriptor)
   --- End of inner exception stack trace ---
   at Microsoft.Extensions.DependencyInjection.ServiceProvider.ValidateService(ServiceDescriptor descriptor)
   at Microsoft.Extensions.DependencyInjection.ServiceProvider..ctor(ICollection`1 serviceDescriptors, ServiceProviderOptions options)
   --- End of inner exception stack trace ---
   at Microsoft.Extensions.DependencyInjection.ServiceProvider..ctor(ICollection`1 serviceDescriptors, ServiceProviderOptions options)
   at Microsoft.Extensions.DependencyInjection.ServiceCollectionContainerBuilderExtensions.BuildServiceProvider(IServiceCollection services, ServiceProviderOptions options)
   at Microsoft.Extensions.Hosting.HostApplicationBuilder.Build()
   at Microsoft.AspNetCore.Builder.WebApplicationBuilder.Build()
   at Program.<Main>$(String[] args) in /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/WebApi/WhimLabAI.WebApi/Program.cs:line 688
 ---> (Inner Exception #1) System.InvalidOperationException: Error while validating the service descriptor 'ServiceType: Microsoft.AspNetCore.Authorization.IAuthorizationHandlerProvider Lifetime: Transient ImplementationType: Microsoft.AspNetCore.Authorization.DefaultAuthorizationHandlerProvider': Unable to resolve service for type 'WhimLabAI.Domain.Repositories.IAdminUserRepository' while attempting to activate 'WhimLabAI.WebApi.Authorization.PermissionAuthorizationHandler'.
 ---> System.InvalidOperationException: Unable to resolve service for type 'WhimLabAI.Domain.Repositories.IAdminUserRepository' while attempting to activate 'WhimLabAI.WebApi.Authorization.PermissionAuthorizationHandler'.
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteFactory.CreateArgumentCallSites(ServiceIdentifier serviceIdentifier, Type implementationType, CallSiteChain callSiteChain, ParameterInfo[] parameters, Boolean throwIfCallSiteNotFound)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteFactory.CreateConstructorCallSite(ResultCache lifetime, ServiceIdentifier serviceIdentifier, Type implementationType, CallSiteChain callSiteChain)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteFactory.TryCreateExact(ServiceDescriptor descriptor, ServiceIdentifier serviceIdentifier, CallSiteChain callSiteChain, Int32 slot)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteFactory.TryCreateEnumerable(ServiceIdentifier serviceIdentifier, CallSiteChain callSiteChain)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteFactory.CreateCallSite(ServiceIdentifier serviceIdentifier, CallSiteChain callSiteChain)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteFactory.CreateArgumentCallSites(ServiceIdentifier serviceIdentifier, Type implementationType, CallSiteChain callSiteChain, ParameterInfo[] parameters, Boolean throwIfCallSiteNotFound)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteFactory.CreateConstructorCallSite(ResultCache lifetime, ServiceIdentifier serviceIdentifier, Type implementationType, CallSiteChain callSiteChain)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteFactory.TryCreateExact(ServiceDescriptor descriptor, ServiceIdentifier serviceIdentifier, CallSiteChain callSiteChain, Int32 slot)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteFactory.GetCallSite(ServiceDescriptor serviceDescriptor, CallSiteChain callSiteChain)
   at Microsoft.Extensions.DependencyInjection.ServiceProvider.ValidateService(ServiceDescriptor descriptor)
   --- End of inner exception stack trace ---
   at Microsoft.Extensions.DependencyInjection.ServiceProvider.ValidateService(ServiceDescriptor descriptor)
   at Microsoft.Extensions.DependencyInjection.ServiceProvider..ctor(ICollection`1 serviceDescriptors, ServiceProviderOptions options)<---

 ---> (Inner Exception #2) System.InvalidOperationException: Error while validating the service descriptor 'ServiceType: Microsoft.AspNetCore.Authorization.Policy.IPolicyEvaluator Lifetime: Transient ImplementationType: Microsoft.AspNetCore.Authorization.Policy.PolicyEvaluator': Unable to resolve service for type 'WhimLabAI.Domain.Repositories.IAdminUserRepository' while attempting to activate 'WhimLabAI.WebApi.Authorization.PermissionAuthorizationHandler'.
 ---> System.InvalidOperationException: Unable to resolve service for type 'WhimLabAI.Domain.Repositories.IAdminUserRepository' while attempting to activate 'WhimLabAI.WebApi.Authorization.PermissionAuthorizationHandler'.
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteFactory.CreateArgumentCallSites(ServiceIdentifier serviceIdentifier, Type implementationType, CallSiteChain callSiteChain, ParameterInfo[] parameters, Boolean throwIfCallSiteNotFound)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteFactory.CreateConstructorCallSite(ResultCache lifetime, ServiceIdentifier serviceIdentifier, Type implementationType, CallSiteChain callSiteChain)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteFactory.TryCreateExact(ServiceDescriptor descriptor, ServiceIdentifier serviceIdentifier, CallSiteChain callSiteChain, Int32 slot)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteFactory.TryCreateEnumerable(ServiceIdentifier serviceIdentifier, CallSiteChain callSiteChain)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteFactory.CreateCallSite(ServiceIdentifier serviceIdentifier, CallSiteChain callSiteChain)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteFactory.CreateArgumentCallSites(ServiceIdentifier serviceIdentifier, Type implementationType, CallSiteChain callSiteChain, ParameterInfo[] parameters, Boolean throwIfCallSiteNotFound)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteFactory.CreateConstructorCallSite(ResultCache lifetime, ServiceIdentifier serviceIdentifier, Type implementationType, CallSiteChain callSiteChain)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteFactory.TryCreateExact(ServiceDescriptor descriptor, ServiceIdentifier serviceIdentifier, CallSiteChain callSiteChain, Int32 slot)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteFactory.TryCreateExact(ServiceIdentifier serviceIdentifier, CallSiteChain callSiteChain)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteFactory.CreateCallSite(ServiceIdentifier serviceIdentifier, CallSiteChain callSiteChain)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteFactory.CreateArgumentCallSites(ServiceIdentifier serviceIdentifier, Type implementationType, CallSiteChain callSiteChain, ParameterInfo[] parameters, Boolean throwIfCallSiteNotFound)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteFactory.CreateConstructorCallSite(ResultCache lifetime, ServiceIdentifier serviceIdentifier, Type implementationType, CallSiteChain callSiteChain)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteFactory.TryCreateExact(ServiceDescriptor descriptor, ServiceIdentifier serviceIdentifier, CallSiteChain callSiteChain, Int32 slot)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteFactory.TryCreateExact(ServiceIdentifier serviceIdentifier, CallSiteChain callSiteChain)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteFactory.CreateCallSite(ServiceIdentifier serviceIdentifier, CallSiteChain callSiteChain)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteFactory.CreateArgumentCallSites(ServiceIdentifier serviceIdentifier, Type implementationType, CallSiteChain callSiteChain, ParameterInfo[] parameters, Boolean throwIfCallSiteNotFound)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteFactory.CreateConstructorCallSite(ResultCache lifetime, ServiceIdentifier serviceIdentifier, Type implementationType, CallSiteChain callSiteChain)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteFactory.TryCreateExact(ServiceDescriptor descriptor, ServiceIdentifier serviceIdentifier, CallSiteChain callSiteChain, Int32 slot)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteFactory.GetCallSite(ServiceDescriptor serviceDescriptor, CallSiteChain callSiteChain)
   at Microsoft.Extensions.DependencyInjection.ServiceProvider.ValidateService(ServiceDescriptor descriptor)
   --- End of inner exception stack trace ---
   at Microsoft.Extensions.DependencyInjection.ServiceProvider.ValidateService(ServiceDescriptor descriptor)
   at Microsoft.Extensions.DependencyInjection.ServiceProvider..ctor(ICollection`1 serviceDescriptors, ServiceProviderOptions options)<---

 ---> (Inner Exception #3) System.InvalidOperationException: Error while validating the service descriptor 'ServiceType: WhimLabAI.Infrastructure.ApiSecurity.IRateLimitingService Lifetime: Singleton ImplementationType: WhimLabAI.Infrastructure.ApiSecurity.RateLimitingService': Unable to resolve service for type 'Microsoft.Extensions.Caching.Distributed.IDistributedCache' while attempting to activate 'WhimLabAI.Infrastructure.ApiSecurity.RateLimitingService'.
 ---> System.InvalidOperationException: Unable to resolve service for type 'Microsoft.Extensions.Caching.Distributed.IDistributedCache' while attempting to activate 'WhimLabAI.Infrastructure.ApiSecurity.RateLimitingService'.
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteFactory.CreateArgumentCallSites(ServiceIdentifier serviceIdentifier, Type implementationType, CallSiteChain callSiteChain, ParameterInfo[] parameters, Boolean throwIfCallSiteNotFound)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteFactory.CreateConstructorCallSite(ResultCache lifetime, ServiceIdentifier serviceIdentifier, Type implementationType, CallSiteChain callSiteChain)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteFactory.TryCreateExact(ServiceDescriptor descriptor, ServiceIdentifier serviceIdentifier, CallSiteChain callSiteChain, Int32 slot)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteFactory.GetCallSite(ServiceDescriptor serviceDescriptor, CallSiteChain callSiteChain)
   at Microsoft.Extensions.DependencyInjection.ServiceProvider.ValidateService(ServiceDescriptor descriptor)
   --- End of inner exception stack trace ---
   at Microsoft.Extensions.DependencyInjection.ServiceProvider.ValidateService(ServiceDescriptor descriptor)
   at Microsoft.Extensions.DependencyInjection.ServiceProvider..ctor(ICollection`1 serviceDescriptors, ServiceProviderOptions options)<---

 ---> (Inner Exception #4) System.InvalidOperationException: Error while validating the service descriptor 'ServiceType: WhimLabAI.Infrastructure.ApiSecurity.IApiKeyManagementService Lifetime: Scoped ImplementationType: WhimLabAI.Infrastructure.ApiSecurity.ApiKeyManagementService': Unable to resolve service for type 'WhimLabAI.Infrastructure.Data.WhimLabAIDbContext' while attempting to activate 'WhimLabAI.Infrastructure.ApiSecurity.ApiKeyManagementService'.
 ---> System.InvalidOperationException: Unable to resolve service for type 'WhimLabAI.Infrastructure.Data.WhimLabAIDbContext' while attempting to activate 'WhimLabAI.Infrastructure.ApiSecurity.ApiKeyManagementService'.
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteFactory.CreateArgumentCallSites(ServiceIdentifier serviceIdentifier, Type implementationType, CallSiteChain callSiteChain, ParameterInfo[] parameters, Boolean throwIfCallSiteNotFound)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteFactory.CreateConstructorCallSite(ResultCache lifetime, ServiceIdentifier serviceIdentifier, Type implementationType, CallSiteChain callSiteChain)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteFactory.TryCreateExact(ServiceDescriptor descriptor, ServiceIdentifier serviceIdentifier, CallSiteChain callSiteChain, Int32 slot)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteFactory.GetCallSite(ServiceDescriptor serviceDescriptor, CallSiteChain callSiteChain)
   at Microsoft.Extensions.DependencyInjection.ServiceProvider.ValidateService(ServiceDescriptor descriptor)
   --- End of inner exception stack trace ---
   at Microsoft.Extensions.DependencyInjection.ServiceProvider.ValidateService(ServiceDescriptor descriptor)
   at Microsoft.Extensions.DependencyInjection.ServiceProvider..ctor(ICollection`1 serviceDescriptors, ServiceProviderOptions options)<---

 ---> (Inner Exception #5) System.InvalidOperationException: Error while validating the service descriptor 'ServiceType: WhimLabAI.Infrastructure.ApiSecurity.ApiKeyAuthenticationHandler Lifetime: Transient ImplementationType: WhimLabAI.Infrastructure.ApiSecurity.ApiKeyAuthenticationHandler': Unable to resolve service for type 'WhimLabAI.Infrastructure.Data.WhimLabAIDbContext' while attempting to activate 'WhimLabAI.Infrastructure.ApiSecurity.ApiKeyAuthenticationHandler'.
 ---> System.InvalidOperationException: Unable to resolve service for type 'WhimLabAI.Infrastructure.Data.WhimLabAIDbContext' while attempting to activate 'WhimLabAI.Infrastructure.ApiSecurity.ApiKeyAuthenticationHandler'.
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteFactory.CreateArgumentCallSites(ServiceIdentifier serviceIdentifier, Type implementationType, CallSiteChain callSiteChain, ParameterInfo[] parameters, Boolean throwIfCallSiteNotFound)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteFactory.CreateConstructorCallSite(ResultCache lifetime, ServiceIdentifier serviceIdentifier, Type implementationType, CallSiteChain callSiteChain)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteFactory.TryCreateExact(ServiceDescriptor descriptor, ServiceIdentifier serviceIdentifier, CallSiteChain callSiteChain, Int32 slot)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteFactory.GetCallSite(ServiceDescriptor serviceDescriptor, CallSiteChain callSiteChain)
   at Microsoft.Extensions.DependencyInjection.ServiceProvider.ValidateService(ServiceDescriptor descriptor)
   --- End of inner exception stack trace ---
   at Microsoft.Extensions.DependencyInjection.ServiceProvider.ValidateService(ServiceDescriptor descriptor)
   at Microsoft.Extensions.DependencyInjection.ServiceProvider..ctor(ICollection`1 serviceDescriptors, ServiceProviderOptions options)<---

 ---> (Inner Exception #6) System.InvalidOperationException: Error while validating the service descriptor 'ServiceType: Microsoft.AspNetCore.Authorization.IAuthorizationHandler Lifetime: Scoped ImplementationType: WhimLabAI.WebApi.Authorization.PermissionAuthorizationHandler': Unable to resolve service for type 'WhimLabAI.Domain.Repositories.IAdminUserRepository' while attempting to activate 'WhimLabAI.WebApi.Authorization.PermissionAuthorizationHandler'.
 ---> System.InvalidOperationException: Unable to resolve service for type 'WhimLabAI.Domain.Repositories.IAdminUserRepository' while attempting to activate 'WhimLabAI.WebApi.Authorization.PermissionAuthorizationHandler'.
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteFactory.CreateArgumentCallSites(ServiceIdentifier serviceIdentifier, Type implementationType, CallSiteChain callSiteChain, ParameterInfo[] parameters, Boolean throwIfCallSiteNotFound)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteFactory.CreateConstructorCallSite(ResultCache lifetime, ServiceIdentifier serviceIdentifier, Type implementationType, CallSiteChain callSiteChain)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteFactory.TryCreateExact(ServiceDescriptor descriptor, ServiceIdentifier serviceIdentifier, CallSiteChain callSiteChain, Int32 slot)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteFactory.GetCallSite(ServiceDescriptor serviceDescriptor, CallSiteChain callSiteChain)
   at Microsoft.Extensions.DependencyInjection.ServiceProvider.ValidateService(ServiceDescriptor descriptor)
   --- End of inner exception stack trace ---
   at Microsoft.Extensions.DependencyInjection.ServiceProvider.ValidateService(ServiceDescriptor descriptor)
   at Microsoft.Extensions.DependencyInjection.ServiceProvider..ctor(ICollection`1 serviceDescriptors, ServiceProviderOptions options)<---

2025-07-21 00:52:47.551 +08:00 [FTL] Application terminated unexpectedly
System.AggregateException: Some services are not able to be constructed (Error while validating the service descriptor 'ServiceType: Microsoft.AspNetCore.Authorization.IAuthorizationService Lifetime: Transient ImplementationType: Microsoft.AspNetCore.Authorization.DefaultAuthorizationService': Unable to resolve service for type 'WhimLabAI.Domain.Repositories.IAdminUserRepository' while attempting to activate 'WhimLabAI.WebApi.Authorization.PermissionAuthorizationHandler'.) (Error while validating the service descriptor 'ServiceType: Microsoft.AspNetCore.Authorization.IAuthorizationHandlerProvider Lifetime: Transient ImplementationType: Microsoft.AspNetCore.Authorization.DefaultAuthorizationHandlerProvider': Unable to resolve service for type 'WhimLabAI.Domain.Repositories.IAdminUserRepository' while attempting to activate 'WhimLabAI.WebApi.Authorization.PermissionAuthorizationHandler'.) (Error while validating the service descriptor 'ServiceType: Microsoft.AspNetCore.Authorization.Policy.IPolicyEvaluator Lifetime: Transient ImplementationType: Microsoft.AspNetCore.Authorization.Policy.PolicyEvaluator': Unable to resolve service for type 'WhimLabAI.Domain.Repositories.IAdminUserRepository' while attempting to activate 'WhimLabAI.WebApi.Authorization.PermissionAuthorizationHandler'.) (Error while validating the service descriptor 'ServiceType: WhimLabAI.Infrastructure.ApiSecurity.IRateLimitingService Lifetime: Singleton ImplementationType: WhimLabAI.Infrastructure.ApiSecurity.RateLimitingService': Unable to resolve service for type 'Microsoft.Extensions.Caching.Distributed.IDistributedCache' while attempting to activate 'WhimLabAI.Infrastructure.ApiSecurity.RateLimitingService'.) (Error while validating the service descriptor 'ServiceType: WhimLabAI.Infrastructure.ApiSecurity.IApiKeyManagementService Lifetime: Scoped ImplementationType: WhimLabAI.Infrastructure.ApiSecurity.ApiKeyManagementService': Unable to resolve service for type 'WhimLabAI.Infrastructure.Data.WhimLabAIDbContext' while attempting to activate 'WhimLabAI.Infrastructure.ApiSecurity.ApiKeyManagementService'.) (Error while validating the service descriptor 'ServiceType: WhimLabAI.Infrastructure.ApiSecurity.ApiKeyAuthenticationHandler Lifetime: Transient ImplementationType: WhimLabAI.Infrastructure.ApiSecurity.ApiKeyAuthenticationHandler': Unable to resolve service for type 'WhimLabAI.Infrastructure.Data.WhimLabAIDbContext' while attempting to activate 'WhimLabAI.Infrastructure.ApiSecurity.ApiKeyAuthenticationHandler'.) (Error while validating the service descriptor 'ServiceType: Microsoft.AspNetCore.Authorization.IAuthorizationHandler Lifetime: Scoped ImplementationType: WhimLabAI.WebApi.Authorization.PermissionAuthorizationHandler': Unable to resolve service for type 'WhimLabAI.Domain.Repositories.IAdminUserRepository' while attempting to activate 'WhimLabAI.WebApi.Authorization.PermissionAuthorizationHandler'.)
 ---> System.InvalidOperationException: Error while validating the service descriptor 'ServiceType: Microsoft.AspNetCore.Authorization.IAuthorizationService Lifetime: Transient ImplementationType: Microsoft.AspNetCore.Authorization.DefaultAuthorizationService': Unable to resolve service for type 'WhimLabAI.Domain.Repositories.IAdminUserRepository' while attempting to activate 'WhimLabAI.WebApi.Authorization.PermissionAuthorizationHandler'.
 ---> System.InvalidOperationException: Unable to resolve service for type 'WhimLabAI.Domain.Repositories.IAdminUserRepository' while attempting to activate 'WhimLabAI.WebApi.Authorization.PermissionAuthorizationHandler'.
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteFactory.CreateArgumentCallSites(ServiceIdentifier serviceIdentifier, Type implementationType, CallSiteChain callSiteChain, ParameterInfo[] parameters, Boolean throwIfCallSiteNotFound)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteFactory.CreateConstructorCallSite(ResultCache lifetime, ServiceIdentifier serviceIdentifier, Type implementationType, CallSiteChain callSiteChain)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteFactory.TryCreateExact(ServiceDescriptor descriptor, ServiceIdentifier serviceIdentifier, CallSiteChain callSiteChain, Int32 slot)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteFactory.TryCreateEnumerable(ServiceIdentifier serviceIdentifier, CallSiteChain callSiteChain)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteFactory.CreateCallSite(ServiceIdentifier serviceIdentifier, CallSiteChain callSiteChain)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteFactory.CreateArgumentCallSites(ServiceIdentifier serviceIdentifier, Type implementationType, CallSiteChain callSiteChain, ParameterInfo[] parameters, Boolean throwIfCallSiteNotFound)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteFactory.CreateConstructorCallSite(ResultCache lifetime, ServiceIdentifier serviceIdentifier, Type implementationType, CallSiteChain callSiteChain)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteFactory.TryCreateExact(ServiceDescriptor descriptor, ServiceIdentifier serviceIdentifier, CallSiteChain callSiteChain, Int32 slot)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteFactory.TryCreateExact(ServiceIdentifier serviceIdentifier, CallSiteChain callSiteChain)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteFactory.CreateCallSite(ServiceIdentifier serviceIdentifier, CallSiteChain callSiteChain)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteFactory.CreateArgumentCallSites(ServiceIdentifier serviceIdentifier, Type implementationType, CallSiteChain callSiteChain, ParameterInfo[] parameters, Boolean throwIfCallSiteNotFound)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteFactory.CreateConstructorCallSite(ResultCache lifetime, ServiceIdentifier serviceIdentifier, Type implementationType, CallSiteChain callSiteChain)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteFactory.TryCreateExact(ServiceDescriptor descriptor, ServiceIdentifier serviceIdentifier, CallSiteChain callSiteChain, Int32 slot)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteFactory.GetCallSite(ServiceDescriptor serviceDescriptor, CallSiteChain callSiteChain)
   at Microsoft.Extensions.DependencyInjection.ServiceProvider.ValidateService(ServiceDescriptor descriptor)
   --- End of inner exception stack trace ---
   at Microsoft.Extensions.DependencyInjection.ServiceProvider.ValidateService(ServiceDescriptor descriptor)
   at Microsoft.Extensions.DependencyInjection.ServiceProvider..ctor(ICollection`1 serviceDescriptors, ServiceProviderOptions options)
   --- End of inner exception stack trace ---
   at Microsoft.Extensions.DependencyInjection.ServiceProvider..ctor(ICollection`1 serviceDescriptors, ServiceProviderOptions options)
   at Microsoft.Extensions.DependencyInjection.ServiceCollectionContainerBuilderExtensions.BuildServiceProvider(IServiceCollection services, ServiceProviderOptions options)
   at Microsoft.Extensions.Hosting.HostApplicationBuilder.Build()
   at Microsoft.AspNetCore.Builder.WebApplicationBuilder.Build()
   at Program.<Main>$(String[] args) in /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/WebApi/WhimLabAI.WebApi/Program.cs:line 688
 ---> (Inner Exception #1) System.InvalidOperationException: Error while validating the service descriptor 'ServiceType: Microsoft.AspNetCore.Authorization.IAuthorizationHandlerProvider Lifetime: Transient ImplementationType: Microsoft.AspNetCore.Authorization.DefaultAuthorizationHandlerProvider': Unable to resolve service for type 'WhimLabAI.Domain.Repositories.IAdminUserRepository' while attempting to activate 'WhimLabAI.WebApi.Authorization.PermissionAuthorizationHandler'.
 ---> System.InvalidOperationException: Unable to resolve service for type 'WhimLabAI.Domain.Repositories.IAdminUserRepository' while attempting to activate 'WhimLabAI.WebApi.Authorization.PermissionAuthorizationHandler'.
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteFactory.CreateArgumentCallSites(ServiceIdentifier serviceIdentifier, Type implementationType, CallSiteChain callSiteChain, ParameterInfo[] parameters, Boolean throwIfCallSiteNotFound)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteFactory.CreateConstructorCallSite(ResultCache lifetime, ServiceIdentifier serviceIdentifier, Type implementationType, CallSiteChain callSiteChain)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteFactory.TryCreateExact(ServiceDescriptor descriptor, ServiceIdentifier serviceIdentifier, CallSiteChain callSiteChain, Int32 slot)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteFactory.TryCreateEnumerable(ServiceIdentifier serviceIdentifier, CallSiteChain callSiteChain)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteFactory.CreateCallSite(ServiceIdentifier serviceIdentifier, CallSiteChain callSiteChain)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteFactory.CreateArgumentCallSites(ServiceIdentifier serviceIdentifier, Type implementationType, CallSiteChain callSiteChain, ParameterInfo[] parameters, Boolean throwIfCallSiteNotFound)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteFactory.CreateConstructorCallSite(ResultCache lifetime, ServiceIdentifier serviceIdentifier, Type implementationType, CallSiteChain callSiteChain)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteFactory.TryCreateExact(ServiceDescriptor descriptor, ServiceIdentifier serviceIdentifier, CallSiteChain callSiteChain, Int32 slot)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteFactory.GetCallSite(ServiceDescriptor serviceDescriptor, CallSiteChain callSiteChain)
   at Microsoft.Extensions.DependencyInjection.ServiceProvider.ValidateService(ServiceDescriptor descriptor)
   --- End of inner exception stack trace ---
   at Microsoft.Extensions.DependencyInjection.ServiceProvider.ValidateService(ServiceDescriptor descriptor)
   at Microsoft.Extensions.DependencyInjection.ServiceProvider..ctor(ICollection`1 serviceDescriptors, ServiceProviderOptions options)<---

 ---> (Inner Exception #2) System.InvalidOperationException: Error while validating the service descriptor 'ServiceType: Microsoft.AspNetCore.Authorization.Policy.IPolicyEvaluator Lifetime: Transient ImplementationType: Microsoft.AspNetCore.Authorization.Policy.PolicyEvaluator': Unable to resolve service for type 'WhimLabAI.Domain.Repositories.IAdminUserRepository' while attempting to activate 'WhimLabAI.WebApi.Authorization.PermissionAuthorizationHandler'.
 ---> System.InvalidOperationException: Unable to resolve service for type 'WhimLabAI.Domain.Repositories.IAdminUserRepository' while attempting to activate 'WhimLabAI.WebApi.Authorization.PermissionAuthorizationHandler'.
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteFactory.CreateArgumentCallSites(ServiceIdentifier serviceIdentifier, Type implementationType, CallSiteChain callSiteChain, ParameterInfo[] parameters, Boolean throwIfCallSiteNotFound)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteFactory.CreateConstructorCallSite(ResultCache lifetime, ServiceIdentifier serviceIdentifier, Type implementationType, CallSiteChain callSiteChain)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteFactory.TryCreateExact(ServiceDescriptor descriptor, ServiceIdentifier serviceIdentifier, CallSiteChain callSiteChain, Int32 slot)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteFactory.TryCreateEnumerable(ServiceIdentifier serviceIdentifier, CallSiteChain callSiteChain)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteFactory.CreateCallSite(ServiceIdentifier serviceIdentifier, CallSiteChain callSiteChain)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteFactory.CreateArgumentCallSites(ServiceIdentifier serviceIdentifier, Type implementationType, CallSiteChain callSiteChain, ParameterInfo[] parameters, Boolean throwIfCallSiteNotFound)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteFactory.CreateConstructorCallSite(ResultCache lifetime, ServiceIdentifier serviceIdentifier, Type implementationType, CallSiteChain callSiteChain)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteFactory.TryCreateExact(ServiceDescriptor descriptor, ServiceIdentifier serviceIdentifier, CallSiteChain callSiteChain, Int32 slot)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteFactory.TryCreateExact(ServiceIdentifier serviceIdentifier, CallSiteChain callSiteChain)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteFactory.CreateCallSite(ServiceIdentifier serviceIdentifier, CallSiteChain callSiteChain)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteFactory.CreateArgumentCallSites(ServiceIdentifier serviceIdentifier, Type implementationType, CallSiteChain callSiteChain, ParameterInfo[] parameters, Boolean throwIfCallSiteNotFound)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteFactory.CreateConstructorCallSite(ResultCache lifetime, ServiceIdentifier serviceIdentifier, Type implementationType, CallSiteChain callSiteChain)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteFactory.TryCreateExact(ServiceDescriptor descriptor, ServiceIdentifier serviceIdentifier, CallSiteChain callSiteChain, Int32 slot)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteFactory.TryCreateExact(ServiceIdentifier serviceIdentifier, CallSiteChain callSiteChain)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteFactory.CreateCallSite(ServiceIdentifier serviceIdentifier, CallSiteChain callSiteChain)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteFactory.CreateArgumentCallSites(ServiceIdentifier serviceIdentifier, Type implementationType, CallSiteChain callSiteChain, ParameterInfo[] parameters, Boolean throwIfCallSiteNotFound)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteFactory.CreateConstructorCallSite(ResultCache lifetime, ServiceIdentifier serviceIdentifier, Type implementationType, CallSiteChain callSiteChain)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteFactory.TryCreateExact(ServiceDescriptor descriptor, ServiceIdentifier serviceIdentifier, CallSiteChain callSiteChain, Int32 slot)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteFactory.GetCallSite(ServiceDescriptor serviceDescriptor, CallSiteChain callSiteChain)
   at Microsoft.Extensions.DependencyInjection.ServiceProvider.ValidateService(ServiceDescriptor descriptor)
   --- End of inner exception stack trace ---
   at Microsoft.Extensions.DependencyInjection.ServiceProvider.ValidateService(ServiceDescriptor descriptor)
   at Microsoft.Extensions.DependencyInjection.ServiceProvider..ctor(ICollection`1 serviceDescriptors, ServiceProviderOptions options)<---

 ---> (Inner Exception #3) System.InvalidOperationException: Error while validating the service descriptor 'ServiceType: WhimLabAI.Infrastructure.ApiSecurity.IRateLimitingService Lifetime: Singleton ImplementationType: WhimLabAI.Infrastructure.ApiSecurity.RateLimitingService': Unable to resolve service for type 'Microsoft.Extensions.Caching.Distributed.IDistributedCache' while attempting to activate 'WhimLabAI.Infrastructure.ApiSecurity.RateLimitingService'.
 ---> System.InvalidOperationException: Unable to resolve service for type 'Microsoft.Extensions.Caching.Distributed.IDistributedCache' while attempting to activate 'WhimLabAI.Infrastructure.ApiSecurity.RateLimitingService'.
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteFactory.CreateArgumentCallSites(ServiceIdentifier serviceIdentifier, Type implementationType, CallSiteChain callSiteChain, ParameterInfo[] parameters, Boolean throwIfCallSiteNotFound)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteFactory.CreateConstructorCallSite(ResultCache lifetime, ServiceIdentifier serviceIdentifier, Type implementationType, CallSiteChain callSiteChain)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteFactory.TryCreateExact(ServiceDescriptor descriptor, ServiceIdentifier serviceIdentifier, CallSiteChain callSiteChain, Int32 slot)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteFactory.GetCallSite(ServiceDescriptor serviceDescriptor, CallSiteChain callSiteChain)
   at Microsoft.Extensions.DependencyInjection.ServiceProvider.ValidateService(ServiceDescriptor descriptor)
   --- End of inner exception stack trace ---
   at Microsoft.Extensions.DependencyInjection.ServiceProvider.ValidateService(ServiceDescriptor descriptor)
   at Microsoft.Extensions.DependencyInjection.ServiceProvider..ctor(ICollection`1 serviceDescriptors, ServiceProviderOptions options)<---

 ---> (Inner Exception #4) System.InvalidOperationException: Error while validating the service descriptor 'ServiceType: WhimLabAI.Infrastructure.ApiSecurity.IApiKeyManagementService Lifetime: Scoped ImplementationType: WhimLabAI.Infrastructure.ApiSecurity.ApiKeyManagementService': Unable to resolve service for type 'WhimLabAI.Infrastructure.Data.WhimLabAIDbContext' while attempting to activate 'WhimLabAI.Infrastructure.ApiSecurity.ApiKeyManagementService'.
 ---> System.InvalidOperationException: Unable to resolve service for type 'WhimLabAI.Infrastructure.Data.WhimLabAIDbContext' while attempting to activate 'WhimLabAI.Infrastructure.ApiSecurity.ApiKeyManagementService'.
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteFactory.CreateArgumentCallSites(ServiceIdentifier serviceIdentifier, Type implementationType, CallSiteChain callSiteChain, ParameterInfo[] parameters, Boolean throwIfCallSiteNotFound)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteFactory.CreateConstructorCallSite(ResultCache lifetime, ServiceIdentifier serviceIdentifier, Type implementationType, CallSiteChain callSiteChain)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteFactory.TryCreateExact(ServiceDescriptor descriptor, ServiceIdentifier serviceIdentifier, CallSiteChain callSiteChain, Int32 slot)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteFactory.GetCallSite(ServiceDescriptor serviceDescriptor, CallSiteChain callSiteChain)
   at Microsoft.Extensions.DependencyInjection.ServiceProvider.ValidateService(ServiceDescriptor descriptor)
   --- End of inner exception stack trace ---
   at Microsoft.Extensions.DependencyInjection.ServiceProvider.ValidateService(ServiceDescriptor descriptor)
   at Microsoft.Extensions.DependencyInjection.ServiceProvider..ctor(ICollection`1 serviceDescriptors, ServiceProviderOptions options)<---

 ---> (Inner Exception #5) System.InvalidOperationException: Error while validating the service descriptor 'ServiceType: WhimLabAI.Infrastructure.ApiSecurity.ApiKeyAuthenticationHandler Lifetime: Transient ImplementationType: WhimLabAI.Infrastructure.ApiSecurity.ApiKeyAuthenticationHandler': Unable to resolve service for type 'WhimLabAI.Infrastructure.Data.WhimLabAIDbContext' while attempting to activate 'WhimLabAI.Infrastructure.ApiSecurity.ApiKeyAuthenticationHandler'.
 ---> System.InvalidOperationException: Unable to resolve service for type 'WhimLabAI.Infrastructure.Data.WhimLabAIDbContext' while attempting to activate 'WhimLabAI.Infrastructure.ApiSecurity.ApiKeyAuthenticationHandler'.
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteFactory.CreateArgumentCallSites(ServiceIdentifier serviceIdentifier, Type implementationType, CallSiteChain callSiteChain, ParameterInfo[] parameters, Boolean throwIfCallSiteNotFound)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteFactory.CreateConstructorCallSite(ResultCache lifetime, ServiceIdentifier serviceIdentifier, Type implementationType, CallSiteChain callSiteChain)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteFactory.TryCreateExact(ServiceDescriptor descriptor, ServiceIdentifier serviceIdentifier, CallSiteChain callSiteChain, Int32 slot)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteFactory.GetCallSite(ServiceDescriptor serviceDescriptor, CallSiteChain callSiteChain)
   at Microsoft.Extensions.DependencyInjection.ServiceProvider.ValidateService(ServiceDescriptor descriptor)
   --- End of inner exception stack trace ---
   at Microsoft.Extensions.DependencyInjection.ServiceProvider.ValidateService(ServiceDescriptor descriptor)
   at Microsoft.Extensions.DependencyInjection.ServiceProvider..ctor(ICollection`1 serviceDescriptors, ServiceProviderOptions options)<---

 ---> (Inner Exception #6) System.InvalidOperationException: Error while validating the service descriptor 'ServiceType: Microsoft.AspNetCore.Authorization.IAuthorizationHandler Lifetime: Scoped ImplementationType: WhimLabAI.WebApi.Authorization.PermissionAuthorizationHandler': Unable to resolve service for type 'WhimLabAI.Domain.Repositories.IAdminUserRepository' while attempting to activate 'WhimLabAI.WebApi.Authorization.PermissionAuthorizationHandler'.
 ---> System.InvalidOperationException: Unable to resolve service for type 'WhimLabAI.Domain.Repositories.IAdminUserRepository' while attempting to activate 'WhimLabAI.WebApi.Authorization.PermissionAuthorizationHandler'.
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteFactory.CreateArgumentCallSites(ServiceIdentifier serviceIdentifier, Type implementationType, CallSiteChain callSiteChain, ParameterInfo[] parameters, Boolean throwIfCallSiteNotFound)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteFactory.CreateConstructorCallSite(ResultCache lifetime, ServiceIdentifier serviceIdentifier, Type implementationType, CallSiteChain callSiteChain)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteFactory.TryCreateExact(ServiceDescriptor descriptor, ServiceIdentifier serviceIdentifier, CallSiteChain callSiteChain, Int32 slot)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteFactory.GetCallSite(ServiceDescriptor serviceDescriptor, CallSiteChain callSiteChain)
   at Microsoft.Extensions.DependencyInjection.ServiceProvider.ValidateService(ServiceDescriptor descriptor)
   --- End of inner exception stack trace ---
   at Microsoft.Extensions.DependencyInjection.ServiceProvider.ValidateService(ServiceDescriptor descriptor)
   at Microsoft.Extensions.DependencyInjection.ServiceProvider..ctor(ICollection`1 serviceDescriptors, ServiceProviderOptions options)<---

2025-07-21 00:55:25.036 +08:00 [INF] Starting WhimLabAI Web API
2025-07-21 00:55:25.561 +08:00 [INF] WebApplication builder created
2025-07-21 00:55:25.561 +08:00 [INF] Loading configuration files...
2025-07-21 00:55:25.562 +08:00 [INF] Environment variables loaded
2025-07-21 00:55:25.579 +08:00 [INF] Environment variable replacements loaded
2025-07-21 00:55:25.579 +08:00 [INF] Configuration loaded
2025-07-21 00:55:25.585 +08:00 [INF] Skipping Infrastructure services for debugging...
2025-07-21 00:55:25.585 +08:00 [INF] Infrastructure services skipped
2025-07-21 00:55:25.585 +08:00 [INF] Skipping Application services for debugging...
2025-07-21 00:55:25.585 +08:00 [INF] Application services skipped
2025-07-21 00:55:25.585 +08:00 [INF] Skipping RealtimeNotificationService for debugging...
2025-07-21 00:55:25.585 +08:00 [INF] RealtimeNotificationService skipped
2025-07-21 00:55:25.589 +08:00 [INF] Skipping security services for debugging...
2025-07-21 00:55:25.589 +08:00 [INF] Security services skipped
2025-07-21 00:55:25.590 +08:00 [INF] Configuring API versioning...
2025-07-21 00:55:25.590 +08:00 [INF] API versioning configured
2025-07-21 00:55:25.590 +08:00 [INF] Configuring input validation...
2025-07-21 00:55:25.590 +08:00 [INF] Input validation configured
2025-07-21 00:55:25.590 +08:00 [INF] Configuring rate limiting...
2025-07-21 00:55:25.592 +08:00 [INF] Rate limiting configured
2025-07-21 00:55:25.599 +08:00 [INF] All services registered, building application...
2025-07-21 00:55:25.599 +08:00 [INF] Calling builder.Build()...
2025-07-21 00:55:25.599 +08:00 [INF] About to call builder.Build() - checking for blocking services...
2025-07-21 00:55:25.620 +08:00 [FTL] Failed to build application
System.AggregateException: Some services are not able to be constructed (Error while validating the service descriptor 'ServiceType: Microsoft.AspNetCore.Authorization.IAuthorizationService Lifetime: Transient ImplementationType: Microsoft.AspNetCore.Authorization.DefaultAuthorizationService': Unable to resolve service for type 'WhimLabAI.Domain.Repositories.IAdminUserRepository' while attempting to activate 'WhimLabAI.WebApi.Authorization.PermissionAuthorizationHandler'.) (Error while validating the service descriptor 'ServiceType: Microsoft.AspNetCore.Authorization.IAuthorizationHandlerProvider Lifetime: Transient ImplementationType: Microsoft.AspNetCore.Authorization.DefaultAuthorizationHandlerProvider': Unable to resolve service for type 'WhimLabAI.Domain.Repositories.IAdminUserRepository' while attempting to activate 'WhimLabAI.WebApi.Authorization.PermissionAuthorizationHandler'.) (Error while validating the service descriptor 'ServiceType: Microsoft.AspNetCore.Authorization.Policy.IPolicyEvaluator Lifetime: Transient ImplementationType: Microsoft.AspNetCore.Authorization.Policy.PolicyEvaluator': Unable to resolve service for type 'WhimLabAI.Domain.Repositories.IAdminUserRepository' while attempting to activate 'WhimLabAI.WebApi.Authorization.PermissionAuthorizationHandler'.) (Error while validating the service descriptor 'ServiceType: WhimLabAI.Infrastructure.ApiSecurity.IRateLimitingService Lifetime: Singleton ImplementationType: WhimLabAI.Infrastructure.ApiSecurity.RateLimitingService': Unable to resolve service for type 'Microsoft.Extensions.Caching.Distributed.IDistributedCache' while attempting to activate 'WhimLabAI.Infrastructure.ApiSecurity.RateLimitingService'.) (Error while validating the service descriptor 'ServiceType: WhimLabAI.Infrastructure.ApiSecurity.IApiKeyManagementService Lifetime: Scoped ImplementationType: WhimLabAI.Infrastructure.ApiSecurity.ApiKeyManagementService': Unable to resolve service for type 'WhimLabAI.Infrastructure.Data.WhimLabAIDbContext' while attempting to activate 'WhimLabAI.Infrastructure.ApiSecurity.ApiKeyManagementService'.) (Error while validating the service descriptor 'ServiceType: WhimLabAI.Infrastructure.ApiSecurity.ApiKeyAuthenticationHandler Lifetime: Transient ImplementationType: WhimLabAI.Infrastructure.ApiSecurity.ApiKeyAuthenticationHandler': Unable to resolve service for type 'WhimLabAI.Infrastructure.Data.WhimLabAIDbContext' while attempting to activate 'WhimLabAI.Infrastructure.ApiSecurity.ApiKeyAuthenticationHandler'.) (Error while validating the service descriptor 'ServiceType: Microsoft.AspNetCore.Authorization.IAuthorizationHandler Lifetime: Scoped ImplementationType: WhimLabAI.WebApi.Authorization.PermissionAuthorizationHandler': Unable to resolve service for type 'WhimLabAI.Domain.Repositories.IAdminUserRepository' while attempting to activate 'WhimLabAI.WebApi.Authorization.PermissionAuthorizationHandler'.)
 ---> System.InvalidOperationException: Error while validating the service descriptor 'ServiceType: Microsoft.AspNetCore.Authorization.IAuthorizationService Lifetime: Transient ImplementationType: Microsoft.AspNetCore.Authorization.DefaultAuthorizationService': Unable to resolve service for type 'WhimLabAI.Domain.Repositories.IAdminUserRepository' while attempting to activate 'WhimLabAI.WebApi.Authorization.PermissionAuthorizationHandler'.
 ---> System.InvalidOperationException: Unable to resolve service for type 'WhimLabAI.Domain.Repositories.IAdminUserRepository' while attempting to activate 'WhimLabAI.WebApi.Authorization.PermissionAuthorizationHandler'.
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteFactory.CreateArgumentCallSites(ServiceIdentifier serviceIdentifier, Type implementationType, CallSiteChain callSiteChain, ParameterInfo[] parameters, Boolean throwIfCallSiteNotFound)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteFactory.CreateConstructorCallSite(ResultCache lifetime, ServiceIdentifier serviceIdentifier, Type implementationType, CallSiteChain callSiteChain)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteFactory.TryCreateExact(ServiceDescriptor descriptor, ServiceIdentifier serviceIdentifier, CallSiteChain callSiteChain, Int32 slot)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteFactory.TryCreateEnumerable(ServiceIdentifier serviceIdentifier, CallSiteChain callSiteChain)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteFactory.CreateCallSite(ServiceIdentifier serviceIdentifier, CallSiteChain callSiteChain)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteFactory.CreateArgumentCallSites(ServiceIdentifier serviceIdentifier, Type implementationType, CallSiteChain callSiteChain, ParameterInfo[] parameters, Boolean throwIfCallSiteNotFound)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteFactory.CreateConstructorCallSite(ResultCache lifetime, ServiceIdentifier serviceIdentifier, Type implementationType, CallSiteChain callSiteChain)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteFactory.TryCreateExact(ServiceDescriptor descriptor, ServiceIdentifier serviceIdentifier, CallSiteChain callSiteChain, Int32 slot)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteFactory.TryCreateExact(ServiceIdentifier serviceIdentifier, CallSiteChain callSiteChain)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteFactory.CreateCallSite(ServiceIdentifier serviceIdentifier, CallSiteChain callSiteChain)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteFactory.CreateArgumentCallSites(ServiceIdentifier serviceIdentifier, Type implementationType, CallSiteChain callSiteChain, ParameterInfo[] parameters, Boolean throwIfCallSiteNotFound)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteFactory.CreateConstructorCallSite(ResultCache lifetime, ServiceIdentifier serviceIdentifier, Type implementationType, CallSiteChain callSiteChain)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteFactory.TryCreateExact(ServiceDescriptor descriptor, ServiceIdentifier serviceIdentifier, CallSiteChain callSiteChain, Int32 slot)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteFactory.GetCallSite(ServiceDescriptor serviceDescriptor, CallSiteChain callSiteChain)
   at Microsoft.Extensions.DependencyInjection.ServiceProvider.ValidateService(ServiceDescriptor descriptor)
   --- End of inner exception stack trace ---
   at Microsoft.Extensions.DependencyInjection.ServiceProvider.ValidateService(ServiceDescriptor descriptor)
   at Microsoft.Extensions.DependencyInjection.ServiceProvider..ctor(ICollection`1 serviceDescriptors, ServiceProviderOptions options)
   --- End of inner exception stack trace ---
   at Microsoft.Extensions.DependencyInjection.ServiceProvider..ctor(ICollection`1 serviceDescriptors, ServiceProviderOptions options)
   at Microsoft.Extensions.DependencyInjection.ServiceCollectionContainerBuilderExtensions.BuildServiceProvider(IServiceCollection services, ServiceProviderOptions options)
   at Microsoft.Extensions.Hosting.HostApplicationBuilder.Build()
   at Microsoft.AspNetCore.Builder.WebApplicationBuilder.Build()
   at Program.<Main>$(String[] args) in /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/WebApi/WhimLabAI.WebApi/Program.cs:line 688
 ---> (Inner Exception #1) System.InvalidOperationException: Error while validating the service descriptor 'ServiceType: Microsoft.AspNetCore.Authorization.IAuthorizationHandlerProvider Lifetime: Transient ImplementationType: Microsoft.AspNetCore.Authorization.DefaultAuthorizationHandlerProvider': Unable to resolve service for type 'WhimLabAI.Domain.Repositories.IAdminUserRepository' while attempting to activate 'WhimLabAI.WebApi.Authorization.PermissionAuthorizationHandler'.
 ---> System.InvalidOperationException: Unable to resolve service for type 'WhimLabAI.Domain.Repositories.IAdminUserRepository' while attempting to activate 'WhimLabAI.WebApi.Authorization.PermissionAuthorizationHandler'.
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteFactory.CreateArgumentCallSites(ServiceIdentifier serviceIdentifier, Type implementationType, CallSiteChain callSiteChain, ParameterInfo[] parameters, Boolean throwIfCallSiteNotFound)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteFactory.CreateConstructorCallSite(ResultCache lifetime, ServiceIdentifier serviceIdentifier, Type implementationType, CallSiteChain callSiteChain)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteFactory.TryCreateExact(ServiceDescriptor descriptor, ServiceIdentifier serviceIdentifier, CallSiteChain callSiteChain, Int32 slot)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteFactory.TryCreateEnumerable(ServiceIdentifier serviceIdentifier, CallSiteChain callSiteChain)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteFactory.CreateCallSite(ServiceIdentifier serviceIdentifier, CallSiteChain callSiteChain)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteFactory.CreateArgumentCallSites(ServiceIdentifier serviceIdentifier, Type implementationType, CallSiteChain callSiteChain, ParameterInfo[] parameters, Boolean throwIfCallSiteNotFound)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteFactory.CreateConstructorCallSite(ResultCache lifetime, ServiceIdentifier serviceIdentifier, Type implementationType, CallSiteChain callSiteChain)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteFactory.TryCreateExact(ServiceDescriptor descriptor, ServiceIdentifier serviceIdentifier, CallSiteChain callSiteChain, Int32 slot)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteFactory.GetCallSite(ServiceDescriptor serviceDescriptor, CallSiteChain callSiteChain)
   at Microsoft.Extensions.DependencyInjection.ServiceProvider.ValidateService(ServiceDescriptor descriptor)
   --- End of inner exception stack trace ---
   at Microsoft.Extensions.DependencyInjection.ServiceProvider.ValidateService(ServiceDescriptor descriptor)
   at Microsoft.Extensions.DependencyInjection.ServiceProvider..ctor(ICollection`1 serviceDescriptors, ServiceProviderOptions options)<---

 ---> (Inner Exception #2) System.InvalidOperationException: Error while validating the service descriptor 'ServiceType: Microsoft.AspNetCore.Authorization.Policy.IPolicyEvaluator Lifetime: Transient ImplementationType: Microsoft.AspNetCore.Authorization.Policy.PolicyEvaluator': Unable to resolve service for type 'WhimLabAI.Domain.Repositories.IAdminUserRepository' while attempting to activate 'WhimLabAI.WebApi.Authorization.PermissionAuthorizationHandler'.
 ---> System.InvalidOperationException: Unable to resolve service for type 'WhimLabAI.Domain.Repositories.IAdminUserRepository' while attempting to activate 'WhimLabAI.WebApi.Authorization.PermissionAuthorizationHandler'.
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteFactory.CreateArgumentCallSites(ServiceIdentifier serviceIdentifier, Type implementationType, CallSiteChain callSiteChain, ParameterInfo[] parameters, Boolean throwIfCallSiteNotFound)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteFactory.CreateConstructorCallSite(ResultCache lifetime, ServiceIdentifier serviceIdentifier, Type implementationType, CallSiteChain callSiteChain)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteFactory.TryCreateExact(ServiceDescriptor descriptor, ServiceIdentifier serviceIdentifier, CallSiteChain callSiteChain, Int32 slot)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteFactory.TryCreateEnumerable(ServiceIdentifier serviceIdentifier, CallSiteChain callSiteChain)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteFactory.CreateCallSite(ServiceIdentifier serviceIdentifier, CallSiteChain callSiteChain)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteFactory.CreateArgumentCallSites(ServiceIdentifier serviceIdentifier, Type implementationType, CallSiteChain callSiteChain, ParameterInfo[] parameters, Boolean throwIfCallSiteNotFound)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteFactory.CreateConstructorCallSite(ResultCache lifetime, ServiceIdentifier serviceIdentifier, Type implementationType, CallSiteChain callSiteChain)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteFactory.TryCreateExact(ServiceDescriptor descriptor, ServiceIdentifier serviceIdentifier, CallSiteChain callSiteChain, Int32 slot)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteFactory.TryCreateExact(ServiceIdentifier serviceIdentifier, CallSiteChain callSiteChain)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteFactory.CreateCallSite(ServiceIdentifier serviceIdentifier, CallSiteChain callSiteChain)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteFactory.CreateArgumentCallSites(ServiceIdentifier serviceIdentifier, Type implementationType, CallSiteChain callSiteChain, ParameterInfo[] parameters, Boolean throwIfCallSiteNotFound)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteFactory.CreateConstructorCallSite(ResultCache lifetime, ServiceIdentifier serviceIdentifier, Type implementationType, CallSiteChain callSiteChain)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteFactory.TryCreateExact(ServiceDescriptor descriptor, ServiceIdentifier serviceIdentifier, CallSiteChain callSiteChain, Int32 slot)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteFactory.TryCreateExact(ServiceIdentifier serviceIdentifier, CallSiteChain callSiteChain)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteFactory.CreateCallSite(ServiceIdentifier serviceIdentifier, CallSiteChain callSiteChain)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteFactory.CreateArgumentCallSites(ServiceIdentifier serviceIdentifier, Type implementationType, CallSiteChain callSiteChain, ParameterInfo[] parameters, Boolean throwIfCallSiteNotFound)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteFactory.CreateConstructorCallSite(ResultCache lifetime, ServiceIdentifier serviceIdentifier, Type implementationType, CallSiteChain callSiteChain)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteFactory.TryCreateExact(ServiceDescriptor descriptor, ServiceIdentifier serviceIdentifier, CallSiteChain callSiteChain, Int32 slot)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteFactory.GetCallSite(ServiceDescriptor serviceDescriptor, CallSiteChain callSiteChain)
   at Microsoft.Extensions.DependencyInjection.ServiceProvider.ValidateService(ServiceDescriptor descriptor)
   --- End of inner exception stack trace ---
   at Microsoft.Extensions.DependencyInjection.ServiceProvider.ValidateService(ServiceDescriptor descriptor)
   at Microsoft.Extensions.DependencyInjection.ServiceProvider..ctor(ICollection`1 serviceDescriptors, ServiceProviderOptions options)<---

 ---> (Inner Exception #3) System.InvalidOperationException: Error while validating the service descriptor 'ServiceType: WhimLabAI.Infrastructure.ApiSecurity.IRateLimitingService Lifetime: Singleton ImplementationType: WhimLabAI.Infrastructure.ApiSecurity.RateLimitingService': Unable to resolve service for type 'Microsoft.Extensions.Caching.Distributed.IDistributedCache' while attempting to activate 'WhimLabAI.Infrastructure.ApiSecurity.RateLimitingService'.
 ---> System.InvalidOperationException: Unable to resolve service for type 'Microsoft.Extensions.Caching.Distributed.IDistributedCache' while attempting to activate 'WhimLabAI.Infrastructure.ApiSecurity.RateLimitingService'.
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteFactory.CreateArgumentCallSites(ServiceIdentifier serviceIdentifier, Type implementationType, CallSiteChain callSiteChain, ParameterInfo[] parameters, Boolean throwIfCallSiteNotFound)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteFactory.CreateConstructorCallSite(ResultCache lifetime, ServiceIdentifier serviceIdentifier, Type implementationType, CallSiteChain callSiteChain)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteFactory.TryCreateExact(ServiceDescriptor descriptor, ServiceIdentifier serviceIdentifier, CallSiteChain callSiteChain, Int32 slot)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteFactory.GetCallSite(ServiceDescriptor serviceDescriptor, CallSiteChain callSiteChain)
   at Microsoft.Extensions.DependencyInjection.ServiceProvider.ValidateService(ServiceDescriptor descriptor)
   --- End of inner exception stack trace ---
   at Microsoft.Extensions.DependencyInjection.ServiceProvider.ValidateService(ServiceDescriptor descriptor)
   at Microsoft.Extensions.DependencyInjection.ServiceProvider..ctor(ICollection`1 serviceDescriptors, ServiceProviderOptions options)<---

 ---> (Inner Exception #4) System.InvalidOperationException: Error while validating the service descriptor 'ServiceType: WhimLabAI.Infrastructure.ApiSecurity.IApiKeyManagementService Lifetime: Scoped ImplementationType: WhimLabAI.Infrastructure.ApiSecurity.ApiKeyManagementService': Unable to resolve service for type 'WhimLabAI.Infrastructure.Data.WhimLabAIDbContext' while attempting to activate 'WhimLabAI.Infrastructure.ApiSecurity.ApiKeyManagementService'.
 ---> System.InvalidOperationException: Unable to resolve service for type 'WhimLabAI.Infrastructure.Data.WhimLabAIDbContext' while attempting to activate 'WhimLabAI.Infrastructure.ApiSecurity.ApiKeyManagementService'.
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteFactory.CreateArgumentCallSites(ServiceIdentifier serviceIdentifier, Type implementationType, CallSiteChain callSiteChain, ParameterInfo[] parameters, Boolean throwIfCallSiteNotFound)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteFactory.CreateConstructorCallSite(ResultCache lifetime, ServiceIdentifier serviceIdentifier, Type implementationType, CallSiteChain callSiteChain)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteFactory.TryCreateExact(ServiceDescriptor descriptor, ServiceIdentifier serviceIdentifier, CallSiteChain callSiteChain, Int32 slot)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteFactory.GetCallSite(ServiceDescriptor serviceDescriptor, CallSiteChain callSiteChain)
   at Microsoft.Extensions.DependencyInjection.ServiceProvider.ValidateService(ServiceDescriptor descriptor)
   --- End of inner exception stack trace ---
   at Microsoft.Extensions.DependencyInjection.ServiceProvider.ValidateService(ServiceDescriptor descriptor)
   at Microsoft.Extensions.DependencyInjection.ServiceProvider..ctor(ICollection`1 serviceDescriptors, ServiceProviderOptions options)<---

 ---> (Inner Exception #5) System.InvalidOperationException: Error while validating the service descriptor 'ServiceType: WhimLabAI.Infrastructure.ApiSecurity.ApiKeyAuthenticationHandler Lifetime: Transient ImplementationType: WhimLabAI.Infrastructure.ApiSecurity.ApiKeyAuthenticationHandler': Unable to resolve service for type 'WhimLabAI.Infrastructure.Data.WhimLabAIDbContext' while attempting to activate 'WhimLabAI.Infrastructure.ApiSecurity.ApiKeyAuthenticationHandler'.
 ---> System.InvalidOperationException: Unable to resolve service for type 'WhimLabAI.Infrastructure.Data.WhimLabAIDbContext' while attempting to activate 'WhimLabAI.Infrastructure.ApiSecurity.ApiKeyAuthenticationHandler'.
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteFactory.CreateArgumentCallSites(ServiceIdentifier serviceIdentifier, Type implementationType, CallSiteChain callSiteChain, ParameterInfo[] parameters, Boolean throwIfCallSiteNotFound)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteFactory.CreateConstructorCallSite(ResultCache lifetime, ServiceIdentifier serviceIdentifier, Type implementationType, CallSiteChain callSiteChain)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteFactory.TryCreateExact(ServiceDescriptor descriptor, ServiceIdentifier serviceIdentifier, CallSiteChain callSiteChain, Int32 slot)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteFactory.GetCallSite(ServiceDescriptor serviceDescriptor, CallSiteChain callSiteChain)
   at Microsoft.Extensions.DependencyInjection.ServiceProvider.ValidateService(ServiceDescriptor descriptor)
   --- End of inner exception stack trace ---
   at Microsoft.Extensions.DependencyInjection.ServiceProvider.ValidateService(ServiceDescriptor descriptor)
   at Microsoft.Extensions.DependencyInjection.ServiceProvider..ctor(ICollection`1 serviceDescriptors, ServiceProviderOptions options)<---

 ---> (Inner Exception #6) System.InvalidOperationException: Error while validating the service descriptor 'ServiceType: Microsoft.AspNetCore.Authorization.IAuthorizationHandler Lifetime: Scoped ImplementationType: WhimLabAI.WebApi.Authorization.PermissionAuthorizationHandler': Unable to resolve service for type 'WhimLabAI.Domain.Repositories.IAdminUserRepository' while attempting to activate 'WhimLabAI.WebApi.Authorization.PermissionAuthorizationHandler'.
 ---> System.InvalidOperationException: Unable to resolve service for type 'WhimLabAI.Domain.Repositories.IAdminUserRepository' while attempting to activate 'WhimLabAI.WebApi.Authorization.PermissionAuthorizationHandler'.
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteFactory.CreateArgumentCallSites(ServiceIdentifier serviceIdentifier, Type implementationType, CallSiteChain callSiteChain, ParameterInfo[] parameters, Boolean throwIfCallSiteNotFound)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteFactory.CreateConstructorCallSite(ResultCache lifetime, ServiceIdentifier serviceIdentifier, Type implementationType, CallSiteChain callSiteChain)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteFactory.TryCreateExact(ServiceDescriptor descriptor, ServiceIdentifier serviceIdentifier, CallSiteChain callSiteChain, Int32 slot)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteFactory.GetCallSite(ServiceDescriptor serviceDescriptor, CallSiteChain callSiteChain)
   at Microsoft.Extensions.DependencyInjection.ServiceProvider.ValidateService(ServiceDescriptor descriptor)
   --- End of inner exception stack trace ---
   at Microsoft.Extensions.DependencyInjection.ServiceProvider.ValidateService(ServiceDescriptor descriptor)
   at Microsoft.Extensions.DependencyInjection.ServiceProvider..ctor(ICollection`1 serviceDescriptors, ServiceProviderOptions options)<---

2025-07-21 00:55:25.713 +08:00 [FTL] Application terminated unexpectedly
System.AggregateException: Some services are not able to be constructed (Error while validating the service descriptor 'ServiceType: Microsoft.AspNetCore.Authorization.IAuthorizationService Lifetime: Transient ImplementationType: Microsoft.AspNetCore.Authorization.DefaultAuthorizationService': Unable to resolve service for type 'WhimLabAI.Domain.Repositories.IAdminUserRepository' while attempting to activate 'WhimLabAI.WebApi.Authorization.PermissionAuthorizationHandler'.) (Error while validating the service descriptor 'ServiceType: Microsoft.AspNetCore.Authorization.IAuthorizationHandlerProvider Lifetime: Transient ImplementationType: Microsoft.AspNetCore.Authorization.DefaultAuthorizationHandlerProvider': Unable to resolve service for type 'WhimLabAI.Domain.Repositories.IAdminUserRepository' while attempting to activate 'WhimLabAI.WebApi.Authorization.PermissionAuthorizationHandler'.) (Error while validating the service descriptor 'ServiceType: Microsoft.AspNetCore.Authorization.Policy.IPolicyEvaluator Lifetime: Transient ImplementationType: Microsoft.AspNetCore.Authorization.Policy.PolicyEvaluator': Unable to resolve service for type 'WhimLabAI.Domain.Repositories.IAdminUserRepository' while attempting to activate 'WhimLabAI.WebApi.Authorization.PermissionAuthorizationHandler'.) (Error while validating the service descriptor 'ServiceType: WhimLabAI.Infrastructure.ApiSecurity.IRateLimitingService Lifetime: Singleton ImplementationType: WhimLabAI.Infrastructure.ApiSecurity.RateLimitingService': Unable to resolve service for type 'Microsoft.Extensions.Caching.Distributed.IDistributedCache' while attempting to activate 'WhimLabAI.Infrastructure.ApiSecurity.RateLimitingService'.) (Error while validating the service descriptor 'ServiceType: WhimLabAI.Infrastructure.ApiSecurity.IApiKeyManagementService Lifetime: Scoped ImplementationType: WhimLabAI.Infrastructure.ApiSecurity.ApiKeyManagementService': Unable to resolve service for type 'WhimLabAI.Infrastructure.Data.WhimLabAIDbContext' while attempting to activate 'WhimLabAI.Infrastructure.ApiSecurity.ApiKeyManagementService'.) (Error while validating the service descriptor 'ServiceType: WhimLabAI.Infrastructure.ApiSecurity.ApiKeyAuthenticationHandler Lifetime: Transient ImplementationType: WhimLabAI.Infrastructure.ApiSecurity.ApiKeyAuthenticationHandler': Unable to resolve service for type 'WhimLabAI.Infrastructure.Data.WhimLabAIDbContext' while attempting to activate 'WhimLabAI.Infrastructure.ApiSecurity.ApiKeyAuthenticationHandler'.) (Error while validating the service descriptor 'ServiceType: Microsoft.AspNetCore.Authorization.IAuthorizationHandler Lifetime: Scoped ImplementationType: WhimLabAI.WebApi.Authorization.PermissionAuthorizationHandler': Unable to resolve service for type 'WhimLabAI.Domain.Repositories.IAdminUserRepository' while attempting to activate 'WhimLabAI.WebApi.Authorization.PermissionAuthorizationHandler'.)
 ---> System.InvalidOperationException: Error while validating the service descriptor 'ServiceType: Microsoft.AspNetCore.Authorization.IAuthorizationService Lifetime: Transient ImplementationType: Microsoft.AspNetCore.Authorization.DefaultAuthorizationService': Unable to resolve service for type 'WhimLabAI.Domain.Repositories.IAdminUserRepository' while attempting to activate 'WhimLabAI.WebApi.Authorization.PermissionAuthorizationHandler'.
 ---> System.InvalidOperationException: Unable to resolve service for type 'WhimLabAI.Domain.Repositories.IAdminUserRepository' while attempting to activate 'WhimLabAI.WebApi.Authorization.PermissionAuthorizationHandler'.
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteFactory.CreateArgumentCallSites(ServiceIdentifier serviceIdentifier, Type implementationType, CallSiteChain callSiteChain, ParameterInfo[] parameters, Boolean throwIfCallSiteNotFound)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteFactory.CreateConstructorCallSite(ResultCache lifetime, ServiceIdentifier serviceIdentifier, Type implementationType, CallSiteChain callSiteChain)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteFactory.TryCreateExact(ServiceDescriptor descriptor, ServiceIdentifier serviceIdentifier, CallSiteChain callSiteChain, Int32 slot)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteFactory.TryCreateEnumerable(ServiceIdentifier serviceIdentifier, CallSiteChain callSiteChain)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteFactory.CreateCallSite(ServiceIdentifier serviceIdentifier, CallSiteChain callSiteChain)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteFactory.CreateArgumentCallSites(ServiceIdentifier serviceIdentifier, Type implementationType, CallSiteChain callSiteChain, ParameterInfo[] parameters, Boolean throwIfCallSiteNotFound)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteFactory.CreateConstructorCallSite(ResultCache lifetime, ServiceIdentifier serviceIdentifier, Type implementationType, CallSiteChain callSiteChain)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteFactory.TryCreateExact(ServiceDescriptor descriptor, ServiceIdentifier serviceIdentifier, CallSiteChain callSiteChain, Int32 slot)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteFactory.TryCreateExact(ServiceIdentifier serviceIdentifier, CallSiteChain callSiteChain)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteFactory.CreateCallSite(ServiceIdentifier serviceIdentifier, CallSiteChain callSiteChain)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteFactory.CreateArgumentCallSites(ServiceIdentifier serviceIdentifier, Type implementationType, CallSiteChain callSiteChain, ParameterInfo[] parameters, Boolean throwIfCallSiteNotFound)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteFactory.CreateConstructorCallSite(ResultCache lifetime, ServiceIdentifier serviceIdentifier, Type implementationType, CallSiteChain callSiteChain)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteFactory.TryCreateExact(ServiceDescriptor descriptor, ServiceIdentifier serviceIdentifier, CallSiteChain callSiteChain, Int32 slot)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteFactory.GetCallSite(ServiceDescriptor serviceDescriptor, CallSiteChain callSiteChain)
   at Microsoft.Extensions.DependencyInjection.ServiceProvider.ValidateService(ServiceDescriptor descriptor)
   --- End of inner exception stack trace ---
   at Microsoft.Extensions.DependencyInjection.ServiceProvider.ValidateService(ServiceDescriptor descriptor)
   at Microsoft.Extensions.DependencyInjection.ServiceProvider..ctor(ICollection`1 serviceDescriptors, ServiceProviderOptions options)
   --- End of inner exception stack trace ---
   at Microsoft.Extensions.DependencyInjection.ServiceProvider..ctor(ICollection`1 serviceDescriptors, ServiceProviderOptions options)
   at Microsoft.Extensions.DependencyInjection.ServiceCollectionContainerBuilderExtensions.BuildServiceProvider(IServiceCollection services, ServiceProviderOptions options)
   at Microsoft.Extensions.Hosting.HostApplicationBuilder.Build()
   at Microsoft.AspNetCore.Builder.WebApplicationBuilder.Build()
   at Program.<Main>$(String[] args) in /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/WebApi/WhimLabAI.WebApi/Program.cs:line 688
 ---> (Inner Exception #1) System.InvalidOperationException: Error while validating the service descriptor 'ServiceType: Microsoft.AspNetCore.Authorization.IAuthorizationHandlerProvider Lifetime: Transient ImplementationType: Microsoft.AspNetCore.Authorization.DefaultAuthorizationHandlerProvider': Unable to resolve service for type 'WhimLabAI.Domain.Repositories.IAdminUserRepository' while attempting to activate 'WhimLabAI.WebApi.Authorization.PermissionAuthorizationHandler'.
 ---> System.InvalidOperationException: Unable to resolve service for type 'WhimLabAI.Domain.Repositories.IAdminUserRepository' while attempting to activate 'WhimLabAI.WebApi.Authorization.PermissionAuthorizationHandler'.
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteFactory.CreateArgumentCallSites(ServiceIdentifier serviceIdentifier, Type implementationType, CallSiteChain callSiteChain, ParameterInfo[] parameters, Boolean throwIfCallSiteNotFound)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteFactory.CreateConstructorCallSite(ResultCache lifetime, ServiceIdentifier serviceIdentifier, Type implementationType, CallSiteChain callSiteChain)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteFactory.TryCreateExact(ServiceDescriptor descriptor, ServiceIdentifier serviceIdentifier, CallSiteChain callSiteChain, Int32 slot)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteFactory.TryCreateEnumerable(ServiceIdentifier serviceIdentifier, CallSiteChain callSiteChain)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteFactory.CreateCallSite(ServiceIdentifier serviceIdentifier, CallSiteChain callSiteChain)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteFactory.CreateArgumentCallSites(ServiceIdentifier serviceIdentifier, Type implementationType, CallSiteChain callSiteChain, ParameterInfo[] parameters, Boolean throwIfCallSiteNotFound)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteFactory.CreateConstructorCallSite(ResultCache lifetime, ServiceIdentifier serviceIdentifier, Type implementationType, CallSiteChain callSiteChain)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteFactory.TryCreateExact(ServiceDescriptor descriptor, ServiceIdentifier serviceIdentifier, CallSiteChain callSiteChain, Int32 slot)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteFactory.GetCallSite(ServiceDescriptor serviceDescriptor, CallSiteChain callSiteChain)
   at Microsoft.Extensions.DependencyInjection.ServiceProvider.ValidateService(ServiceDescriptor descriptor)
   --- End of inner exception stack trace ---
   at Microsoft.Extensions.DependencyInjection.ServiceProvider.ValidateService(ServiceDescriptor descriptor)
   at Microsoft.Extensions.DependencyInjection.ServiceProvider..ctor(ICollection`1 serviceDescriptors, ServiceProviderOptions options)<---

 ---> (Inner Exception #2) System.InvalidOperationException: Error while validating the service descriptor 'ServiceType: Microsoft.AspNetCore.Authorization.Policy.IPolicyEvaluator Lifetime: Transient ImplementationType: Microsoft.AspNetCore.Authorization.Policy.PolicyEvaluator': Unable to resolve service for type 'WhimLabAI.Domain.Repositories.IAdminUserRepository' while attempting to activate 'WhimLabAI.WebApi.Authorization.PermissionAuthorizationHandler'.
 ---> System.InvalidOperationException: Unable to resolve service for type 'WhimLabAI.Domain.Repositories.IAdminUserRepository' while attempting to activate 'WhimLabAI.WebApi.Authorization.PermissionAuthorizationHandler'.
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteFactory.CreateArgumentCallSites(ServiceIdentifier serviceIdentifier, Type implementationType, CallSiteChain callSiteChain, ParameterInfo[] parameters, Boolean throwIfCallSiteNotFound)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteFactory.CreateConstructorCallSite(ResultCache lifetime, ServiceIdentifier serviceIdentifier, Type implementationType, CallSiteChain callSiteChain)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteFactory.TryCreateExact(ServiceDescriptor descriptor, ServiceIdentifier serviceIdentifier, CallSiteChain callSiteChain, Int32 slot)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteFactory.TryCreateEnumerable(ServiceIdentifier serviceIdentifier, CallSiteChain callSiteChain)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteFactory.CreateCallSite(ServiceIdentifier serviceIdentifier, CallSiteChain callSiteChain)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteFactory.CreateArgumentCallSites(ServiceIdentifier serviceIdentifier, Type implementationType, CallSiteChain callSiteChain, ParameterInfo[] parameters, Boolean throwIfCallSiteNotFound)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteFactory.CreateConstructorCallSite(ResultCache lifetime, ServiceIdentifier serviceIdentifier, Type implementationType, CallSiteChain callSiteChain)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteFactory.TryCreateExact(ServiceDescriptor descriptor, ServiceIdentifier serviceIdentifier, CallSiteChain callSiteChain, Int32 slot)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteFactory.TryCreateExact(ServiceIdentifier serviceIdentifier, CallSiteChain callSiteChain)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteFactory.CreateCallSite(ServiceIdentifier serviceIdentifier, CallSiteChain callSiteChain)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteFactory.CreateArgumentCallSites(ServiceIdentifier serviceIdentifier, Type implementationType, CallSiteChain callSiteChain, ParameterInfo[] parameters, Boolean throwIfCallSiteNotFound)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteFactory.CreateConstructorCallSite(ResultCache lifetime, ServiceIdentifier serviceIdentifier, Type implementationType, CallSiteChain callSiteChain)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteFactory.TryCreateExact(ServiceDescriptor descriptor, ServiceIdentifier serviceIdentifier, CallSiteChain callSiteChain, Int32 slot)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteFactory.TryCreateExact(ServiceIdentifier serviceIdentifier, CallSiteChain callSiteChain)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteFactory.CreateCallSite(ServiceIdentifier serviceIdentifier, CallSiteChain callSiteChain)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteFactory.CreateArgumentCallSites(ServiceIdentifier serviceIdentifier, Type implementationType, CallSiteChain callSiteChain, ParameterInfo[] parameters, Boolean throwIfCallSiteNotFound)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteFactory.CreateConstructorCallSite(ResultCache lifetime, ServiceIdentifier serviceIdentifier, Type implementationType, CallSiteChain callSiteChain)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteFactory.TryCreateExact(ServiceDescriptor descriptor, ServiceIdentifier serviceIdentifier, CallSiteChain callSiteChain, Int32 slot)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteFactory.GetCallSite(ServiceDescriptor serviceDescriptor, CallSiteChain callSiteChain)
   at Microsoft.Extensions.DependencyInjection.ServiceProvider.ValidateService(ServiceDescriptor descriptor)
   --- End of inner exception stack trace ---
   at Microsoft.Extensions.DependencyInjection.ServiceProvider.ValidateService(ServiceDescriptor descriptor)
   at Microsoft.Extensions.DependencyInjection.ServiceProvider..ctor(ICollection`1 serviceDescriptors, ServiceProviderOptions options)<---

 ---> (Inner Exception #3) System.InvalidOperationException: Error while validating the service descriptor 'ServiceType: WhimLabAI.Infrastructure.ApiSecurity.IRateLimitingService Lifetime: Singleton ImplementationType: WhimLabAI.Infrastructure.ApiSecurity.RateLimitingService': Unable to resolve service for type 'Microsoft.Extensions.Caching.Distributed.IDistributedCache' while attempting to activate 'WhimLabAI.Infrastructure.ApiSecurity.RateLimitingService'.
 ---> System.InvalidOperationException: Unable to resolve service for type 'Microsoft.Extensions.Caching.Distributed.IDistributedCache' while attempting to activate 'WhimLabAI.Infrastructure.ApiSecurity.RateLimitingService'.
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteFactory.CreateArgumentCallSites(ServiceIdentifier serviceIdentifier, Type implementationType, CallSiteChain callSiteChain, ParameterInfo[] parameters, Boolean throwIfCallSiteNotFound)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteFactory.CreateConstructorCallSite(ResultCache lifetime, ServiceIdentifier serviceIdentifier, Type implementationType, CallSiteChain callSiteChain)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteFactory.TryCreateExact(ServiceDescriptor descriptor, ServiceIdentifier serviceIdentifier, CallSiteChain callSiteChain, Int32 slot)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteFactory.GetCallSite(ServiceDescriptor serviceDescriptor, CallSiteChain callSiteChain)
   at Microsoft.Extensions.DependencyInjection.ServiceProvider.ValidateService(ServiceDescriptor descriptor)
   --- End of inner exception stack trace ---
   at Microsoft.Extensions.DependencyInjection.ServiceProvider.ValidateService(ServiceDescriptor descriptor)
   at Microsoft.Extensions.DependencyInjection.ServiceProvider..ctor(ICollection`1 serviceDescriptors, ServiceProviderOptions options)<---

 ---> (Inner Exception #4) System.InvalidOperationException: Error while validating the service descriptor 'ServiceType: WhimLabAI.Infrastructure.ApiSecurity.IApiKeyManagementService Lifetime: Scoped ImplementationType: WhimLabAI.Infrastructure.ApiSecurity.ApiKeyManagementService': Unable to resolve service for type 'WhimLabAI.Infrastructure.Data.WhimLabAIDbContext' while attempting to activate 'WhimLabAI.Infrastructure.ApiSecurity.ApiKeyManagementService'.
 ---> System.InvalidOperationException: Unable to resolve service for type 'WhimLabAI.Infrastructure.Data.WhimLabAIDbContext' while attempting to activate 'WhimLabAI.Infrastructure.ApiSecurity.ApiKeyManagementService'.
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteFactory.CreateArgumentCallSites(ServiceIdentifier serviceIdentifier, Type implementationType, CallSiteChain callSiteChain, ParameterInfo[] parameters, Boolean throwIfCallSiteNotFound)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteFactory.CreateConstructorCallSite(ResultCache lifetime, ServiceIdentifier serviceIdentifier, Type implementationType, CallSiteChain callSiteChain)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteFactory.TryCreateExact(ServiceDescriptor descriptor, ServiceIdentifier serviceIdentifier, CallSiteChain callSiteChain, Int32 slot)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteFactory.GetCallSite(ServiceDescriptor serviceDescriptor, CallSiteChain callSiteChain)
   at Microsoft.Extensions.DependencyInjection.ServiceProvider.ValidateService(ServiceDescriptor descriptor)
   --- End of inner exception stack trace ---
   at Microsoft.Extensions.DependencyInjection.ServiceProvider.ValidateService(ServiceDescriptor descriptor)
   at Microsoft.Extensions.DependencyInjection.ServiceProvider..ctor(ICollection`1 serviceDescriptors, ServiceProviderOptions options)<---

 ---> (Inner Exception #5) System.InvalidOperationException: Error while validating the service descriptor 'ServiceType: WhimLabAI.Infrastructure.ApiSecurity.ApiKeyAuthenticationHandler Lifetime: Transient ImplementationType: WhimLabAI.Infrastructure.ApiSecurity.ApiKeyAuthenticationHandler': Unable to resolve service for type 'WhimLabAI.Infrastructure.Data.WhimLabAIDbContext' while attempting to activate 'WhimLabAI.Infrastructure.ApiSecurity.ApiKeyAuthenticationHandler'.
 ---> System.InvalidOperationException: Unable to resolve service for type 'WhimLabAI.Infrastructure.Data.WhimLabAIDbContext' while attempting to activate 'WhimLabAI.Infrastructure.ApiSecurity.ApiKeyAuthenticationHandler'.
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteFactory.CreateArgumentCallSites(ServiceIdentifier serviceIdentifier, Type implementationType, CallSiteChain callSiteChain, ParameterInfo[] parameters, Boolean throwIfCallSiteNotFound)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteFactory.CreateConstructorCallSite(ResultCache lifetime, ServiceIdentifier serviceIdentifier, Type implementationType, CallSiteChain callSiteChain)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteFactory.TryCreateExact(ServiceDescriptor descriptor, ServiceIdentifier serviceIdentifier, CallSiteChain callSiteChain, Int32 slot)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteFactory.GetCallSite(ServiceDescriptor serviceDescriptor, CallSiteChain callSiteChain)
   at Microsoft.Extensions.DependencyInjection.ServiceProvider.ValidateService(ServiceDescriptor descriptor)
   --- End of inner exception stack trace ---
   at Microsoft.Extensions.DependencyInjection.ServiceProvider.ValidateService(ServiceDescriptor descriptor)
   at Microsoft.Extensions.DependencyInjection.ServiceProvider..ctor(ICollection`1 serviceDescriptors, ServiceProviderOptions options)<---

 ---> (Inner Exception #6) System.InvalidOperationException: Error while validating the service descriptor 'ServiceType: Microsoft.AspNetCore.Authorization.IAuthorizationHandler Lifetime: Scoped ImplementationType: WhimLabAI.WebApi.Authorization.PermissionAuthorizationHandler': Unable to resolve service for type 'WhimLabAI.Domain.Repositories.IAdminUserRepository' while attempting to activate 'WhimLabAI.WebApi.Authorization.PermissionAuthorizationHandler'.
 ---> System.InvalidOperationException: Unable to resolve service for type 'WhimLabAI.Domain.Repositories.IAdminUserRepository' while attempting to activate 'WhimLabAI.WebApi.Authorization.PermissionAuthorizationHandler'.
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteFactory.CreateArgumentCallSites(ServiceIdentifier serviceIdentifier, Type implementationType, CallSiteChain callSiteChain, ParameterInfo[] parameters, Boolean throwIfCallSiteNotFound)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteFactory.CreateConstructorCallSite(ResultCache lifetime, ServiceIdentifier serviceIdentifier, Type implementationType, CallSiteChain callSiteChain)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteFactory.TryCreateExact(ServiceDescriptor descriptor, ServiceIdentifier serviceIdentifier, CallSiteChain callSiteChain, Int32 slot)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteFactory.GetCallSite(ServiceDescriptor serviceDescriptor, CallSiteChain callSiteChain)
   at Microsoft.Extensions.DependencyInjection.ServiceProvider.ValidateService(ServiceDescriptor descriptor)
   --- End of inner exception stack trace ---
   at Microsoft.Extensions.DependencyInjection.ServiceProvider.ValidateService(ServiceDescriptor descriptor)
   at Microsoft.Extensions.DependencyInjection.ServiceProvider..ctor(ICollection`1 serviceDescriptors, ServiceProviderOptions options)<---

2025-07-21 01:00:13.041 +08:00 [INF] Starting WhimLabAI Web API
2025-07-21 01:00:13.356 +08:00 [INF] WebApplication builder created
2025-07-21 01:00:13.356 +08:00 [INF] Loading configuration files...
2025-07-21 01:00:13.357 +08:00 [INF] Environment variables loaded
2025-07-21 01:00:13.367 +08:00 [INF] Environment variable replacements loaded
2025-07-21 01:00:13.367 +08:00 [INF] Configuration loaded
2025-07-21 01:00:13.372 +08:00 [INF] Registering Infrastructure services...
2025-07-21 01:00:13.676 +08:00 [INF] Infrastructure services registered
2025-07-21 01:00:13.676 +08:00 [INF] Registering Application services...
2025-07-21 01:00:13.678 +08:00 [INF] Application services registered
2025-07-21 01:00:13.678 +08:00 [INF] Registering RealtimeNotificationService...
2025-07-21 01:00:13.678 +08:00 [INF] RealtimeNotificationService registered
2025-07-21 01:00:13.678 +08:00 [INF] Registering security services...
2025-07-21 01:00:13.679 +08:00 [INF] Security services registered
2025-07-21 01:00:13.679 +08:00 [INF] Configuring API versioning...
2025-07-21 01:00:13.679 +08:00 [INF] API versioning configured
2025-07-21 01:00:13.679 +08:00 [INF] Configuring input validation...
2025-07-21 01:00:13.679 +08:00 [INF] Input validation configured
2025-07-21 01:00:13.679 +08:00 [INF] Configuring rate limiting...
2025-07-21 01:00:13.680 +08:00 [INF] Rate limiting configured
2025-07-21 01:00:13.683 +08:00 [INF] All services registered, building application...
2025-07-21 01:00:13.683 +08:00 [INF] Calling builder.Build()...
2025-07-21 01:00:13.684 +08:00 [INF] About to call builder.Build() - checking for blocking services...
2025-07-21 01:05:56.317 +08:00 [INF] Starting WhimLabAI Web API
2025-07-21 01:05:56.837 +08:00 [INF] WebApplication builder created
2025-07-21 01:05:56.837 +08:00 [INF] Loading configuration files...
2025-07-21 01:05:56.838 +08:00 [INF] Environment variables loaded
2025-07-21 01:05:56.850 +08:00 [INF] Environment variable replacements loaded
2025-07-21 01:05:56.850 +08:00 [INF] Configuration loaded
2025-07-21 01:05:56.855 +08:00 [INF] Registering Infrastructure services...
2025-07-21 01:05:57.150 +08:00 [INF] Infrastructure services registered
2025-07-21 01:05:57.150 +08:00 [INF] Registering Application services...
2025-07-21 01:05:57.152 +08:00 [INF] Application services registered
2025-07-21 01:05:57.152 +08:00 [INF] Registering RealtimeNotificationService...
2025-07-21 01:05:57.152 +08:00 [INF] RealtimeNotificationService registered
2025-07-21 01:05:57.152 +08:00 [INF] Registering security services...
2025-07-21 01:05:57.153 +08:00 [INF] Security services registered
2025-07-21 01:05:57.153 +08:00 [INF] Configuring API versioning...
2025-07-21 01:05:57.153 +08:00 [INF] API versioning configured
2025-07-21 01:05:57.153 +08:00 [INF] Configuring input validation...
2025-07-21 01:05:57.154 +08:00 [INF] Input validation configured
2025-07-21 01:05:57.154 +08:00 [INF] Configuring rate limiting...
2025-07-21 01:05:57.154 +08:00 [INF] Rate limiting configured
2025-07-21 01:05:57.160 +08:00 [INF] All services registered, building application...
2025-07-21 01:05:57.160 +08:00 [INF] Calling builder.Build()...
2025-07-21 01:05:57.160 +08:00 [INF] About to call builder.Build() - checking for blocking services...
2025-07-21 01:11:33.856 +08:00 [INF] Starting WhimLabAI Web API
2025-07-21 01:11:34.371 +08:00 [INF] WebApplication builder created
2025-07-21 01:11:34.371 +08:00 [INF] Loading configuration files...
2025-07-21 01:11:34.372 +08:00 [INF] Environment variables loaded
2025-07-21 01:11:34.383 +08:00 [INF] Environment variable replacements loaded
2025-07-21 01:11:34.384 +08:00 [INF] Configuration loaded
2025-07-21 01:11:34.390 +08:00 [INF] Registering Infrastructure services...
2025-07-21 01:11:34.577 +08:00 [INF] Infrastructure services registered
2025-07-21 01:11:34.577 +08:00 [INF] Registering Application services...
2025-07-21 01:11:34.582 +08:00 [INF] Application services registered
2025-07-21 01:11:34.582 +08:00 [INF] Registering RealtimeNotificationService...
2025-07-21 01:11:34.582 +08:00 [INF] RealtimeNotificationService registered
2025-07-21 01:11:34.583 +08:00 [INF] Registering security services...
2025-07-21 01:11:34.584 +08:00 [INF] Security services registered
2025-07-21 01:11:34.584 +08:00 [INF] Configuring API versioning...
2025-07-21 01:11:34.584 +08:00 [INF] API versioning configured
2025-07-21 01:11:34.584 +08:00 [INF] Configuring input validation...
2025-07-21 01:11:34.584 +08:00 [INF] Input validation configured
2025-07-21 01:11:34.584 +08:00 [INF] Configuring rate limiting...
2025-07-21 01:11:34.584 +08:00 [INF] Rate limiting configured
2025-07-21 01:11:34.591 +08:00 [INF] All services registered, building application...
2025-07-21 01:11:34.591 +08:00 [INF] Calling builder.Build()...
2025-07-21 01:11:34.591 +08:00 [INF] About to call builder.Build() - checking for blocking services...
2025-07-21 01:13:40.703 +08:00 [INF] Starting WhimLabAI Web API
2025-07-21 01:13:41.015 +08:00 [INF] WebApplication builder created
2025-07-21 01:13:41.015 +08:00 [INF] Loading configuration files...
2025-07-21 01:13:41.016 +08:00 [INF] Environment variables loaded
2025-07-21 01:13:41.026 +08:00 [INF] Environment variable replacements loaded
2025-07-21 01:13:41.026 +08:00 [INF] Configuration loaded
2025-07-21 01:13:41.030 +08:00 [INF] Registering Infrastructure services...
2025-07-21 01:13:41.213 +08:00 [INF] Infrastructure services registered
2025-07-21 01:13:41.213 +08:00 [INF] Registering Application services...
2025-07-21 01:13:41.218 +08:00 [INF] Application services registered
2025-07-21 01:13:41.218 +08:00 [INF] Registering RealtimeNotificationService...
2025-07-21 01:13:41.218 +08:00 [INF] RealtimeNotificationService registered
2025-07-21 01:13:41.218 +08:00 [INF] Registering security services...
2025-07-21 01:13:41.218 +08:00 [INF] Security services registered
2025-07-21 01:13:41.218 +08:00 [INF] Configuring API versioning...
2025-07-21 01:13:41.219 +08:00 [INF] API versioning configured
2025-07-21 01:13:41.219 +08:00 [INF] Configuring input validation...
2025-07-21 01:13:41.219 +08:00 [INF] Input validation configured
2025-07-21 01:13:41.219 +08:00 [INF] Configuring rate limiting...
2025-07-21 01:13:41.219 +08:00 [INF] Rate limiting configured
2025-07-21 01:13:41.221 +08:00 [INF] Temporarily disabling Hangfire for debugging...
2025-07-21 01:13:41.221 +08:00 [INF] Hangfire disabled for debugging
2025-07-21 01:13:41.222 +08:00 [INF] All services registered, building application...
2025-07-21 01:13:41.222 +08:00 [INF] Calling builder.Build()...
2025-07-21 01:13:41.222 +08:00 [INF] About to call builder.Build() - checking for blocking services...
2025-07-21 01:15:53.792 +08:00 [INF] Starting WhimLabAI Web API
2025-07-21 01:15:54.123 +08:00 [INF] WebApplication builder created
2025-07-21 01:15:54.123 +08:00 [INF] Loading configuration files...
2025-07-21 01:15:54.124 +08:00 [INF] Environment variables loaded
2025-07-21 01:15:54.134 +08:00 [INF] Environment variable replacements loaded
2025-07-21 01:15:54.134 +08:00 [INF] Configuration loaded
2025-07-21 01:15:54.139 +08:00 [INF] Registering Infrastructure services...
2025-07-21 01:15:54.314 +08:00 [INF] Infrastructure services registered
2025-07-21 01:15:54.314 +08:00 [INF] Registering Application services...
2025-07-21 01:15:54.318 +08:00 [INF] Application services registered
2025-07-21 01:15:54.318 +08:00 [INF] Registering RealtimeNotificationService...
2025-07-21 01:15:54.318 +08:00 [INF] RealtimeNotificationService registered
2025-07-21 01:15:54.318 +08:00 [INF] Registering security services...
2025-07-21 01:15:54.318 +08:00 [INF] Security services registered
2025-07-21 01:15:54.318 +08:00 [INF] Configuring API versioning...
2025-07-21 01:15:54.318 +08:00 [INF] API versioning configured
2025-07-21 01:15:54.318 +08:00 [INF] Configuring input validation...
2025-07-21 01:15:54.319 +08:00 [INF] Input validation configured
2025-07-21 01:15:54.319 +08:00 [INF] Configuring rate limiting...
2025-07-21 01:15:54.319 +08:00 [INF] Rate limiting configured
2025-07-21 01:15:54.319 +08:00 [INF] Temporarily disabling health checks for debugging...
2025-07-21 01:15:54.319 +08:00 [INF] Health checks disabled for debugging
2025-07-21 01:15:54.320 +08:00 [INF] Temporarily disabling Hangfire for debugging...
2025-07-21 01:15:54.320 +08:00 [INF] Hangfire disabled for debugging
2025-07-21 01:15:54.321 +08:00 [INF] All services registered, building application...
2025-07-21 01:15:54.321 +08:00 [INF] Calling builder.Build()...
2025-07-21 01:15:54.321 +08:00 [INF] About to call builder.Build() - checking for blocking services...
2025-07-21 01:18:53.830 +08:00 [INF] Starting WhimLabAI Web API
2025-07-21 01:18:54.161 +08:00 [INF] WebApplication builder created
2025-07-21 01:18:54.161 +08:00 [INF] Loading configuration files...
2025-07-21 01:18:54.162 +08:00 [INF] Environment variables loaded
2025-07-21 01:18:54.173 +08:00 [INF] Environment variable replacements loaded
2025-07-21 01:18:54.173 +08:00 [INF] Configuration loaded
2025-07-21 01:18:54.177 +08:00 [INF] Registering Infrastructure services...
2025-07-21 01:18:54.368 +08:00 [INF] Infrastructure services registered
2025-07-21 01:18:54.368 +08:00 [INF] Registering Application services...
2025-07-21 01:18:54.372 +08:00 [INF] Application services registered
2025-07-21 01:18:54.372 +08:00 [INF] Registering RealtimeNotificationService...
2025-07-21 01:18:54.372 +08:00 [INF] RealtimeNotificationService registered
2025-07-21 01:18:54.372 +08:00 [INF] Registering security services...
2025-07-21 01:18:54.374 +08:00 [INF] Security services registered
2025-07-21 01:18:54.374 +08:00 [INF] Configuring API versioning...
2025-07-21 01:18:54.374 +08:00 [INF] API versioning configured
2025-07-21 01:18:54.374 +08:00 [INF] Configuring input validation...
2025-07-21 01:18:54.374 +08:00 [INF] Input validation configured
2025-07-21 01:18:54.374 +08:00 [INF] Configuring rate limiting...
2025-07-21 01:18:54.375 +08:00 [INF] Rate limiting configured
2025-07-21 01:18:54.375 +08:00 [INF] Temporarily disabling health checks for debugging...
2025-07-21 01:18:54.375 +08:00 [INF] Health checks disabled for debugging
2025-07-21 01:18:54.377 +08:00 [INF] Temporarily disabling Hangfire for debugging...
2025-07-21 01:18:54.377 +08:00 [INF] Hangfire disabled for debugging
2025-07-21 01:18:54.378 +08:00 [INF] All services registered, building application...
2025-07-21 01:18:54.378 +08:00 [INF] Calling builder.Build()...
2025-07-21 01:18:54.378 +08:00 [INF] About to call builder.Build() - checking for blocking services...
2025-07-21 01:20:51.857 +08:00 [INF] Starting WhimLabAI Web API
2025-07-21 01:20:52.185 +08:00 [INF] WebApplication builder created
2025-07-21 01:20:52.185 +08:00 [INF] Loading configuration files...
2025-07-21 01:20:52.185 +08:00 [INF] Environment variables loaded
2025-07-21 01:20:52.196 +08:00 [INF] Environment variable replacements loaded
2025-07-21 01:20:52.196 +08:00 [INF] Configuration loaded
2025-07-21 01:20:52.201 +08:00 [INF] Registering Infrastructure services...
2025-07-21 01:20:52.391 +08:00 [INF] Infrastructure services registered
2025-07-21 01:20:52.391 +08:00 [INF] Registering Application services...
2025-07-21 01:20:52.395 +08:00 [INF] Application services registered
2025-07-21 01:20:52.395 +08:00 [INF] Registering RealtimeNotificationService...
2025-07-21 01:20:52.395 +08:00 [INF] RealtimeNotificationService registered
2025-07-21 01:20:52.395 +08:00 [INF] Registering security services...
2025-07-21 01:20:52.396 +08:00 [INF] Security services registered
2025-07-21 01:20:52.396 +08:00 [INF] Configuring API versioning...
2025-07-21 01:20:52.396 +08:00 [INF] API versioning configured
2025-07-21 01:20:52.396 +08:00 [INF] Configuring input validation...
2025-07-21 01:20:52.396 +08:00 [INF] Input validation configured
2025-07-21 01:20:52.396 +08:00 [INF] Configuring rate limiting...
2025-07-21 01:20:52.396 +08:00 [INF] Rate limiting configured
2025-07-21 01:20:52.397 +08:00 [INF] Temporarily disabling health checks for debugging...
2025-07-21 01:20:52.397 +08:00 [INF] Health checks disabled for debugging
2025-07-21 01:20:52.400 +08:00 [INF] Temporarily disabling Hangfire for debugging...
2025-07-21 01:20:52.400 +08:00 [INF] Hangfire disabled for debugging
2025-07-21 01:20:52.401 +08:00 [INF] All services registered, building application...
2025-07-21 01:20:52.401 +08:00 [INF] Calling builder.Build()...
2025-07-21 01:20:52.401 +08:00 [INF] About to call builder.Build() - checking for blocking services...
2025-07-21 01:23:16.557 +08:00 [INF] Starting WhimLabAI Web API
2025-07-21 01:23:16.914 +08:00 [INF] WebApplication builder created
2025-07-21 01:23:16.914 +08:00 [INF] Loading configuration files...
2025-07-21 01:23:16.915 +08:00 [INF] Environment variables loaded
2025-07-21 01:23:16.926 +08:00 [INF] Environment variable replacements loaded
2025-07-21 01:23:16.926 +08:00 [INF] Configuration loaded
2025-07-21 01:23:16.931 +08:00 [INF] Registering Infrastructure services...
2025-07-21 01:23:17.126 +08:00 [INF] Infrastructure services registered
2025-07-21 01:23:17.126 +08:00 [INF] Registering Application services...
2025-07-21 01:23:17.133 +08:00 [INF] Application services registered
2025-07-21 01:23:17.133 +08:00 [INF] Registering RealtimeNotificationService...
2025-07-21 01:23:17.133 +08:00 [INF] RealtimeNotificationService registered
2025-07-21 01:23:17.133 +08:00 [INF] Registering security services...
2025-07-21 01:23:17.134 +08:00 [INF] Security services registered
2025-07-21 01:23:17.134 +08:00 [INF] Configuring API versioning...
2025-07-21 01:23:17.134 +08:00 [INF] API versioning configured
2025-07-21 01:23:17.134 +08:00 [INF] Configuring input validation...
2025-07-21 01:23:17.134 +08:00 [INF] Input validation configured
2025-07-21 01:23:17.134 +08:00 [INF] Configuring rate limiting...
2025-07-21 01:23:17.135 +08:00 [INF] Rate limiting configured
2025-07-21 01:23:17.137 +08:00 [INF] Temporarily disabling health checks for debugging...
2025-07-21 01:23:17.137 +08:00 [INF] Health checks disabled for debugging
2025-07-21 01:23:17.138 +08:00 [INF] Temporarily disabling Hangfire for debugging...
2025-07-21 01:23:17.138 +08:00 [INF] Hangfire disabled for debugging
2025-07-21 01:23:17.140 +08:00 [INF] All services registered, building application...
2025-07-21 01:23:17.140 +08:00 [INF] Calling builder.Build()...
2025-07-21 01:23:17.140 +08:00 [INF] About to call builder.Build() - checking for blocking services...
