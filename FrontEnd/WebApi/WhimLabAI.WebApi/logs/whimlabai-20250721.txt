2025-07-21 00:01:34.049 +08:00 [INF] Starting WhimLabAI Web API
2025-07-21 00:01:34.369 +08:00 [INF] WebApplication builder created
2025-07-21 00:01:34.370 +08:00 [INF] Loading configuration files...
2025-07-21 00:01:34.370 +08:00 [INF] Environment variables loaded
2025-07-21 00:01:34.382 +08:00 [INF] Environment variable replacements loaded
2025-07-21 00:01:34.382 +08:00 [INF] Configuration loaded
2025-07-21 00:01:34.389 +08:00 [INF] Registering Infrastructure services...
2025-07-21 00:01:34.678 +08:00 [INF] Infrastructure services registered
2025-07-21 00:01:34.678 +08:00 [INF] Registering Application services...
2025-07-21 00:01:34.681 +08:00 [INF] Application services registered
2025-07-21 00:01:34.681 +08:00 [INF] Registering RealtimeNotificationService...
2025-07-21 00:01:34.681 +08:00 [INF] RealtimeNotificationService registered
2025-07-21 00:01:34.681 +08:00 [INF] Registering security services...
2025-07-21 00:01:34.682 +08:00 [INF] Security services registered
2025-07-21 00:01:34.682 +08:00 [INF] Configuring API versioning...
2025-07-21 00:01:34.682 +08:00 [INF] API versioning configured
2025-07-21 00:01:34.682 +08:00 [INF] Configuring input validation...
2025-07-21 00:01:34.683 +08:00 [INF] Input validation configured
2025-07-21 00:01:34.683 +08:00 [INF] Configuring rate limiting...
2025-07-21 00:01:34.683 +08:00 [INF] Rate limiting configured
2025-07-21 00:01:34.687 +08:00 [INF] All services registered, building application...
2025-07-21 00:01:34.687 +08:00 [INF] Calling builder.Build()...
2025-07-21 00:02:39.920 +08:00 [INF] Starting WhimLabAI Web API
2025-07-21 00:02:40.240 +08:00 [INF] WebApplication builder created
2025-07-21 00:02:40.240 +08:00 [INF] Loading configuration files...
2025-07-21 00:02:40.241 +08:00 [INF] Environment variables loaded
2025-07-21 00:02:40.252 +08:00 [INF] Environment variable replacements loaded
2025-07-21 00:02:40.252 +08:00 [INF] Configuration loaded
2025-07-21 00:02:40.258 +08:00 [INF] Registering Infrastructure services...
2025-07-21 00:02:40.535 +08:00 [INF] Infrastructure services registered
2025-07-21 00:02:40.536 +08:00 [INF] Registering Application services...
2025-07-21 00:02:40.537 +08:00 [INF] Application services registered
2025-07-21 00:02:40.537 +08:00 [INF] Registering RealtimeNotificationService...
2025-07-21 00:02:40.538 +08:00 [INF] RealtimeNotificationService registered
2025-07-21 00:02:40.538 +08:00 [INF] Registering security services...
2025-07-21 00:02:40.538 +08:00 [INF] Security services registered
2025-07-21 00:02:40.539 +08:00 [INF] Configuring API versioning...
2025-07-21 00:02:40.539 +08:00 [INF] API versioning configured
2025-07-21 00:02:40.539 +08:00 [INF] Configuring input validation...
2025-07-21 00:02:40.539 +08:00 [INF] Input validation configured
2025-07-21 00:02:40.539 +08:00 [INF] Configuring rate limiting...
2025-07-21 00:02:40.539 +08:00 [INF] Rate limiting configured
2025-07-21 00:02:40.543 +08:00 [INF] All services registered, building application...
2025-07-21 00:02:40.543 +08:00 [INF] Calling builder.Build()...
2025-07-21 00:08:30.866 +08:00 [INF] Starting WhimLabAI Web API
2025-07-21 00:08:31.174 +08:00 [INF] WebApplication builder created
2025-07-21 00:08:31.175 +08:00 [INF] Loading configuration files...
2025-07-21 00:08:31.175 +08:00 [INF] Environment variables loaded
2025-07-21 00:08:31.185 +08:00 [INF] Environment variable replacements loaded
2025-07-21 00:08:31.185 +08:00 [INF] Configuration loaded
2025-07-21 00:08:31.190 +08:00 [INF] Registering Infrastructure services...
2025-07-21 00:08:31.452 +08:00 [INF] Infrastructure services registered
2025-07-21 00:08:31.452 +08:00 [INF] Registering Application services...
2025-07-21 00:08:31.454 +08:00 [INF] Application services registered
2025-07-21 00:08:31.454 +08:00 [INF] Registering RealtimeNotificationService...
2025-07-21 00:08:31.454 +08:00 [INF] RealtimeNotificationService registered
2025-07-21 00:08:31.454 +08:00 [INF] Registering security services...
2025-07-21 00:08:31.455 +08:00 [INF] Security services registered
2025-07-21 00:08:31.455 +08:00 [INF] Configuring API versioning...
2025-07-21 00:08:31.455 +08:00 [INF] API versioning configured
2025-07-21 00:08:31.455 +08:00 [INF] Configuring input validation...
2025-07-21 00:08:31.455 +08:00 [INF] Input validation configured
2025-07-21 00:08:31.455 +08:00 [INF] Configuring rate limiting...
2025-07-21 00:08:31.456 +08:00 [INF] Rate limiting configured
2025-07-21 00:08:31.459 +08:00 [INF] All services registered, building application...
2025-07-21 00:08:31.459 +08:00 [INF] Calling builder.Build()...
2025-07-21 00:12:53.543 +08:00 [INF] Starting WhimLabAI Web API
2025-07-21 00:12:53.876 +08:00 [INF] WebApplication builder created
2025-07-21 00:12:53.876 +08:00 [INF] Loading configuration files...
2025-07-21 00:12:53.876 +08:00 [INF] Environment variables loaded
2025-07-21 00:12:53.887 +08:00 [INF] Environment variable replacements loaded
2025-07-21 00:12:53.887 +08:00 [INF] Configuration loaded
2025-07-21 00:12:53.891 +08:00 [INF] Registering Infrastructure services...
2025-07-21 00:12:54.104 +08:00 [INF] Infrastructure services registered
2025-07-21 00:12:54.104 +08:00 [INF] Registering Application services...
2025-07-21 00:12:54.106 +08:00 [INF] Application services registered
2025-07-21 00:12:54.106 +08:00 [INF] Registering RealtimeNotificationService...
2025-07-21 00:12:54.106 +08:00 [INF] RealtimeNotificationService registered
2025-07-21 00:12:54.106 +08:00 [INF] Registering security services...
2025-07-21 00:12:54.107 +08:00 [INF] Security services registered
2025-07-21 00:12:54.107 +08:00 [INF] Configuring API versioning...
2025-07-21 00:12:54.107 +08:00 [INF] API versioning configured
2025-07-21 00:12:54.107 +08:00 [INF] Configuring input validation...
2025-07-21 00:12:54.107 +08:00 [INF] Input validation configured
2025-07-21 00:12:54.107 +08:00 [INF] Configuring rate limiting...
2025-07-21 00:12:54.107 +08:00 [INF] Rate limiting configured
2025-07-21 00:12:54.111 +08:00 [INF] All services registered, building application...
2025-07-21 00:12:54.111 +08:00 [INF] Calling builder.Build()...
2025-07-21 00:21:53.264 +08:00 [INF] Starting WhimLabAI Web API
2025-07-21 00:21:53.583 +08:00 [INF] WebApplication builder created
2025-07-21 00:21:53.583 +08:00 [INF] Loading configuration files...
2025-07-21 00:21:53.583 +08:00 [INF] Environment variables loaded
2025-07-21 00:21:53.594 +08:00 [INF] Environment variable replacements loaded
2025-07-21 00:21:53.594 +08:00 [INF] Configuration loaded
2025-07-21 00:21:53.599 +08:00 [INF] Registering Infrastructure services...
2025-07-21 00:21:53.780 +08:00 [INF] Infrastructure services registered
2025-07-21 00:21:53.781 +08:00 [INF] Registering Application services...
2025-07-21 00:21:53.786 +08:00 [INF] Application services registered
2025-07-21 00:21:53.786 +08:00 [INF] Registering RealtimeNotificationService...
2025-07-21 00:21:53.786 +08:00 [INF] RealtimeNotificationService registered
2025-07-21 00:21:53.786 +08:00 [INF] Registering security services...
2025-07-21 00:21:53.787 +08:00 [INF] Security services registered
2025-07-21 00:21:53.787 +08:00 [INF] Configuring API versioning...
2025-07-21 00:21:53.787 +08:00 [INF] API versioning configured
2025-07-21 00:21:53.787 +08:00 [INF] Configuring input validation...
2025-07-21 00:21:53.787 +08:00 [INF] Input validation configured
2025-07-21 00:21:53.787 +08:00 [INF] Configuring rate limiting...
2025-07-21 00:21:53.787 +08:00 [INF] Rate limiting configured
2025-07-21 00:21:53.792 +08:00 [INF] All services registered, building application...
2025-07-21 00:21:53.792 +08:00 [INF] Calling builder.Build()...
2025-07-21 00:21:53.792 +08:00 [INF] About to call builder.Build() - checking for blocking services...
