{"Logging": {"LogLevel": {"Default": "Debug", "Microsoft.AspNetCore": "Information", "Microsoft.EntityFrameworkCore.Database.Command": "Information"}}, "DetailedErrors": true, "ConnectionStrings": {"DefaultConnection": "Host=${POSTGRES_HOST:localhost};Port=${POSTGRES_PORT:5432};Database=${POSTGRES_DB:whimlabai};Username=${POSTGRES_USER:postgres};Password=${POSTGRES_PASSWORD:postgres123};Pooling=true;MinPoolSize=5;MaxPoolSize=100;ConnectionLifetime=300;CommandTimeout=30;Timeout=30", "Redis": "${REDIS_HOST:localhost}:${REDIS_PORT:6379},password=${REDIS_PASSWORD:redis123},defaultDatabase=0,abortConnect=false,connectTimeout=5000,syncTimeout=5000,asyncTimeout=5000,keepAlive=30,connectRetry=3"}, "Jwt": {"SecretKey": "${JWT_SECRET:CHANGE_ME_USE_SECURE_KEY_AT_LEAST_256_BITS}"}, "AI": {"SemanticKernel": {"ApiKey": "${OPENAI_API_KEY:sk-CHANGE_ME_YOUR_ACTUAL_KEY}", "Endpoint": "", "DefaultModel": "gpt-3.5-turbo"}}, "Storage": {"Provider": "minio", "MinIO": {"Endpoint": "${MINIO_ENDPOINT:localhost:9000}", "AccessKey": "${MINIO_ROOT_USER:admin}", "SecretKey": "${MINIO_ROOT_PASSWORD:admin123}", "UseSSL": false, "DefaultBucket": "whimlab-ai", "DefaultUrlExpiryMinutes": 1440, "PublicEndpoint": "http://${MINIO_ENDPOINT:localhost:9000}"}}, "Verification": {"AdminCaptchaPolicy": {"Enabled": true, "AlwaysRequired": true, "DisableInDevelopment": false}, "CustomerCaptchaPolicy": {"Enabled": true, "AlwaysRequired": false, "FailedAttemptsThreshold": 3, "HourlyAttemptsThreshold": 10, "DisableInDevelopment": true}}, "Sms": {"Enabled": true, "Provider": "twi<PERSON>", "Providers": {"Twilio": {"AccountSid": "${TWILIO_ACCOUNT_SID:ACtest1234567890abcdef1234567890ab}", "AuthToken": "${TWILIO_AUTH_TOKEN:test1234567890abcdef1234567890ab}", "FromPhoneNumber": "${TWILIO_FROM_NUMBER:+*************}", "MessagingServiceSid": "${TWILIO_MESSAGING_SERVICE_SID:}", "MaxRetries": 1, "RetryDelaySeconds": 2}, "Aliyun": {"AccessKeyId": "${ALIYUN_ACCESS_KEY_ID:test-aliyun-access-key-id}", "AccessKeySecret": "${ALIYUN_ACCESS_KEY_SECRET:test-aliyun-access-key-secret}", "SignName": "${ALIYUN_SIGN_NAME:WhimLabAI测试}", "TemplateCode": "${ALIYUN_TEMPLATE_CODE:SMS_123456789}", "RegionId": "cn-hangzhou", "Product": "Dysmsapi", "Domain": "dysmsapi.aliyuncs.com", "Version": "2017-05-25", "Action": "SendSms"}, "Tencent": {"SecretId": "${TENCENT_SECRET_ID:test-tencent-secret-id}", "SecretKey": "${TENCENT_SECRET_KEY:test-tencent-secret-key}", "SmsSdkAppId": "${TENCENT_SMS_SDK_APP_ID:**********}", "SignName": "${TENCENT_SIGN_NAME:WhimLabAI测试}", "TemplateId": "${TENCENT_TEMPLATE_ID:123456}", "Region": "ap-guangzhou", "Endpoint": "sms.tencentcloudapi.com"}}, "RateLimiting": {"MaxSmsPerPhonePerDay": 100, "MaxSmsPerPhonePerHour": 20, "MaxSmsPerIpPerDay": 200}}, "Payment": {"BaseUrl": "https://localhost:5201", "CallbackUrl": "https://localhost:5201", "Security": {"EnableIpWhitelist": false, "IpWhitelist": ["127.0.0.1", "::1"], "ReplayWindowMinutes": 5, "EnableSignatureValidation": false}, "Alipay": {"AppId": "${ALIPAY_APP_ID:2021000000000001}", "PrivateKey": "${ALIPAY_PRIVATE_KEY:MIIEpAIBAAKCAQEAvKyb1mAqT7J5Y8KD7kwDFUBYN0fz2QrHKTDqUTlXsqd5LqXH6M8QV3hfKwgRHkDvT7Pc1rrTqZ5xJkL7J0FqCDZmPJRD7mIJCQ5j0StelYGdxJ0asLxqKqHUqMkNpJ1R2QyJ3G1x9FqZ0J5Y8KD7kwDFUBYN0fz2QrHKTDqUTlXsqd5LqXH6M8QV3hfKwgRHkDvT7Pc1rrTqZ5xJkL7J0FqCDZmPJRD7mIJCQ5j0StelYGdxJ0asLxqKqHUqMkNpJ1R2QyJ3G1x9FqZ0J5Y8KD7kwDFUBYN0fz2QrHKTDqUTlXsqd5LqXH6M8QV3hfKwgRHkDvT7Pc1rrTqZ5xJkL7J0FqCDZmPJRD7mIJCQ5j0StelYGdxJ0asLxqKqHUqMkNpJ1R2QyJ3G1x9FqZ0wIDAQABAoIBAQCrXsYXL5Y8KD7kwDFUBYN0fz2QrHKTDqUTlXsqd5LqXH6M8QV3hfKwgRHkDvT7Pc1rrTqZ5xJkL7J0FqCDZmPJRD7mIJCQ5j0StelYGdxJ0asLxqKqHUqMkNpJ1R2QyJ3G1x9FqZ0J5Y8KD7kwDFUBYN0fz2QrHKTDqUTlXsqd5LqXH6M8QV3hfKwgRHkDvT7Pc1rrTqZ5xJkL7J0FqCDZmPJRD7mIJCQ5j0StelYGdxJ0asLxqKqHUqMkNpJ1R2QyJ3G1x9FqZ0J5Y8KD7kwDFUBYN0fz2QrHKTDqUTlXsqd5LqXH6M8QV3hfKwgRHkDvT7Pc1rrTqZ5xJkL7J0FqCDZmPJRD7mIJCQ5j0StelYGdxJ0asLxqKqHUqMkNpJ1R2QyJ3G1x9FqZ0AoGBAPgL5Y8KD7kwDFUBYN0fz2QrHKTDqUTlXsqd5LqXH6M8QV3hfKwgRHkDvT7Pc1rrTqZ5xJkL7J0FqCDZmPJRD7mIJCQ5j0StelYGdxJ0asLxqKqHUqMkNpJ1R2QyJ3G1x9FqZ0J5Y8KD7kwDFUBYN0fz2QrHKTDqUTlXsqd5LqXH6M8QV3hfKwgRHkDvT7Pc1rrTqZ5xJkL7J0FqCDZmPJRD7mIJCQ5j0StelYGdxJ0asLxqKqHUqMkNpJ1R2QyJ3G1x9FqZ0AoGBAMJL5Y8KD7kwDFUBYN0fz2QrHKTDqUTlXsqd5LqXH6M8QV3hfKwgRHkDvT7Pc1rrTqZ5xJkL7J0FqCDZmPJRD7mIJCQ5j0StelYGdxJ0asLxqKqHUqMkNpJ1R2QyJ3G1x9FqZ0J5Y8KD7kwDFUBYN0fz2QrHKTDqUTlXsqd5LqXH6M8QV3hfKwgRHkDvT7Pc1rrTqZ5xJkL7J0FqCDZmPJRD7mIJCQ5j0StelYGdxJ0asLxqKqHUqMkNpJ1R2QyJ3G1x9FqZ0A=}", "PublicKey": "${ALIPAY_PUBLIC_KEY:MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAvKyb1mAqT7J5Y8KD7kwDFUBYN0fz2QrHKTDqUTlXsqd5LqXH6M8QV3hfKwgRHkDvT7Pc1rrTqZ5xJkL7J0FqCDZmPJRD7mIJCQ5j0StelYGdxJ0asLxqKqHUqMkNpJ1R2QyJ3G1x9FqZ0J5Y8KD7kwDFUBYN0fz2QrHKTDqUTlXsqd5LqXH6M8QV3hfKwgRHkDvT7Pc1rrTqZ5xJkL7J0FqCDZmPJRD7mIJCQ5j0StelYGdxJ0asLxqKqHUqMkNpJ1R2QyJ3G1x9FqZ0J5Y8KD7kwDFUBYN0fz2QrHKTDqUTlXsqd5LqXH6M8QV3hfKwgRHkDvT7Pc1rrTqZ5xJkL7J0FqCDZmPJRD7mIJCQ5j0StelYGdxJ0asLxqKqHUqMkNpJ1R2QyJ3G1x9FqZ0wIDAQAB}", "AlipayPublicKey": "${ALIPAY_PLATFORM_PUBLIC_KEY:MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAliN8Kl5H1Y5Y8KD7kwDFUBYN0fz2QrHKTDqUTlXsqd5LqXH6M8QV3hfKwgRHkDvT7Pc1rrTqZ5xJkL7J0FqCDZmPJRD7mIJCQ5j0StelYGdxJ0asLxqKqHUqMkNpJ1R2QyJ3G1x9FqZ0J5Y8KD7kwDFUBYN0fz2QrHKTDqUTlXsqd5LqXH6M8QV3hfKwgRHkDvT7Pc1rrTqZ5xJkL7J0FqCDZmPJRD7mIJCQ5j0StelYGdxJ0asLxqKqHUqMkNpJ1R2QyJ3G1x9FqZ0J5Y8KD7kwDFUBYN0fz2QrHKTDqUTlXsqd5LqXH6M8QV3hfKwgRHkDvT7Pc1rrTqZ5xJkL7J0FqCDZmPJRD7mIJCQ5j0StelYGdxJ0asLxqKqHUqMkNpJ1R2QyJ3G1x9FqZ0wIDAQAB}", "IsProduction": false, "Gateway": "https://openapi.alipaydev.com/gateway.do", "NotifyUrl": "https://localhost:5201/api/payment/alipay/notify", "ReturnUrl": "https://localhost:5201/payment/result"}, "WeChatPay": {"AppId": "${WECHAT_APP_ID:wx0000000000000001}", "MchId": "${WECHAT_MCH_ID:1000000001}", "ApiKey": "${WECHAT_API_KEY:test-api-key-32-characters-12345}", "CertPath": "./certs/wechat/apiclient_cert_dev.p12", "Gateway": "https://api.mch.weixin.qq.com/sandboxnew", "NotifyUrl": "https://localhost:5201/api/payment/wechatpay/notify", "ReturnUrl": "https://localhost:5201/payment/result"}}, "RabbitMQ": {"Host": "${RABBITMQ_HOST:localhost}", "Port": 5672, "Username": "${RABBITMQ_USER:whimlab}", "Password": "${RABBITMQ_PASS:rabbitmq123}", "VirtualHost": "/"}, "Email": {"Enabled": true, "LogEmailContent": true, "MaxRetryAttempts": 1, "RetryDelaySeconds": 1, "Smtp": {"Host": "${SMTP_HOST:smtp.whimlab.com}", "Port": "${SMTP_PORT:587}", "UseSsl": "${SMTP_USE_SSL:true}", "Username": "${SMTP_USERNAME:<EMAIL>}", "Password": "${SMTP_PASSWORD:_,EsTATE,94}", "Timeout": 10000}, "From": {"Name": "${EMAIL_FROM_NAME:WhimLab AI Dev}", "Address": "${EMAIL_FROM_ADDRESS:<EMAIL>}"}}}