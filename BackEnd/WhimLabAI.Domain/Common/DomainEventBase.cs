using System;

namespace WhimLabAI.Domain.Common;

/// <summary>
/// 领域事件基类
/// </summary>
public abstract record DomainEventBase : IDomainEvent
{
    /// <summary>
    /// 事件发生时间
    /// </summary>
    public DateTime OccurredOn { get; init; }
    
    /// <summary>
    /// 聚合根ID
    /// </summary>
    public Guid AggregateId { get; init; }
    
    /// <summary>
    /// 事件类型
    /// </summary>
    public string EventType { get; init; }
    
    protected DomainEventBase(Guid aggregateId)
    {
        AggregateId = aggregateId;
        OccurredOn = DateTime.UtcNow;
        EventType = GetType().Name;
    }
}