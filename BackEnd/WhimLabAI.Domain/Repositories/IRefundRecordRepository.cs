using WhimLabAI.Domain.Entities.Payment;
using WhimLabAI.Shared.Enums;

namespace WhimLabAI.Domain.Repositories;

public interface IRefundRecordRepository : IRepository<RefundRecord>
{
    Task<RefundRecord?> GetByRefundNoAsync(string refundNo, CancellationToken cancellationToken = default);
    Task<IEnumerable<RefundRecord>> GetOrderRefundsAsync(Guid orderId, CancellationToken cancellationToken = default);
    Task<IEnumerable<RefundRecord>> GetPendingRefundsAsync(CancellationToken cancellationToken = default);
    Task<bool> UpdateRefundStatusAsync(Guid refundId, RefundStatus newStatus, string? transactionId = null, CancellationToken cancellationToken = default);
    Task<decimal> GetTotalRefundedAmountAsync(Guid orderId, CancellationToken cancellationToken = default);
    Task<bool> HasPendingRefundAsync(Guid orderId, CancellationToken cancellationToken = default);
    Task<RefundStatistics> GetRefundStatisticsAsync(DateTime startDate, DateTime endDate, CancellationToken cancellationToken = default);
    Task<IEnumerable<RefundRecord>> GetUserRefundsAsync(Guid userId, CancellationToken cancellationToken = default);
    Task<bool> CanRefundOrderAsync(Guid orderId, decimal amount, CancellationToken cancellationToken = default);
    Task<IEnumerable<RefundRecord>> GetRefundsByPaymentMethodAsync(PaymentMethod method, DateTime startDate, DateTime endDate, CancellationToken cancellationToken = default);
}

public class RefundStatistics
{
    public int TotalRefunds { get; set; }
    public decimal TotalAmount { get; set; }
    public int SuccessfulRefunds { get; set; }
    public int FailedRefunds { get; set; }
    public int PendingRefunds { get; set; }
    public Dictionary<RefundStatus, int> RefundsByStatus { get; set; } = new();
    public Dictionary<PaymentMethod, decimal> RefundsByPaymentMethod { get; set; } = new();
    public Dictionary<string, decimal> RefundsByReason { get; set; } = new();
}