using System.Threading;
using System.Threading.Tasks;
using WhimLabAI.Domain.Entities.Notification;

namespace WhimLabAI.Domain.Repositories;

/// <summary>
/// 短信日志仓储接口
/// </summary>
public interface ISmsLogRepository : IRepository<SmsLog>
{
    /// <summary>
    /// 获取手机号在指定时间内的发送次数
    /// </summary>
    /// <param name="phoneNumber">手机号码（加密后）</param>
    /// <param name="minutes">时间范围（分钟）</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>发送次数</returns>
    Task<int> GetSendCountAsync(string phoneNumber, int minutes, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 获取IP在指定时间内的发送次数
    /// </summary>
    /// <param name="ipAddress">IP地址</param>
    /// <param name="minutes">时间范围（分钟）</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>发送次数</returns>
    Task<int> GetIpSendCountAsync(string ipAddress, int minutes, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 获取最新的验证码
    /// </summary>
    /// <param name="phoneNumber">手机号码（加密后）</param>
    /// <param name="purpose">用途</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>短信日志</returns>
    Task<SmsLog?> GetLatestVerificationCodeAsync(string phoneNumber, string purpose, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 检查手机号是否被屏蔽
    /// </summary>
    /// <param name="phoneNumber">手机号码（加密后）</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>是否被屏蔽</returns>
    Task<bool> IsPhoneBlockedAsync(string phoneNumber, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 获取待重试的短信
    /// </summary>
    /// <param name="maxRetryCount">最大重试次数</param>
    /// <param name="limit">限制数量</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>短信日志列表</returns>
    Task<List<SmsLog>> GetPendingRetryAsync(int maxRetryCount, int limit, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 根据提供商消息ID获取短信日志
    /// </summary>
    /// <param name="providerMessageId">提供商消息ID</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>短信日志</returns>
    Task<SmsLog?> GetByProviderMessageIdAsync(string providerMessageId, CancellationToken cancellationToken = default);
}