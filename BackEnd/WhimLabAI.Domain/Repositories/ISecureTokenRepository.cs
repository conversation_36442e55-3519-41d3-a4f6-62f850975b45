using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using WhimLabAI.Domain.Entities.Security;

namespace WhimLabAI.Domain.Repositories;

/// <summary>
/// Repository interface for secure token operations
/// </summary>
public interface ISecureTokenRepository
{
    /// <summary>
    /// Adds a new secure token
    /// </summary>
    Task<SecureToken> AddAsync(SecureToken token);
    
    /// <summary>
    /// Gets a token by its ID
    /// </summary>
    Task<SecureToken?> GetByTokenIdAsync(Guid tokenId);
    
    /// <summary>
    /// Gets all tokens for a user
    /// </summary>
    Task<List<SecureToken>> GetByUserIdAsync(Guid userId, string? tokenType = null);
    
    /// <summary>
    /// Updates a token
    /// </summary>
    Task UpdateAsync(SecureToken token);
    
    /// <summary>
    /// Deletes expired tokens
    /// </summary>
    Task<int> DeleteExpiredTokensAsync();
    
    /// <summary>
    /// Gets tokens by type
    /// </summary>
    Task<List<SecureToken>> GetByTypeAsync(string tokenType, bool includeRevoked = false);
    
    /// <summary>
    /// Checks if a token exists
    /// </summary>
    Task<bool> ExistsAsync(Guid tokenId);
    
    /// <summary>
    /// Revokes all tokens for a user
    /// </summary>
    Task RevokeAllUserTokensAsync(Guid userId, string? tokenType = null);
}