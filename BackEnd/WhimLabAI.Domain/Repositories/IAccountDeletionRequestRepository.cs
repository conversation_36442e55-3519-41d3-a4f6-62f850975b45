using System;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using WhimLabAI.Domain.Entities.User;

namespace WhimLabAI.Domain.Repositories;

/// <summary>
/// 账号注销请求仓储接口
/// </summary>
public interface IAccountDeletionRequestRepository : IRepository<AccountDeletionRequest>
{
    /// <summary>
    /// 根据用户ID获取最新的注销请求
    /// </summary>
    Task<AccountDeletionRequest?> GetLatestByCustomerUserIdAsync(
        Guid customerUserId, 
        CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 获取用户的所有注销请求
    /// </summary>
    Task<List<AccountDeletionRequest>> GetByCustomerUserIdAsync(
        Guid customerUserId, 
        CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 获取待处理的注销请求（冷静期已结束但未冻结账号）
    /// </summary>
    Task<List<AccountDeletionRequest>> GetPendingFreezingRequestsAsync(
        CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 获取待匿名化的请求（账号已冻结30天）
    /// </summary>
    Task<List<AccountDeletionRequest>> GetPendingAnonymizationRequestsAsync(
        CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 获取需要发送冷静期提醒的请求（冷静期还剩1天）
    /// </summary>
    Task<List<AccountDeletionRequest>> GetRequestsNeedingCoolingOffReminderAsync(
        CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 检查用户是否有活跃的注销请求
    /// </summary>
    Task<bool> HasActiveRequestAsync(
        Guid customerUserId, 
        CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 根据验证码获取请求
    /// </summary>
    Task<AccountDeletionRequest?> GetByVerificationCodeAsync(
        string verificationCode, 
        CancellationToken cancellationToken = default);
}