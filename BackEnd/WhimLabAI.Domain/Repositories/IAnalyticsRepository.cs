using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using WhimLabAI.Domain.Entities.Agent;
using WhimLabAI.Domain.Entities.Conversation;
using WhimLabAI.Domain.Entities.Organization;
using WhimLabAI.Domain.Entities.Payment;
using WhimLabAI.Domain.Entities.Subscription;
using WhimLabAI.Domain.Entities.User;

namespace WhimLabAI.Domain.Repositories;

/// <summary>
/// 数据分析仓储接口
/// </summary>
public interface IAnalyticsRepository
{
    #region 用户分析查询
    
    /// <summary>
    /// 获取用户查询集
    /// </summary>
    IQueryable<CustomerUser> GetCustomerUsersQuery();
    
    /// <summary>
    /// 获取指定时间范围内的活跃用户数
    /// </summary>
    Task<int> GetActiveUsersCountAsync(DateTime startDate, DateTime endDate, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 获取用户留存数据
    /// </summary>
    Task<Dictionary<int, int>> GetUserRetentionAsync(DateTime cohortDate, List<Guid> userIds, int daysToTrack, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 获取用户登录历史记录查询集
    /// </summary>
    IQueryable<UserLoginHistory> GetUserLoginHistoriesQuery();
    
    /// <summary>
    /// 获取部门查询集
    /// </summary>
    IQueryable<Department> GetDepartmentsQuery();
    
    /// <summary>
    /// 获取用户获客来源查询集
    /// </summary>
    IQueryable<UserAcquisition> GetUserAcquisitionsQuery();
    
    #endregion
    
    #region Agent分析查询
    
    /// <summary>
    /// 获取Agent查询集
    /// </summary>
    IQueryable<Agent> GetAgentsQuery();
    
    /// <summary>
    /// 获取热门Agent统计
    /// </summary>
    IQueryable<Agent> GetPopularAgentsQuery(DateTime startDate, DateTime endDate);
    
    #endregion
    
    #region 对话分析查询
    
    /// <summary>
    /// 获取对话查询集
    /// </summary>
    IQueryable<Conversation> GetConversationsQuery();
    
    /// <summary>
    /// 获取消息查询集
    /// </summary>
    IQueryable<ConversationMessage> GetMessagesQuery();
    
    /// <summary>
    /// 获取对话统计数据
    /// </summary>
    Task<(int totalConversations, int totalMessages, long totalTokens)> GetConversationStatsAsync(
        DateTime startDate, 
        DateTime endDate, 
        CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 获取对话度量查询集
    /// </summary>
    IQueryable<ConversationMetrics> GetConversationMetricsQuery();
    
    #endregion
    
    #region 财务分析查询
    
    /// <summary>
    /// 获取订阅查询集
    /// </summary>
    IQueryable<Subscription> GetSubscriptionsQuery();
    
    /// <summary>
    /// 获取订单查询集
    /// </summary>
    IQueryable<Order> GetOrdersQuery();
    
    /// <summary>
    /// 获取收入统计
    /// </summary>
    Task<(decimal totalRevenue, decimal recurringRevenue, int orderCount)> GetRevenueStatsAsync(
        DateTime startDate, 
        DateTime endDate, 
        CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 获取退款申请查询集
    /// </summary>
    IQueryable<RefundRequest> GetRefundRequestsQuery();
    
    #endregion
    
    #region Token使用分析查询
    
    /// <summary>
    /// 获取Token使用记录查询集
    /// </summary>
    IQueryable<TokenUsage> GetTokenUsagesQuery();
    
    /// <summary>
    /// 获取Token使用统计
    /// </summary>
    Task<Dictionary<string, long>> GetTokenUsageByModelAsync(
        DateTime startDate, 
        DateTime endDate, 
        CancellationToken cancellationToken = default);
    
    #endregion
    
    #region 评价分析查询
    
    /// <summary>
    /// 获取评价查询集
    /// </summary>
    IQueryable<AgentRating> GetReviewsQuery();
    
    /// <summary>
    /// 获取评分统计
    /// </summary>
    Task<(double averageRating, int totalRatings)> GetRatingStatsAsync(
        Guid? agentId, 
        DateTime? startDate, 
        DateTime? endDate, 
        CancellationToken cancellationToken = default);
    
    #endregion
}