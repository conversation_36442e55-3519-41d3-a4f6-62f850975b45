using System;
using WhimLabAI.Domain.Common;

namespace WhimLabAI.Domain.DomainEvents;

/// <summary>
/// 账号数据匿名化事件
/// </summary>
public record AccountDataAnonymizedEvent : DomainEventBase
{
    /// <summary>
    /// 请求ID
    /// </summary>
    public Guid RequestId { get; init; }
    
    /// <summary>
    /// 用户ID
    /// </summary>
    public Guid CustomerUserId { get; init; }
    
    /// <summary>
    /// 原用户名（匿名化前）
    /// </summary>
    public string OriginalUsername { get; init; }
    
    /// <summary>
    /// 匿名化时间
    /// </summary>
    public DateTime AnonymizedAt { get; init; }
    
    /// <summary>
    /// 匿名化的数据类型
    /// </summary>
    public string[] AnonymizedDataTypes { get; init; }
    
    public AccountDataAnonymizedEvent(
        Guid requestId,
        Guid customerUserId,
        string originalUsername,
        DateTime anonymizedAt,
        string[] anonymizedDataTypes) : base(customerUserId)
    {
        RequestId = requestId;
        CustomerUserId = customerUserId;
        OriginalUsername = originalUsername;
        AnonymizedAt = anonymizedAt;
        AnonymizedDataTypes = anonymizedDataTypes;
    }
}