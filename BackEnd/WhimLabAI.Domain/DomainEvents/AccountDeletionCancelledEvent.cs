using System;
using WhimLabAI.Domain.Common;

namespace WhimLabAI.Domain.DomainEvents;

/// <summary>
/// 账号注销请求撤销事件
/// </summary>
public record AccountDeletionCancelledEvent : DomainEventBase
{
    /// <summary>
    /// 请求ID
    /// </summary>
    public Guid RequestId { get; init; }
    
    /// <summary>
    /// 用户ID
    /// </summary>
    public Guid CustomerUserId { get; init; }
    
    /// <summary>
    /// 用户名
    /// </summary>
    public string Username { get; init; }
    
    /// <summary>
    /// 撤销原因
    /// </summary>
    public string CancellationReason { get; init; }
    
    /// <summary>
    /// 撤销时间
    /// </summary>
    public DateTime CancelledAt { get; init; }
    
    public AccountDeletionCancelledEvent(
        Guid requestId,
        Guid customerUserId,
        string username,
        string cancellationReason,
        DateTime cancelledAt) : base(customerUserId)
    {
        RequestId = requestId;
        CustomerUserId = customerUserId;
        Username = username;
        CancellationReason = cancellationReason;
        CancelledAt = cancelledAt;
    }
}