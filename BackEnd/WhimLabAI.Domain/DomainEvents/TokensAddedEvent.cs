using WhimLabAI.Domain.Common;

namespace WhimLabAI.Domain.DomainEvents;

/// <summary>
/// Domain event raised when tokens are added to a subscription
/// </summary>
public class TokensAddedEvent : DomainEvent
{
    public Guid SubscriptionId { get; }
    public Guid CustomerUserId { get; }
    public int TokensAdded { get; }
    public string Reason { get; }
    
    public TokensAddedEvent(Guid subscriptionId, Guid customerUserId, int tokensAdded, string reason)
        : base(subscriptionId)
    {
        SubscriptionId = subscriptionId;
        CustomerUserId = customerUserId;
        TokensAdded = tokensAdded;
        Reason = reason;
    }
}