using System;
using WhimLabAI.Domain.Common;

namespace WhimLabAI.Domain.DomainEvents;

/// <summary>
/// 账号冻结事件
/// </summary>
public record AccountFrozenEvent : DomainEventBase
{
    /// <summary>
    /// 请求ID
    /// </summary>
    public Guid RequestId { get; init; }
    
    /// <summary>
    /// 用户ID
    /// </summary>
    public Guid CustomerUserId { get; init; }
    
    /// <summary>
    /// 用户名
    /// </summary>
    public string Username { get; init; }
    
    /// <summary>
    /// 冻结时间
    /// </summary>
    public DateTime FrozenAt { get; init; }
    
    /// <summary>
    /// 计划删除时间
    /// </summary>
    public DateTime PlannedDeletionAt { get; init; }
    
    public AccountFrozenEvent(
        Guid requestId,
        Guid customerUserId,
        string username,
        DateTime frozenAt,
        DateTime plannedDeletionAt) : base(customerUserId)
    {
        RequestId = requestId;
        CustomerUserId = customerUserId;
        Username = username;
        FrozenAt = frozenAt;
        PlannedDeletionAt = plannedDeletionAt;
    }
}