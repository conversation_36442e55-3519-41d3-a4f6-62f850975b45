using System;
using WhimLabAI.Domain.Common;
using WhimLabAI.Shared.Enums;

namespace WhimLabAI.Domain.DomainEvents;

/// <summary>
/// 账号注销请求创建事件
/// </summary>
public record AccountDeletionRequestedEvent : DomainEventBase
{
    /// <summary>
    /// 请求ID
    /// </summary>
    public Guid RequestId { get; init; }
    
    /// <summary>
    /// 用户ID
    /// </summary>
    public Guid CustomerUserId { get; init; }
    
    /// <summary>
    /// 用户名
    /// </summary>
    public string Username { get; init; }
    
    /// <summary>
    /// 邮箱
    /// </summary>
    public string? Email { get; init; }
    
    /// <summary>
    /// 注销原因
    /// </summary>
    public string Reason { get; init; }
    
    /// <summary>
    /// 原因分类
    /// </summary>
    public DeletionReasonCategory ReasonCategory { get; init; }
    
    /// <summary>
    /// 冷静期结束时间
    /// </summary>
    public DateTime CoolingOffEndAt { get; init; }
    
    /// <summary>
    /// 计划删除时间
    /// </summary>
    public DateTime PlannedDeletionAt { get; init; }
    
    public AccountDeletionRequestedEvent(
        Guid requestId,
        Guid customerUserId,
        string username,
        string? email,
        string reason,
        DeletionReasonCategory reasonCategory,
        DateTime coolingOffEndAt,
        DateTime plannedDeletionAt) : base(customerUserId)
    {
        RequestId = requestId;
        CustomerUserId = customerUserId;
        Username = username;
        Email = email;
        Reason = reason;
        ReasonCategory = reasonCategory;
        CoolingOffEndAt = coolingOffEndAt;
        PlannedDeletionAt = plannedDeletionAt;
    }
}