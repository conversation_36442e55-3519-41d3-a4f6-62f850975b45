using System;
using System.Collections.Generic;
using WhimLabAI.Domain.Common;
using WhimLabAI.Domain.Entities.User;

namespace WhimLabAI.Domain.Entities.Organization;

/// <summary>
/// 部门实体
/// </summary>
public class Department : Entity
{
    /// <summary>
    /// 部门名称
    /// </summary>
    public string Name { get; set; } = string.Empty;
    
    /// <summary>
    /// 部门代码
    /// </summary>
    public string Code { get; set; } = string.Empty;
    
    /// <summary>
    /// 部门描述
    /// </summary>
    public string? Description { get; set; }
    
    /// <summary>
    /// 上级部门ID
    /// </summary>
    public Guid? ParentDepartmentId { get; set; }
    
    /// <summary>
    /// 上级部门
    /// </summary>
    public Department? ParentDepartment { get; set; }
    
    /// <summary>
    /// 子部门列表
    /// </summary>
    public ICollection<Department> SubDepartments { get; set; } = new List<Department>();
    
    /// <summary>
    /// 部门成员列表
    /// </summary>
    public ICollection<CustomerUser> Members { get; set; } = new List<CustomerUser>();
    
    /// <summary>
    /// 部门预算（月度，人民币）
    /// </summary>
    public decimal MonthlyBudget { get; set; }
    
    /// <summary>
    /// 是否启用
    /// </summary>
    public bool IsActive { get; set; } = true;
}