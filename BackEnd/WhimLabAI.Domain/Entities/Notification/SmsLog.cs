using WhimLabAI.Domain.Common;

namespace WhimLabAI.Domain.Entities.Notification;

/// <summary>
/// 短信日志实体
/// </summary>
public class SmsLog : Entity
{
    /// <summary>
    /// 手机号码（加密存储）
    /// </summary>
    public string PhoneNumber { get; set; } = string.Empty;
    
    /// <summary>
    /// 手机号码掩码（用于展示，如：138****1234）
    /// </summary>
    public string PhoneNumberMask { get; set; } = string.Empty;
    
    /// <summary>
    /// 国家代码
    /// </summary>
    public string CountryCode { get; set; } = string.Empty;
    
    /// <summary>
    /// 短信类型（VerificationCode/Notification/Marketing）
    /// </summary>
    public string Type { get; set; } = string.Empty;
    
    /// <summary>
    /// 用途（Login/Register/ResetPassword/PaymentNotify等）
    /// </summary>
    public string Purpose { get; set; } = string.Empty;
    
    /// <summary>
    /// 模板ID
    /// </summary>
    public string? TemplateId { get; set; }
    
    /// <summary>
    /// 模板参数（JSON格式）
    /// </summary>
    public string? TemplateParams { get; set; }
    
    /// <summary>
    /// 短信内容（脱敏后）
    /// </summary>
    public string Content { get; set; } = string.Empty;
    
    /// <summary>
    /// 提供商名称
    /// </summary>
    public string Provider { get; set; } = string.Empty;
    
    /// <summary>
    /// 提供商返回的消息ID
    /// </summary>
    public string? ProviderMessageId { get; set; }
    
    /// <summary>
    /// 发送状态（Pending/Sent/Delivered/Failed）
    /// </summary>
    public string Status { get; set; } = "Pending";
    
    /// <summary>
    /// 发送时间
    /// </summary>
    public DateTime SendTime { get; set; }
    
    /// <summary>
    /// 送达时间
    /// </summary>
    public DateTime? DeliveredTime { get; set; }
    
    /// <summary>
    /// 重试次数
    /// </summary>
    public int RetryCount { get; set; }
    
    /// <summary>
    /// 最后重试时间
    /// </summary>
    public DateTime? LastRetryTime { get; set; }
    
    /// <summary>
    /// 错误代码
    /// </summary>
    public string? ErrorCode { get; set; }
    
    /// <summary>
    /// 错误消息
    /// </summary>
    public string? ErrorMessage { get; set; }
    
    /// <summary>
    /// 费用（分）
    /// </summary>
    public int Cost { get; set; }
    
    /// <summary>
    /// 关联的用户ID
    /// </summary>
    public Guid? UserId { get; set; }
    
    /// <summary>
    /// 用户类型（Customer/Admin）
    /// </summary>
    public string? UserType { get; set; }
    
    /// <summary>
    /// IP地址
    /// </summary>
    public string? IpAddress { get; set; }
    
    /// <summary>
    /// 用户代理
    /// </summary>
    public string? UserAgent { get; set; }
    
    /// <summary>
    /// 请求ID（用于追踪）
    /// </summary>
    public string? RequestId { get; set; }
    
    /// <summary>
    /// 验证码（仅用于验证码类型，加密存储）
    /// </summary>
    public string? VerificationCode { get; set; }
    
    /// <summary>
    /// 验证码过期时间
    /// </summary>
    public DateTime? CodeExpiryTime { get; set; }
    
    /// <summary>
    /// 是否已验证
    /// </summary>
    public bool IsVerified { get; set; }
    
    /// <summary>
    /// 验证时间
    /// </summary>
    public DateTime? VerifiedTime { get; set; }
    
    /// <summary>
    /// 验证尝试次数
    /// </summary>
    public int VerifyAttempts { get; set; }
    
    /// <summary>
    /// 是否被屏蔽（用于防止滥用）
    /// </summary>
    public bool IsBlocked { get; set; }
    
    /// <summary>
    /// 屏蔽原因
    /// </summary>
    public string? BlockReason { get; set; }
    
    /// <summary>
    /// 附加数据（JSON格式）
    /// </summary>
    public string? AdditionalData { get; set; }

    /// <summary>
    /// 创建短信日志
    /// </summary>
    public static SmsLog Create(
        string phoneNumber,
        string phoneNumberMask,
        string countryCode,
        string type,
        string purpose,
        string content,
        string provider)
    {
        return new SmsLog
        {
            Id = Guid.NewGuid(),
            PhoneNumber = phoneNumber,
            PhoneNumberMask = phoneNumberMask,
            CountryCode = countryCode,
            Type = type,
            Purpose = purpose,
            Content = content,
            Provider = provider,
            Status = "Pending",
            SendTime = DateTime.UtcNow
        };
    }

    /// <summary>
    /// 标记为已发送
    /// </summary>
    public void MarkAsSent(string providerMessageId, int cost)
    {
        Status = "Sent";
        ProviderMessageId = providerMessageId;
        Cost = cost;
    }

    /// <summary>
    /// 标记为已送达
    /// </summary>
    public void MarkAsDelivered()
    {
        Status = "Delivered";
        DeliveredTime = DateTime.UtcNow;
    }

    /// <summary>
    /// 标记为失败
    /// </summary>
    public void MarkAsFailed(string errorCode, string errorMessage)
    {
        Status = "Failed";
        ErrorCode = errorCode;
        ErrorMessage = errorMessage;
    }

    /// <summary>
    /// 增加重试次数
    /// </summary>
    public void IncrementRetryCount()
    {
        RetryCount++;
        LastRetryTime = DateTime.UtcNow;
    }

    /// <summary>
    /// 设置验证码信息
    /// </summary>
    public void SetVerificationCode(string code, int expiryMinutes = 5)
    {
        VerificationCode = code;
        CodeExpiryTime = DateTime.UtcNow.AddMinutes(expiryMinutes);
    }

    /// <summary>
    /// 验证验证码
    /// </summary>
    public bool VerifyCode(string code)
    {
        VerifyAttempts++;

        if (IsVerified)
            return false;

        if (CodeExpiryTime.HasValue && DateTime.UtcNow > CodeExpiryTime.Value)
            return false;

        if (VerificationCode == code)
        {
            IsVerified = true;
            VerifiedTime = DateTime.UtcNow;
            return true;
        }

        return false;
    }

    /// <summary>
    /// 是否已过期
    /// </summary>
    public bool IsExpired()
    {
        if (!CodeExpiryTime.HasValue)
            return false;

        return DateTime.UtcNow > CodeExpiryTime.Value;
    }

    /// <summary>
    /// 屏蔽手机号
    /// </summary>
    public void Block(string reason)
    {
        IsBlocked = true;
        BlockReason = reason;
    }
}