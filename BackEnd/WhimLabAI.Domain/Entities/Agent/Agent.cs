using WhimLabAI.Domain.Common;
using WhimLabAI.Domain.DomainEvents;
using WhimLabAI.Domain.ValueObjects;
using WhimLabAI.Shared.Enums;
using WhimLabAI.Shared.Exceptions;

namespace WhimLabAI.Domain.Entities.Agent;

public class Agent : AggregateRoot
{
    private readonly List<AgentVersion> _versions = new();
    private readonly List<AgentTag> _tags = new();
    private readonly List<AgentRating> _ratings = new();
    private readonly List<AgentApiKey> _apiKeys = new();
    
    public string Name { get; private set; }
    public string? UniqueKey { get; private set; }
    public string? Description { get; private set; }
    public string? DetailedIntro { get; private set; }
    public Guid? CategoryId { get; private set; }
    public string? Icon { get; private set; }
    public string? Cover { get; private set; }
    public AgentStatus Status { get; private set; }
    public Guid? CurrentVersionId { get; private set; }
    public Guid CreatorId { get; private set; }
    public DateTime? PublishedAt { get; private set; }
    public DateTime? ArchivedAt { get; private set; }
    public int UsageCount { get; private set; }
    public int ViewCount { get; private set; }
    public int LikeCount { get; private set; }
    public double AverageRating { get; private set; }
    public int RatingCount { get; private set; }
    public Dictionary<string, object> Metadata { get; private set; }
    
    public AgentCategory? Category { get; private set; }
    public AgentVersion? CurrentVersion { get; private set; }
    public IReadOnlyCollection<AgentVersion> Versions => _versions.AsReadOnly();
    public IReadOnlyCollection<AgentTag> Tags => _tags.AsReadOnly();
    public IReadOnlyCollection<AgentRating> Ratings => _ratings.AsReadOnly();
    public IReadOnlyCollection<AgentApiKey> ApiKeys => _apiKeys.AsReadOnly();
    
    private Agent() : base()
    {
        Metadata = new Dictionary<string, object>();
    }
    
    public Agent(
        string name,
        Guid creatorId,
        string? uniqueKey = null,
        string? description = null,
        Guid? categoryId = null) : base()
    {
        SetName(name);
        CreatorId = creatorId;
        SetUniqueKey(uniqueKey);
        Description = description;
        CategoryId = categoryId;
        Status = AgentStatus.Draft;
        UsageCount = 0;
        ViewCount = 0;
        LikeCount = 0;
        AverageRating = 0;
        RatingCount = 0;
        Metadata = new Dictionary<string, object>();
        
        // Create initial version
        var initialVersion = new AgentVersion(Id, 1);
        _versions.Add(initialVersion);
        // Don't set CurrentVersionId here to avoid circular dependency
    }
    
    public void Update(
        string? name = null,
        string? description = null,
        string? detailedIntro = null,
        Guid? categoryId = null,
        string? icon = null,
        string? cover = null)
    {
        if (Status == AgentStatus.Archived)
            throw new BusinessException("已归档的智能体不能修改");
            
        if (name != null) SetName(name);
        if (description != null) Description = description;
        if (detailedIntro != null) DetailedIntro = detailedIntro;
        if (categoryId.HasValue) CategoryId = categoryId.Value;
        if (icon != null) Icon = icon;
        if (cover != null) Cover = cover;
        
        UpdateTimestamp();
    }
    
    public AgentVersion CreateNewVersion(ModelConfiguration? modelConfig = null)
    {
        if (Status == AgentStatus.Archived)
            throw new BusinessException("已归档的智能体不能创建新版本");
            
        var latestVersion = _versions.OrderByDescending(v => v.VersionNumber).FirstOrDefault();
        var newVersionNumber = latestVersion?.VersionNumber + 1 ?? 1;
        
        var newVersion = new AgentVersion(Id, newVersionNumber);
        
        // Copy configuration from current version if not provided
        if (modelConfig == null && CurrentVersion != null)
        {
            newVersion.UpdateConfiguration(
                CurrentVersion.ModelConfig,
                CurrentVersion.SystemPrompt,
                CurrentVersion.UserPrompt,
                CurrentVersion.Plugins.ToList(),
                CurrentVersion.KnowledgeBases.ToList());
        }
        else if (modelConfig != null)
        {
            newVersion.UpdateConfiguration(modelConfig);
        }
        
        _versions.Add(newVersion);
        CurrentVersionId = newVersion.Id;
        
        UpdateTimestamp();
        return newVersion;
    }
    
    public void Publish()
    {
        if (Status == AgentStatus.Published)
            throw new BusinessException("智能体已经发布");
            
        if (Status == AgentStatus.Archived)
            throw new BusinessException("已归档的智能体不能发布");
            
        var currentVersion = _versions.FirstOrDefault(v => v.Id == CurrentVersionId);
        if (currentVersion == null)
            throw new BusinessException("当前版本不存在");
            
        if (!currentVersion.IsValid())
            throw new BusinessException("当前版本配置不完整");
            
        Status = AgentStatus.Published;
        PublishedAt = DateTime.UtcNow;
        currentVersion.Publish();
        
        UpdateTimestamp();
        RaiseDomainEvent(new AgentPublishedEvent(Id, Name, CreatorId));
        RaiseDomainEvent(new AgentVersionPublishedEvent(Id, currentVersion.Id, currentVersion.VersionNumber, CreatorId.ToString()));
    }
    
    public void Archive(string? reason = null)
    {
        if (Status == AgentStatus.Archived)
            throw new BusinessException("智能体已经归档");
            
        Status = AgentStatus.Archived;
        ArchivedAt = DateTime.UtcNow;
        
        UpdateTimestamp();
    }
    
    public void Restore()
    {
        if (Status != AgentStatus.Archived)
            throw new BusinessException("只有已归档的智能体才能恢复");
            
        Status = AgentStatus.Draft;
        ArchivedAt = null;
        
        UpdateTimestamp();
    }
    
    public void AddTag(string tag)
    {
        if (string.IsNullOrWhiteSpace(tag))
            return;
            
        if (_tags.Any(t => t.Name.Equals(tag, StringComparison.OrdinalIgnoreCase)))
            return;
            
        _tags.Add(new AgentTag(tag));
        UpdateTimestamp();
    }
    
    public void RemoveTag(string tag)
    {
        var tagToRemove = _tags.FirstOrDefault(t => t.Name.Equals(tag, StringComparison.OrdinalIgnoreCase));
        if (tagToRemove != null)
        {
            _tags.Remove(tagToRemove);
            UpdateTimestamp();
        }
    }
    
    public void ClearTags()
    {
        _tags.Clear();
        UpdateTimestamp();
    }
    
    public void SetInitialVersion()
    {
        if (!_versions.Any())
        {
            // Create initial version if it doesn't exist
            var initialVersion = new AgentVersion(Id, 1);
            _versions.Add(initialVersion);
            CurrentVersionId = initialVersion.Id;
            UpdateTimestamp();
        }
        else if (CurrentVersionId == null)
        {
            // Set current version if versions exist but CurrentVersionId is null
            var initialVersion = _versions.First();
            CurrentVersionId = initialVersion.Id;
            UpdateTimestamp();
        }
    }
    
    public void IncrementUsageCount()
    {
        UsageCount++;
        UpdateTimestamp();
    }
    
    public void IncrementViewCount()
    {
        ViewCount++;
        UpdateTimestamp();
    }
    
    public void AddRating(Guid userId, int score, string? feedback = null)
    {
        if (score < 1 || score > 5)
            throw new ValidationException("Score", "评分必须在1-5之间");
            
        var existingRating = _ratings.FirstOrDefault(r => r.UserId == userId);
        if (existingRating != null)
        {
            existingRating.Update(score, feedback);
        }
        else
        {
            _ratings.Add(new AgentRating(userId, score, feedback));
        }
        
        RecalculateRating();
        UpdateTimestamp();
    }
    
    private void RecalculateRating()
    {
        if (_ratings.Count == 0)
        {
            AverageRating = 0;
            RatingCount = 0;
        }
        else
        {
            AverageRating = Math.Round(_ratings.Average(r => r.Score), 1);
            RatingCount = _ratings.Count;
        }
    }
    
    private void SetName(string name)
    {
        if (string.IsNullOrWhiteSpace(name))
            throw new ValidationException("Name", "智能体名称不能为空");
            
        if (name.Length > 100)
            throw new ValidationException("Name", "智能体名称不能超过100个字符");
            
        Name = name;
    }
    
    private void SetUniqueKey(string? uniqueKey)
    {
        if (string.IsNullOrWhiteSpace(uniqueKey))
        {
            UniqueKey = null;
            return;
        }
        
        if (!global::System.Text.RegularExpressions.Regex.IsMatch(uniqueKey, @"^[a-zA-Z0-9_-]+$"))
            throw new ValidationException("UniqueKey", "唯一标识只能包含字母、数字、下划线和连字符");
            
        if (uniqueKey.Length < 3 || uniqueKey.Length > 50)
            throw new ValidationException("UniqueKey", "唯一标识长度必须在3-50个字符之间");
            
        UniqueKey = uniqueKey.ToLowerInvariant();
    }
    
    // API Key Management Methods
    public void AddDifyApiKey(string encryptedApiKey, string difyAppId)
    {
        if (CurrentVersion?.ModelConfig?.ModelType != "Dify")
            throw new BusinessException("只有Dify类型的智能体才能添加Dify API密钥");
            
        // Check if we already have a Dify API key
        var existingDifyKey = _apiKeys.FirstOrDefault(k => k.KeyType == "Dify");
        if (existingDifyKey != null)
        {
            existingDifyKey.UpdateApiKey(encryptedApiKey);
        }
        else
        {
            var agentApiKey = AgentApiKey.CreateForDify(Id, encryptedApiKey, difyAppId);
            _apiKeys.Add(agentApiKey);
        }
        
        UpdateTimestamp();
    }
    
    public void AddSystemProviderApiKey(string providerName, string encryptedApiKey)
    {
        if (CurrentVersion?.ModelConfig?.ModelType == "Dify")
            throw new BusinessException("Dify类型的智能体不使用系统供应商API密钥");
            
        // Check if we already have a key for this provider
        var existingKey = _apiKeys.FirstOrDefault(k => k.ProviderName == providerName && k.KeyType == "SystemProvider");
        if (existingKey != null)
        {
            existingKey.UpdateApiKey(encryptedApiKey);
        }
        else
        {
            var agentApiKey = AgentApiKey.CreateForSystemProvider(Id, providerName, encryptedApiKey);
            _apiKeys.Add(agentApiKey);
        }
        
        UpdateTimestamp();
    }
    
    public void RemoveApiKey(Guid apiKeyId)
    {
        var apiKey = _apiKeys.FirstOrDefault(k => k.Id == apiKeyId);
        if (apiKey != null)
        {
            _apiKeys.Remove(apiKey);
            UpdateTimestamp();
        }
    }
    
    public AgentApiKey? GetActiveApiKey()
    {
        return _apiKeys.FirstOrDefault(k => k.IsValid());
    }
    
    public AgentApiKey? GetDifyApiKey()
    {
        return _apiKeys.FirstOrDefault(k => k.KeyType == "Dify" && k.IsValid());
    }
    
    public void IncrementLikeCount()
    {
        LikeCount++;
        UpdateTimestamp();
    }
    
    public void DecrementLikeCount()
    {
        if (LikeCount > 0)
        {
            LikeCount--;
            UpdateTimestamp();
        }
    }
}