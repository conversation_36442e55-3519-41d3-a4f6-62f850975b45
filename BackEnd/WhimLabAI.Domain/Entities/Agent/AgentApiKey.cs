using WhimLabAI.Domain.Common;
using WhimLabAI.Shared.Exceptions;

namespace WhimLabAI.Domain.Entities.Agent;

/// <summary>
/// Agent API密钥实体
/// 支持两种策略：
/// 1. Semantic Kernel - 共享系统级供应商密钥
/// 2. Dify - 每个Agent实例独立密钥
/// </summary>
public class AgentApiKey : Entity
{
    public Guid AgentId { get; private set; }
    public string KeyType { get; private set; } // "Dify" or "SystemProvider"
    public string ProviderName { get; private set; } // "OpenAI", "Anthropic", "Dify", etc.
    public string EncryptedKey { get; private set; }
    public string? DifyAppId { get; private set; }
    public bool IsActive { get; private set; }
    public DateTime? ExpiresAt { get; private set; }
    
    private AgentApiKey() : base() { }
    
    /// <summary>
    /// 创建Dify类型的API密钥
    /// </summary>
    /// <param name="agentId">代理ID</param>
    /// <param name="encryptedApiKey">已加密的API密钥</param>
    /// <param name="difyAppId">Dify应用ID</param>
    public static AgentApiKey CreateForDify(
        Guid agentId, 
        string encryptedApiKey, 
        string difyAppId)
    {
        if (string.IsNullOrWhiteSpace(encryptedApiKey))
            throw new ValidationException("EncryptedApiKey", "加密的API密钥不能为空");
            
        if (string.IsNullOrWhiteSpace(difyAppId))
            throw new ValidationException("DifyAppId", "Dify应用ID不能为空");
            
        return new AgentApiKey
        {
            Id = Guid.NewGuid(),
            AgentId = agentId,
            KeyType = "Dify",
            ProviderName = "Dify",
            EncryptedKey = encryptedApiKey,
            DifyAppId = difyAppId,
            IsActive = true,
            CreatedAt = DateTime.UtcNow
        };
    }
    
    /// <summary>
    /// 创建系统供应商类型的API密钥
    /// </summary>
    /// <param name="agentId">代理ID</param>
    /// <param name="providerName">供应商名称</param>
    /// <param name="encryptedApiKey">已加密的API密钥</param>
    public static AgentApiKey CreateForSystemProvider(
        Guid agentId,
        string providerName,
        string encryptedApiKey)
    {
        if (string.IsNullOrWhiteSpace(providerName))
            throw new ValidationException("ProviderName", "供应商名称不能为空");
            
        if (string.IsNullOrWhiteSpace(encryptedApiKey))
            throw new ValidationException("EncryptedApiKey", "加密的API密钥不能为空");
            
        return new AgentApiKey
        {
            Id = Guid.NewGuid(),
            AgentId = agentId,
            KeyType = "SystemProvider",
            ProviderName = providerName,
            EncryptedKey = encryptedApiKey,
            IsActive = true,
            CreatedAt = DateTime.UtcNow
        };
    }
    
    /// <summary>
    /// 更新API密钥
    /// </summary>
    /// <param name="newEncryptedApiKey">新的已加密API密钥</param>
    public void UpdateApiKey(string newEncryptedApiKey)
    {
        if (string.IsNullOrWhiteSpace(newEncryptedApiKey))
            throw new ValidationException("EncryptedApiKey", "加密的API密钥不能为空");
            
        EncryptedKey = newEncryptedApiKey;
        UpdateTimestamp();
    }
    
    /// <summary>
    /// 设置过期时间
    /// </summary>
    public void SetExpiration(DateTime? expiresAt)
    {
        ExpiresAt = expiresAt;
        UpdateTimestamp();
    }
    
    /// <summary>
    /// 停用密钥
    /// </summary>
    public void Deactivate()
    {
        IsActive = false;
        UpdateTimestamp();
    }
    
    /// <summary>
    /// 激活密钥
    /// </summary>
    public void Activate()
    {
        IsActive = true;
        UpdateTimestamp();
    }
    
    /// <summary>
    /// 检查密钥是否过期
    /// </summary>
    public bool IsExpired()
    {
        return ExpiresAt.HasValue && ExpiresAt.Value < DateTime.UtcNow;
    }
    
    /// <summary>
    /// 检查密钥是否可用
    /// </summary>
    public bool IsValid()
    {
        return IsActive && !IsExpired();
    }
    
    /// <summary>
    /// 获取加密的API密钥
    /// </summary>
    /// <returns>加密的API密钥</returns>
    public string GetEncryptedApiKey()
    {
        return EncryptedKey;
    }
    
    /// <summary>
    /// 获取加密上下文信息，用于解密
    /// </summary>
    /// <returns>加密上下文</returns>
    public string GetEncryptionContext()
    {
        return $"AgentApiKey_{AgentId}_{Id}";
    }
}