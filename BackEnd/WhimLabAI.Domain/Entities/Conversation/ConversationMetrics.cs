using System;
using WhimLabAI.Domain.Common;

namespace WhimLabAI.Domain.Entities.Conversation;

/// <summary>
/// 对话度量数据
/// </summary>
public class ConversationMetrics : Entity
{
    /// <summary>
    /// 对话ID
    /// </summary>
    public Guid ConversationId { get; set; }
    
    /// <summary>
    /// 关联对话
    /// </summary>
    public Conversation Conversation { get; set; } = null!;
    
    /// <summary>
    /// 首次响应时间（秒）
    /// </summary>
    public double? FirstResponseTime { get; set; }
    
    /// <summary>
    /// 平均响应时间（秒）
    /// </summary>
    public double? AverageResponseTime { get; set; }
    
    /// <summary>
    /// 最长响应时间（秒）
    /// </summary>
    public double? MaxResponseTime { get; set; }
    
    /// <summary>
    /// 最短响应时间（秒）
    /// </summary>
    public double? MinResponseTime { get; set; }
    
    /// <summary>
    /// 总消息数
    /// </summary>
    public int TotalMessages { get; set; }
    
    /// <summary>
    /// AI响应次数
    /// </summary>
    public int AiResponseCount { get; set; }
    
    /// <summary>
    /// 用户消息数
    /// </summary>
    public int UserMessageCount { get; set; }
    
    /// <summary>
    /// 会话持续时间（分钟）
    /// </summary>
    public double SessionDuration { get; set; }
    
    /// <summary>
    /// 是否完成（基于业务规则判断）
    /// </summary>
    public bool IsCompleted { get; set; }
    
    /// <summary>
    /// 用户满意度评分（1-5）
    /// </summary>
    public int? SatisfactionScore { get; set; }
    
    /// <summary>
    /// 解决率（0-100）
    /// </summary>
    public double? ResolutionRate { get; set; }
    
    /// <summary>
    /// 最后更新时间
    /// </summary>
    public DateTime LastCalculatedAt { get; set; }
}