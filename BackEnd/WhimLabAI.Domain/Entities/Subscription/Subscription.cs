using WhimLabAI.Domain.Common;
using WhimLabAI.Domain.DomainEvents;
using WhimLabAI.Domain.ValueObjects;
using WhimLabAI.Shared.Exceptions;
using WhimLabAI.Shared.Enums;

namespace WhimLabAI.Domain.Entities.Subscription;

public class Subscription : AggregateRoot
{
    private readonly List<UsageRecord> _usageRecords = new();
    
    public Guid CustomerUserId { get; set; }
    public Guid PlanId { get; set; }
    public SubscriptionStatus Status { get; set; }
    public DateTime StartDate { get; set; }
    public DateTime? EndDate { get; set; }
    public bool AutoRenew { get; set; }
    public int RemainingTokens { get; set; }
    public string? PaymentMethod { get; set; }
    public Guid? OrderId { get; set; }
    public DateTime? NextBillingDate { get; set; }
    public DateTime? CancellationDate { get; set; }
    public string? CancellationReason { get; set; }
    public DateTime? LastResetDate { get; set; }
    public DateTime? NextResetDate { get; set; }
    public DateTime? LastRenewalDate { get; set; }
    public DateTime? PauseDate { get; set; }
    public DateTime? ResumeDate { get; set; }
    public DateTime? ExpiredAt { get; set; }
    public Guid? NextPlanId { get; set; }
    public TokenQuota TokenQuota { get; private set; }
    public Money PaidAmount { get; private set; }
    
    public SubscriptionPlan? Plan { get; set; }
    public IReadOnlyCollection<UsageRecord> UsageRecords => _usageRecords.AsReadOnly();
    
    private Subscription() : base()
    {
        TokenQuota = TokenQuota.Create(0);
        PaidAmount = Money.Zero("CNY");
    }
    
    public Subscription(
        Guid customerUserId,
        Guid planId,
        DateTime startDate,
        DateTime? endDate,
        string? paymentMethod = null,
        Guid? orderId = null,
        bool autoRenew = true) : base()
    {
        CustomerUserId = customerUserId;
        PlanId = planId;
        StartDate = startDate;
        EndDate = endDate;
        PaymentMethod = paymentMethod;
        OrderId = orderId;
        AutoRenew = autoRenew;
        Status = SubscriptionStatus.Pending;
        RemainingTokens = 0; // Will be set when activated with plan info
        LastResetDate = DateTime.UtcNow;
        
        // Set NextResetDate to first day of next month
        var now = DateTime.UtcNow;
        var nextMonth = now.AddMonths(1);
        NextResetDate = new DateTime(nextMonth.Year, nextMonth.Month, 1, 0, 0, 0, DateTimeKind.Utc);
        
        TokenQuota = TokenQuota.Create(0); // Will be set when activated with plan info
        PaidAmount = Money.Zero("CNY"); // Will be set when activated with plan info
        
        if (autoRenew && endDate.HasValue)
        {
            NextBillingDate = endDate;
        }
    }
    
    public void Activate()
    {
        if (Status != SubscriptionStatus.Pending)
            throw new BusinessException("只有待激活的订阅才能激活");
            
        Status = SubscriptionStatus.Active;
        UpdateTimestamp();
        
        RaiseDomainEvent(new SubscriptionActivatedEvent(Id, CustomerUserId, PlanId));
    }
    
    public void SetPlanInfo(int tokenQuota, Money paidAmount)
    {
        TokenQuota = tokenQuota == -1 ? TokenQuota.Unlimited() : TokenQuota.Create(tokenQuota);
        PaidAmount = paidAmount ?? throw new ArgumentNullException(nameof(paidAmount));
        RemainingTokens = tokenQuota;
        UpdateTimestamp();
    }
    
    public void Cancel(string? reason = null)
    {
        if (Status == SubscriptionStatus.Cancelled)
            throw new BusinessException("订阅已经取消");
            
        if (Status == SubscriptionStatus.Expired)
            throw new BusinessException("已过期的订阅不能取消");
            
        Status = SubscriptionStatus.Cancelled;
        CancellationDate = DateTime.UtcNow;
        CancellationReason = reason;
        AutoRenew = false;
        NextBillingDate = null;
        
        UpdateTimestamp();
    }
    
    public void Expire()
    {
        if (Status != SubscriptionStatus.Active)
            throw new BusinessException("只有活跃的订阅才能过期");
            
        Status = SubscriptionStatus.Expired;
        ExpiredAt = DateTime.UtcNow;
        UpdateTimestamp();
    }
    
    public void Renew(DateTime newEndDate, Guid? orderId = null)
    {
        if (Status != SubscriptionStatus.Active && Status != SubscriptionStatus.Expired)
            throw new BusinessException("只有活跃或过期的订阅才能续费");
            
        EndDate = newEndDate;
        Status = SubscriptionStatus.Active;
        LastRenewalDate = DateTime.UtcNow;
        OrderId = orderId;
        
        if (AutoRenew)
        {
            NextBillingDate = newEndDate;
        }
        
        UpdateTimestamp();
    }
    
    public void UseTokens(int tokens)
    {
        if (Status == SubscriptionStatus.Paused)
            throw new BusinessException("暂停的订阅不能使用Token");
            
        if (Status != SubscriptionStatus.Active)
            throw new BusinessException("只有活跃的订阅才能使用Token");
            
        if (EndDate.HasValue && DateTime.UtcNow > EndDate)
            throw new BusinessException("订阅已过期");
            
        if (!TokenQuota.CanUse(tokens))
            throw new BusinessException("Token额度不足");
            
        TokenQuota = TokenQuota.Use(tokens);
        RemainingTokens = TokenQuota.Remaining;
        
        // Check if quota is exhausted
        if (TokenQuota.IsExhausted())
        {
            RaiseDomainEvent(new QuotaExhaustedEvent(Id, CustomerUserId));
        }
        // Check if quota is low (< 10%)
        else if (!TokenQuota.IsUnlimited && Plan != null && TokenQuota.Remaining < Plan.MonthlyTokens * 0.1)
        {
            var usagePercentage = (1 - (double)TokenQuota.Remaining / Plan.MonthlyTokens) * 100;
            RaiseDomainEvent(new QuotaAlertEvent(Id, CustomerUserId, usagePercentage));
        }
        
        UpdateTimestamp();
    }
    
    public void ResetTokens(int newTotal)
    {
        TokenQuota = newTotal == -1 ? TokenQuota.Unlimited() : TokenQuota.Create(newTotal);
        RemainingTokens = TokenQuota.Remaining;
        LastResetDate = DateTime.UtcNow;
        UpdateTimestamp();
    }
    
    public void ResetMonthlyTokens()
    {
        if (Status != SubscriptionStatus.Active)
            return; // Skip inactive subscriptions
            
        // Reset tokens to monthly limit
        TokenQuota = TokenQuota.Reset();
        RemainingTokens = TokenQuota.Remaining;
        LastResetDate = DateTime.UtcNow;
        
        // Calculate next reset date (first day of next month)
        var now = DateTime.UtcNow;
        var nextMonth = now.AddMonths(1);
        NextResetDate = new DateTime(nextMonth.Year, nextMonth.Month, 1, 0, 0, 0, DateTimeKind.Utc);
        
        UpdateTimestamp();
    }
    
    public void AddTokens(int tokensToAdd, string reason)
    {
        if (Status != SubscriptionStatus.Active)
            throw new BusinessException("只有活跃的订阅才能添加Token");
            
        if (tokensToAdd <= 0)
            throw new ArgumentException("Token数量必须大于0", nameof(tokensToAdd));
            
        // Add tokens to the quota
        var newTotal = TokenQuota.Total + tokensToAdd;
        var newRemaining = RemainingTokens + tokensToAdd;
        
        TokenQuota = TokenQuota.Create(newTotal, newRemaining);
        RemainingTokens = newRemaining;
        
        UpdateTimestamp();
        
        // Raise domain event for token addition
        RaiseDomainEvent(new TokensAddedEvent(Id, CustomerUserId, tokensToAdd, reason));
    }
    
    public void EnableAutoRenew()
    {
        if (Status != SubscriptionStatus.Active)
            throw new BusinessException("只有活跃的订阅才能启用自动续费");
            
        AutoRenew = true;
        NextBillingDate = EndDate;
        UpdateTimestamp();
    }
    
    public void DisableAutoRenew()
    {
        AutoRenew = false;
        NextBillingDate = null;
        UpdateTimestamp();
    }
    
    public bool IsValid()
    {
        return (Status == SubscriptionStatus.Active || Status == SubscriptionStatus.Paused) && 
               DateTime.UtcNow >= StartDate && 
               (!EndDate.HasValue || DateTime.UtcNow <= EndDate);
    }
    
    public int GetDaysRemaining()
    {
        if (!EndDate.HasValue)
            return int.MaxValue; // No expiration
            
        if (DateTime.UtcNow > EndDate)
            return 0;
            
        return (EndDate.Value - DateTime.UtcNow).Days;
    }
    
    public void Pause(DateTime resumeDate)
    {
        if (Status != SubscriptionStatus.Active)
            throw new BusinessException("只有活跃的订阅才能暂停");
            
        if (resumeDate <= DateTime.UtcNow)
            throw new BusinessException("恢复日期必须是未来的日期");
            
        Status = SubscriptionStatus.Paused;
        PauseDate = DateTime.UtcNow;
        ResumeDate = resumeDate;
        
        // Extend the subscription end date by the pause duration
        if (EndDate.HasValue)
        {
            var pauseDuration = resumeDate - DateTime.UtcNow;
            EndDate = EndDate.Value.Add(pauseDuration);
            
            // Also extend next billing date if auto-renew is enabled
            if (AutoRenew && NextBillingDate.HasValue)
            {
                NextBillingDate = NextBillingDate.Value.Add(pauseDuration);
            }
        }
        
        UpdateTimestamp();
    }
    
    public void Resume()
    {
        if (Status != SubscriptionStatus.Paused)
            throw new BusinessException("只有暂停的订阅才能恢复");
            
        // Check if subscription has expired during pause
        if (EndDate.HasValue && DateTime.UtcNow > EndDate)
        {
            Status = SubscriptionStatus.Expired;
            ExpiredAt = DateTime.UtcNow;
        }
        else
        {
            Status = SubscriptionStatus.Active;
        }
        
        // Clear pause-related dates
        PauseDate = null;
        ResumeDate = null;
        
        UpdateTimestamp();
    }
    
    public bool ShouldAutoResume()
    {
        return Status == SubscriptionStatus.Paused && 
               ResumeDate.HasValue && 
               DateTime.UtcNow >= ResumeDate;
    }
    
}