using WhimLabAI.Domain.Common;

namespace WhimLabAI.Domain.Entities.Auth;

/// <summary>
/// 权限实体 - 原子化的权限点
/// </summary>
public class Permission : Entity
{
    /// <summary>
    /// 权限代码（如 users:customers:read）
    /// </summary>
    public string Code { get; private set; }
    
    /// <summary>
    /// 权限名称
    /// </summary>
    public string Name { get; private set; }
    
    /// <summary>
    /// 权限描述
    /// </summary>
    public string? Description { get; private set; }
    
    /// <summary>
    /// 权限分类（如 users, agents, subscriptions）
    /// </summary>
    public string Category { get; private set; }
    
    /// <summary>
    /// 父权限ID（用于构建权限树）
    /// </summary>
    public Guid? ParentId { get; private set; }
    
    /// <summary>
    /// 排序顺序
    /// </summary>
    public int DisplayOrder { get; private set; }
    
    /// <summary>
    /// 是否启用
    /// </summary>
    public bool IsEnabled { get; private set; }
    
    /// <summary>
    /// 是否为系统预设权限（不可删除）
    /// </summary>
    public bool IsSystem { get; private set; }
    
    private Permission() : base()
    {
        // Required for EF Core
    }
    
    public Permission(
        string code,
        string name,
        string category,
        string? description = null,
        Guid? parentId = null,
        bool isSystem = false) : base()
    {
        SetCode(code);
        SetName(name);
        SetCategory(category);
        Description = description;
        ParentId = parentId;
        IsEnabled = true;
        IsSystem = isSystem;
        DisplayOrder = 0;
    }
    
    public void Update(
        string name,
        string? description,
        int displayOrder)
    {
        SetName(name);
        Description = description;
        DisplayOrder = displayOrder;
        UpdateTimestamp();
    }
    
    public void Enable()
    {
        IsEnabled = true;
        UpdateTimestamp();
    }
    
    public void Disable()
    {
        IsEnabled = false;
        UpdateTimestamp();
    }
    
    private void SetCode(string code)
    {
        if (string.IsNullOrWhiteSpace(code))
            throw new ArgumentNullException(nameof(code), "权限代码不能为空");
            
        if (!global::System.Text.RegularExpressions.Regex.IsMatch(code, @"^[a-z]+(:?[a-z_]+)*$"))
            throw new ArgumentException("权限代码格式不正确，应为小写字母和下划线，用冒号分隔", nameof(code));
            
        Code = code;
    }
    
    private void SetName(string name)
    {
        if (string.IsNullOrWhiteSpace(name))
            throw new ArgumentNullException(nameof(name), "权限名称不能为空");
            
        Name = name;
    }
    
    private void SetCategory(string category)
    {
        if (string.IsNullOrWhiteSpace(category))
            throw new ArgumentNullException(nameof(category), "权限分类不能为空");
            
        Category = category;
    }
}