using System;
using WhimLabAI.Domain.Common;
using WhimLabAI.Domain.Entities.User;
using WhimLabAI.Domain.ValueObjects;
using WhimLabAI.Shared.Enums;

namespace WhimLabAI.Domain.Entities.Payment;

/// <summary>
/// 退款申请实体
/// </summary>
public class RefundRequest : Entity
{
    /// <summary>
    /// 订单ID
    /// </summary>
    public Guid OrderId { get; set; }
    
    /// <summary>
    /// 关联订单
    /// </summary>
    public Order Order { get; set; } = null!;
    
    /// <summary>
    /// 客户用户ID
    /// </summary>
    public Guid CustomerUserId { get; set; }
    
    /// <summary>
    /// 客户用户
    /// </summary>
    public CustomerUser CustomerUser { get; set; } = null!;
    
    /// <summary>
    /// 退款金额
    /// </summary>
    public Money RefundAmount { get; set; } = null!;
    
    /// <summary>
    /// 退款原因类型
    /// </summary>
    public RefundReasonType ReasonType { get; set; }
    
    /// <summary>
    /// 退款原因详细描述
    /// </summary>
    public string? ReasonDetails { get; set; }
    
    /// <summary>
    /// 退款状态
    /// </summary>
    public RefundStatus Status { get; set; }
    
    /// <summary>
    /// 申请时间
    /// </summary>
    public DateTime RequestedAt { get; set; }
    
    /// <summary>
    /// 审批时间
    /// </summary>
    public DateTime? ApprovedAt { get; set; }
    
    /// <summary>
    /// 拒绝时间
    /// </summary>
    public DateTime? RejectedAt { get; set; }
    
    /// <summary>
    /// 完成时间
    /// </summary>
    public DateTime? CompletedAt { get; set; }
    
    /// <summary>
    /// 审批人ID
    /// </summary>
    public Guid? ApprovedByAdminId { get; set; }
    
    /// <summary>
    /// 审批备注
    /// </summary>
    public string? ApprovalNotes { get; set; }
    
    /// <summary>
    /// 退款交易ID
    /// </summary>
    public string? RefundTransactionId { get; set; }
    
    /// <summary>
    /// 处理时长（小时）
    /// </summary>
    public double? ProcessingDuration => CompletedAt.HasValue && RequestedAt != DateTime.MinValue
        ? (CompletedAt.Value - RequestedAt).TotalHours
        : null;
}