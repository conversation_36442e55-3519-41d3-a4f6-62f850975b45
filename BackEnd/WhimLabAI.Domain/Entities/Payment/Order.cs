using WhimLabAI.Domain.Common;
using WhimLabAI.Domain.DomainEvents;
using WhimLabAI.Domain.ValueObjects;
using WhimLabAI.Shared.Enums;
using WhimLabAI.Shared.Exceptions;

namespace WhimLabAI.Domain.Entities.Payment;

public class Order : AggregateRoot, ISoftDelete
{
    private readonly List<PaymentTransaction> _transactions = new();
    private readonly List<RefundRecord> _refunds = new();
    
    public string OrderNo { get; private set; }
    public Guid CustomerUserId { get; private set; }
    public OrderType Type { get; private set; }
    public Guid? ProductId { get; private set; }
    public string? ProductName { get; private set; }
    public Money Amount { get; private set; }
    public Money? DiscountAmount { get; private set; }
    public Money FinalAmount { get; private set; }
    public Money PayableAmount { get; private set; }
    public PaymentMethod PaymentMethod { get; private set; }
    public OrderStatus Status { get; private set; }
    public DateTime? PaidAt { get; private set; }
    public DateTime? RefundedAt { get; private set; }
    public DateTime ExpireAt { get; private set; }
    public string? CouponCode { get; private set; }
    public Guid? CouponId { get; private set; }
    public string? Remark { get; private set; }
    public Dictionary<string, object> Metadata { get; private set; }
    
    // ISoftDelete implementation
    public bool IsDeleted { get; private set; }
    public DateTime? DeletedAt { get; private set; }
    
    public IReadOnlyCollection<PaymentTransaction> Transactions => _transactions.AsReadOnly();
    public IReadOnlyCollection<RefundRecord> Refunds => _refunds.AsReadOnly();
    
    private Order() : base()
    {
        Metadata = new Dictionary<string, object>();
    }
    
    public Order(
        string orderNo,
        Guid customerUserId,
        OrderType type,
        Money amount,
        PaymentMethod paymentMethod,
        Guid? productId = null,
        string? productName = null,
        string? remark = null,
        int expireMinutes = 30,
        Dictionary<string, object>? metadata = null) : base()
    {
        OrderNo = orderNo ?? throw new ArgumentNullException(nameof(orderNo));
        CustomerUserId = customerUserId;
        Type = type;
        Amount = amount ?? throw new ArgumentNullException(nameof(amount));
        FinalAmount = amount;
        PayableAmount = amount;
        PaymentMethod = paymentMethod;
        ProductId = productId;
        ProductName = productName;
        Remark = remark;
        Status = OrderStatus.Pending;
        ExpireAt = DateTime.UtcNow.AddMinutes(expireMinutes);
        Metadata = metadata ?? new Dictionary<string, object>();
    }
    
    public void ApplyCoupon(Guid couponId, string couponCode, Money discountAmount)
    {
        if (Status != OrderStatus.Pending)
            throw new BusinessException("只有待支付的订单才能使用优惠券");
            
        if (discountAmount.Currency != Amount.Currency)
            throw new BusinessException("优惠券金额货币类型不匹配");
            
        if (discountAmount > Amount)
            throw new BusinessException("优惠券金额不能超过订单金额");
            
        CouponId = couponId;
        CouponCode = couponCode;
        DiscountAmount = discountAmount;
        FinalAmount = Amount.Subtract(discountAmount);
        PayableAmount = FinalAmount;
        
        UpdateTimestamp();
    }
    
    public void RemoveCoupon()
    {
        if (Status != OrderStatus.Pending)
            throw new BusinessException("只有待支付的订单才能移除优惠券");
            
        CouponId = null;
        CouponCode = null;
        DiscountAmount = null;
        FinalAmount = Amount;
        PayableAmount = Amount;
        
        UpdateTimestamp();
    }
    
    public void UpdateRemark(string? remark)
    {
        Remark = remark;
        UpdateTimestamp();
    }
    
    public PaymentTransaction CreatePayment(string? transactionId = null)
    {
        if (Status != OrderStatus.Pending)
            throw new BusinessException("只有待支付的订单才能创建支付");
            
        var transaction = new PaymentTransaction(
            Id,
            FinalAmount,
            PaymentMethod,
            transactionId);
            
        _transactions.Add(transaction);
        UpdateTimestamp();
        
        return transaction;
    }
    
    public void MarkAsPaid(string transactionId, DateTime? paidAt = null)
    {
        if (Status == OrderStatus.Paid)
            throw new BusinessException("订单已支付");
            
        if (Status == OrderStatus.Refunded)
            throw new BusinessException("已退款的订单不能标记为已支付");
            
        var transaction = _transactions.FirstOrDefault(t => t.TransactionId == transactionId);
        if (transaction == null)
        {
            transaction = CreatePayment(transactionId);
        }
        
        transaction.MarkAsSuccess();
        Status = OrderStatus.Paid;
        PaidAt = paidAt ?? DateTime.UtcNow;
        
        UpdateTimestamp();
        RaiseDomainEvent(new OrderPaidEvent(Id, CustomerUserId, FinalAmount));
    }
    
    public void MarkAsFailed(string? reason = null)
    {
        if (Status != OrderStatus.Pending)
            throw new BusinessException("只有待支付的订单才能标记为失败");
            
        Status = OrderStatus.Failed;
        
        var latestTransaction = _transactions.OrderByDescending(t => t.CreatedAt).FirstOrDefault();
        latestTransaction?.MarkAsFailed(reason);
        
        UpdateTimestamp();
    }
    
    public void Cancel(string? reason = null)
    {
        if (Status != OrderStatus.Pending)
            throw new BusinessException("只有待支付的订单才能取消");
            
        Status = OrderStatus.Cancelled;
        SetMetadata("CancellationReason", reason ?? "用户取消");
        
        UpdateTimestamp();
    }
    
    public void Expire()
    {
        if (Status != OrderStatus.Pending)
            throw new BusinessException("只有待支付的订单才能过期");
            
        Status = OrderStatus.Expired;
        UpdateTimestamp();
    }
    
    public RefundRecord Refund(Money refundAmount, string reason, string? refundNo = null)
    {
        if (Status != OrderStatus.Paid)
            throw new BusinessException("只有已支付的订单才能退款");
            
        if (refundAmount.Currency != FinalAmount.Currency)
            throw new BusinessException("退款金额货币类型不匹配");
            
        var totalRefunded = GetTotalRefundedAmount();
        if (totalRefunded.Add(refundAmount) > FinalAmount)
            throw new BusinessException("退款总额不能超过订单金额");
            
        var refund = new RefundRecord(
            Id,
            refundAmount,
            reason,
            refundNo);
            
        _refunds.Add(refund);
        
        // If fully refunded, update order status
        if (totalRefunded.Add(refundAmount) == FinalAmount)
        {
            Status = OrderStatus.Refunded;
            RefundedAt = DateTime.UtcNow;
        }
        
        UpdateTimestamp();
        return refund;
    }
    
    public void CompleteRefund(string refundNo)
    {
        var refund = _refunds.FirstOrDefault(r => r.RefundNo == refundNo);
        if (refund == null)
            throw new NotFoundException("Refund", refundNo);
            
        refund.MarkAsCompleted();
        
        // Check if all refunds are completed and update status
        var totalRefunded = GetTotalRefundedAmount();
        if (totalRefunded == FinalAmount && _refunds.All(r => r.Status == RefundStatus.Completed))
        {
            Status = OrderStatus.Refunded;
            RefundedAt = DateTime.UtcNow;
        }
        
        UpdateTimestamp();
    }
    
    public Money GetTotalRefundedAmount()
    {
        if (!_refunds.Any())
            return Money.Zero(FinalAmount.Currency);
            
        return _refunds
            .Where(r => r.Status == RefundStatus.Completed || r.Status == RefundStatus.Processing)
            .Select(r => r.RefundAmount)
            .Aggregate((a, b) => a.Add(b));
    }
    
    public bool IsExpired()
    {
        return Status == OrderStatus.Pending && DateTime.UtcNow > ExpireAt;
    }
    
    public bool CanRefund()
    {
        return Status == OrderStatus.Paid && GetTotalRefundedAmount() < FinalAmount;
    }
    
    public void SetMetadata(string key, object value)
    {
        Metadata[key] = value;
        UpdateTimestamp();
    }
    
    public void RemoveMetadata(string key)
    {
        if (Metadata.Remove(key))
        {
            UpdateTimestamp();
        }
    }
    
    /// <summary>
    /// 软删除订单
    /// </summary>
    public void Delete()
    {
        // 只允许删除已取消或已过期的订单
        if (Status != OrderStatus.Cancelled && Status != OrderStatus.Expired)
        {
            throw new BusinessException("只能删除已取消或已过期的订单");
        }
        
        IsDeleted = true;
        DeletedAt = DateTime.UtcNow;
        UpdateTimestamp();
    }
    
    /// <summary>
    /// 恢复已删除的订单
    /// </summary>
    public void Restore()
    {
        if (!IsDeleted)
        {
            throw new BusinessException("订单未被删除");
        }
        
        IsDeleted = false;
        DeletedAt = null;
        UpdateTimestamp();
    }
    
    /// <summary>
    /// 记录错误信息
    /// </summary>
    public void RecordError(string errorCode, string errorMessage)
    {
        SetMetadata("LastErrorCode", errorCode);
        SetMetadata("LastErrorMessage", errorMessage);
        SetMetadata("LastErrorAt", DateTime.UtcNow.ToString("O"));
    }
    
    /// <summary>
    /// 检查订单是否已过期
    /// </summary>
    public bool HasExpired() => IsExpired();
    
    /// <summary>
    /// 标记为退款中
    /// </summary>
    public void MarkAsRefunding()
    {
        if (Status != OrderStatus.Paid)
            throw new BusinessException("只有已支付的订单才能标记为退款中");
            
        // This method is for tracking refund process status
        // The actual status change happens when refund is completed
        SetMetadata("RefundingStartedAt", DateTime.UtcNow.ToString("O"));
    }
    
    /// <summary>
    /// 标记为已退款
    /// </summary>
    public void MarkAsRefunded()
    {
        if (Status != OrderStatus.Paid)
            throw new BusinessException("只有已支付的订单才能标记为已退款");
            
        Status = OrderStatus.Refunded;
        RefundedAt = DateTime.UtcNow;
        UpdateTimestamp();
    }
    
    #region Factory Methods
    
    /// <summary>
    /// 创建订阅订单
    /// </summary>
    public static Order CreateForSubscription(
        Guid customerUserId,
        Guid planId,
        string planName,
        Money price,
        PaymentMethod paymentMethod,
        string? remark = null)
    {
        var orderNo = GenerateOrderNo();
        var order = new Order(
            orderNo,
            customerUserId,
            OrderType.Subscription,
            price,
            paymentMethod,
            planId,
            planName,
            remark,
            30); // 30 minutes expiration
            
        order.SetMetadata("PlanId", planId.ToString());
        return order;
    }
    
    /// <summary>
    /// 创建代币包订单
    /// </summary>
    public static Order CreateForTokenPackage(
        Guid customerUserId,
        Guid packageId,
        string packageName,
        Money price,
        int tokenAmount,
        PaymentMethod paymentMethod,
        string? remark = null)
    {
        var orderNo = GenerateOrderNo();
        var order = new Order(
            orderNo,
            customerUserId,
            OrderType.TokenPackage,
            price,
            paymentMethod,
            packageId,
            packageName,
            remark,
            30); // 30 minutes expiration
            
        order.SetMetadata("PackageId", packageId.ToString());
        order.SetMetadata("TokenAmount", tokenAmount);
        return order;
    }
    
    private static string GenerateOrderNo()
    {
        return $"ORD{DateTime.UtcNow:yyyyMMddHHmmss}{Guid.NewGuid().ToString("N").Substring(0, 8).ToUpper()}";
    }
    
    #endregion
}
