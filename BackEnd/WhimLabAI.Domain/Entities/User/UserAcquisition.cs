using System;
using WhimLabAI.Domain.Common;

namespace WhimLabAI.Domain.Entities.User;

/// <summary>
/// 用户获客来源记录
/// </summary>
public class UserAcquisition : Entity
{
    /// <summary>
    /// 客户用户ID
    /// </summary>
    public Guid CustomerUserId { get; set; }
    
    /// <summary>
    /// 客户用户
    /// </summary>
    public CustomerUser CustomerUser { get; set; } = null!;
    
    /// <summary>
    /// 获客渠道
    /// </summary>
    public string Channel { get; set; } = string.Empty;
    
    /// <summary>
    /// 子渠道（如具体的广告系列）
    /// </summary>
    public string? SubChannel { get; set; }
    
    /// <summary>
    /// 来源URL
    /// </summary>
    public string? SourceUrl { get; set; }
    
    /// <summary>
    /// UTM来源
    /// </summary>
    public string? UtmSource { get; set; }
    
    /// <summary>
    /// UTM媒介
    /// </summary>
    public string? UtmMedium { get; set; }
    
    /// <summary>
    /// UTM活动
    /// </summary>
    public string? UtmCampaign { get; set; }
    
    /// <summary>
    /// 推荐人用户ID（如果是推荐渠道）
    /// </summary>
    public Guid? ReferrerUserId { get; set; }
    
    /// <summary>
    /// 推荐人用户
    /// </summary>
    public CustomerUser? ReferrerUser { get; set; }
    
    /// <summary>
    /// 获客成本（人民币）
    /// </summary>
    public decimal AcquisitionCost { get; set; }
    
    /// <summary>
    /// 是否已转化（从免费用户转为付费用户）
    /// </summary>
    public bool IsConverted { get; set; }
    
    /// <summary>
    /// 转化日期
    /// </summary>
    public DateTime? ConversionDate { get; set; }
    
    /// <summary>
    /// 首次订阅金额
    /// </summary>
    public decimal? FirstSubscriptionAmount { get; set; }
}