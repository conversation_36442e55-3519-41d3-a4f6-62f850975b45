using System;
using WhimLabAI.Domain.Common;
using WhimLabAI.Shared.Enums;
using WhimLabAI.Shared.Exceptions;

namespace WhimLabAI.Domain.Entities.User;

/// <summary>
/// 账号注销请求实体
/// </summary>
public class AccountDeletionRequest : Entity
{
    /// <summary>
    /// 客户用户ID
    /// </summary>
    public Guid CustomerUserId { get; private set; }
    
    /// <summary>
    /// 关联的客户用户
    /// </summary>
    public CustomerUser CustomerUser { get; private set; } = null!;
    
    /// <summary>
    /// 注销原因
    /// </summary>
    public string Reason { get; private set; }
    
    /// <summary>
    /// 注销原因分类
    /// </summary>
    public DeletionReasonCategory ReasonCategory { get; private set; }
    
    /// <summary>
    /// 请求时的IP地址
    /// </summary>
    public string IpAddress { get; private set; }
    
    /// <summary>
    /// 请求时的用户代理
    /// </summary>
    public string? UserAgent { get; private set; }
    
    /// <summary>
    /// 请求状态
    /// </summary>
    public DeletionRequestStatus Status { get; private set; }
    
    /// <summary>
    /// 请求时间
    /// </summary>
    public DateTime RequestedAt { get; private set; }
    
    /// <summary>
    /// 冷静期结束时间（请求后7天）
    /// </summary>
    public DateTime CoolingOffEndAt { get; private set; }
    
    /// <summary>
    /// 计划删除时间（冷静期后30天）
    /// </summary>
    public DateTime PlannedDeletionAt { get; private set; }
    
    /// <summary>
    /// 实际删除时间
    /// </summary>
    public DateTime? ActualDeletionAt { get; private set; }
    
    /// <summary>
    /// 撤销时间
    /// </summary>
    public DateTime? CancelledAt { get; private set; }
    
    /// <summary>
    /// 撤销原因
    /// </summary>
    public string? CancellationReason { get; private set; }
    
    /// <summary>
    /// 是否已通知用户冷静期即将结束
    /// </summary>
    public bool CoolingOffReminderSent { get; private set; }
    
    /// <summary>
    /// 是否已通知用户账号已冻结
    /// </summary>
    public bool AccountFrozenNotificationSent { get; private set; }
    
    /// <summary>
    /// 验证码（用于邮件确认）
    /// </summary>
    public string? VerificationCode { get; private set; }
    
    /// <summary>
    /// 验证码是否已确认
    /// </summary>
    public bool IsVerified { get; private set; }
    
    /// <summary>
    /// 验证时间
    /// </summary>
    public DateTime? VerifiedAt { get; private set; }
    
    /// <summary>
    /// 创建者
    /// </summary>
    public string? CreatedBy { get; set; }
    
    private AccountDeletionRequest() : base()
    {
        // Required for EF Core
    }
    
    /// <summary>
    /// 创建账号注销请求
    /// </summary>
    public AccountDeletionRequest(
        Guid customerUserId,
        string reason,
        DeletionReasonCategory reasonCategory,
        string ipAddress,
        string? userAgent = null,
        string? verificationCode = null) : base()
    {
        if (customerUserId == Guid.Empty)
            throw new ValidationException(nameof(customerUserId), "用户ID不能为空");
            
        if (string.IsNullOrWhiteSpace(reason))
            throw new ValidationException(nameof(reason), "注销原因不能为空");
            
        if (string.IsNullOrWhiteSpace(ipAddress))
            throw new ValidationException(nameof(ipAddress), "IP地址不能为空");
        
        CustomerUserId = customerUserId;
        Reason = reason;
        ReasonCategory = reasonCategory;
        IpAddress = ipAddress;
        UserAgent = userAgent;
        VerificationCode = verificationCode;
        
        Status = DeletionRequestStatus.PendingVerification;
        RequestedAt = DateTime.UtcNow;
        CoolingOffEndAt = RequestedAt.AddDays(7);
        PlannedDeletionAt = CoolingOffEndAt.AddDays(30);
        
        IsVerified = string.IsNullOrEmpty(verificationCode); // 如果没有验证码，视为已验证
        VerifiedAt = IsVerified ? RequestedAt : null;
        
        CoolingOffReminderSent = false;
        AccountFrozenNotificationSent = false;
    }
    
    /// <summary>
    /// 验证请求
    /// </summary>
    public void Verify(string code)
    {
        if (Status != DeletionRequestStatus.PendingVerification)
            throw new BusinessException("该请求不在待验证状态");
            
        if (IsVerified)
            throw new BusinessException("该请求已经验证过了");
            
        if (string.IsNullOrEmpty(VerificationCode))
            throw new BusinessException("该请求不需要验证");
            
        if (VerificationCode != code)
            throw new BusinessException("验证码不正确");
            
        IsVerified = true;
        VerifiedAt = DateTime.UtcNow;
        Status = DeletionRequestStatus.InCoolingOff;
        UpdateTimestamp();
    }
    
    /// <summary>
    /// 开始冷静期
    /// </summary>
    public void StartCoolingOff()
    {
        if (Status != DeletionRequestStatus.PendingVerification)
            throw new BusinessException("只有待验证的请求才能开始冷静期");
            
        if (!IsVerified)
            throw new BusinessException("请求尚未验证");
            
        Status = DeletionRequestStatus.InCoolingOff;
        UpdateTimestamp();
    }
    
    /// <summary>
    /// 撤销注销请求
    /// </summary>
    public void Cancel(string reason)
    {
        if (Status == DeletionRequestStatus.Cancelled)
            throw new BusinessException("该请求已经被撤销");
            
        if (Status == DeletionRequestStatus.Completed)
            throw new BusinessException("该请求已经完成，无法撤销");
            
        if (Status == DeletionRequestStatus.DataAnonymized)
            throw new BusinessException("数据已匿名化，无法撤销");
            
        Status = DeletionRequestStatus.Cancelled;
        CancelledAt = DateTime.UtcNow;
        CancellationReason = reason;
        UpdateTimestamp();
    }
    
    /// <summary>
    /// 冻结账号（冷静期结束后）
    /// </summary>
    public void FreezeAccount()
    {
        if (Status != DeletionRequestStatus.InCoolingOff)
            throw new BusinessException("只有在冷静期的请求才能冻结账号");
            
        if (DateTime.UtcNow < CoolingOffEndAt)
            throw new BusinessException("冷静期尚未结束");
            
        Status = DeletionRequestStatus.AccountFrozen;
        UpdateTimestamp();
    }
    
    /// <summary>
    /// 完成数据匿名化
    /// </summary>
    public void CompleteAnonymization()
    {
        if (Status != DeletionRequestStatus.AccountFrozen)
            throw new BusinessException("只有已冻结的账号才能进行数据匿名化");
            
        if (DateTime.UtcNow < PlannedDeletionAt)
            throw new BusinessException("尚未到达计划删除时间");
            
        Status = DeletionRequestStatus.DataAnonymized;
        ActualDeletionAt = DateTime.UtcNow;
        UpdateTimestamp();
    }
    
    /// <summary>
    /// 完成删除
    /// </summary>
    public void Complete()
    {
        if (Status != DeletionRequestStatus.DataAnonymized)
            throw new BusinessException("只有已匿名化的请求才能标记为完成");
            
        Status = DeletionRequestStatus.Completed;
        UpdateTimestamp();
    }
    
    /// <summary>
    /// 标记已发送冷静期提醒
    /// </summary>
    public void MarkCoolingOffReminderSent()
    {
        CoolingOffReminderSent = true;
        UpdateTimestamp();
    }
    
    /// <summary>
    /// 标记已发送账号冻结通知
    /// </summary>
    public void MarkAccountFrozenNotificationSent()
    {
        AccountFrozenNotificationSent = true;
        UpdateTimestamp();
    }
    
    /// <summary>
    /// 是否在冷静期内
    /// </summary>
    public bool IsInCoolingOffPeriod()
    {
        return Status == DeletionRequestStatus.InCoolingOff && 
               DateTime.UtcNow >= RequestedAt && 
               DateTime.UtcNow < CoolingOffEndAt;
    }
    
    /// <summary>
    /// 是否可以撤销
    /// </summary>
    public bool CanBeCancelled()
    {
        return Status != DeletionRequestStatus.Cancelled &&
               Status != DeletionRequestStatus.Completed &&
               Status != DeletionRequestStatus.DataAnonymized;
    }
    
    /// <summary>
    /// 获取剩余冷静期天数
    /// </summary>
    public int GetRemainingCoolingOffDays()
    {
        if (Status != DeletionRequestStatus.InCoolingOff)
            return 0;
            
        var remaining = (CoolingOffEndAt - DateTime.UtcNow).Days;
        return Math.Max(0, remaining);
    }
}