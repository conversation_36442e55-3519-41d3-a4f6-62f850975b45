using System;
using WhimLabAI.Domain.Common;

namespace WhimLabAI.Domain.Entities.User;

/// <summary>
/// 用户登录历史记录
/// </summary>
public class UserLoginHistory : Entity
{
    /// <summary>
    /// 客户用户ID
    /// </summary>
    public Guid CustomerUserId { get; set; }
    
    /// <summary>
    /// 客户用户
    /// </summary>
    public CustomerUser CustomerUser { get; set; } = null!;
    
    /// <summary>
    /// 登录时间
    /// </summary>
    public DateTime LoginTime { get; set; }
    
    /// <summary>
    /// 登录IP地址
    /// </summary>
    public string? IpAddress { get; set; }
    
    /// <summary>
    /// 用户代理字符串
    /// </summary>
    public string? UserAgent { get; set; }
    
    /// <summary>
    /// 登录设备类型
    /// </summary>
    public string? DeviceType { get; set; }
    
    /// <summary>
    /// 登录位置（城市）
    /// </summary>
    public string? Location { get; set; }
    
    /// <summary>
    /// 登录是否成功
    /// </summary>
    public bool IsSuccessful { get; set; }
    
    /// <summary>
    /// 失败原因（如果登录失败）
    /// </summary>
    public string? FailureReason { get; set; }
}