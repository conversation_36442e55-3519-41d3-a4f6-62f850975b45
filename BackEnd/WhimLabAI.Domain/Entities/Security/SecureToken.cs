using System;
using WhimLabAI.Domain.Common;

namespace WhimLabAI.Domain.Entities.Security;

/// <summary>
/// Entity for storing encrypted tokens securely
/// </summary>
public class SecureToken : Entity, ISoftDelete
{
    /// <summary>
    /// Unique identifier for the token
    /// </summary>
    public Guid TokenId { get; private set; }
    
    /// <summary>
    /// User ID associated with the token
    /// </summary>
    public Guid UserId { get; private set; }
    
    /// <summary>
    /// Type of token (RefreshToken, ApiKey, etc.)
    /// </summary>
    public string TokenType { get; private set; }
    
    /// <summary>
    /// Encrypted token value
    /// </summary>
    public string EncryptedToken { get; private set; }
    
    /// <summary>
    /// Hash of the token for validation without decryption
    /// </summary>
    public string TokenHash { get; private set; }
    
    /// <summary>
    /// Salt used for encryption (unique per token)
    /// </summary>
    public string Salt { get; private set; }
    
    /// <summary>
    /// Initialization vector for encryption
    /// </summary>
    public string InitializationVector { get; private set; }
    
    /// <summary>
    /// Token expiration date
    /// </summary>
    public DateTime? ExpiresAt { get; private set; }
    
    /// <summary>
    /// Whether the token has been revoked
    /// </summary>
    public bool IsRevoked { get; private set; }
    
    /// <summary>
    /// Date when the token was revoked
    /// </summary>
    public DateTime? RevokedAt { get; private set; }
    
    /// <summary>
    /// Reason for revocation
    /// </summary>
    public string? RevocationReason { get; private set; }
    
    /// <summary>
    /// Last time the token was used
    /// </summary>
    public DateTime? LastUsedAt { get; private set; }
    
    /// <summary>
    /// Number of times the token has been used
    /// </summary>
    public int UsageCount { get; private set; }
    
    /// <summary>
    /// Optional metadata stored as JSON
    /// </summary>
    public string? Metadata { get; private set; }
    
    /// <summary>
    /// IP address from which the token was created
    /// </summary>
    public string? CreatedFromIp { get; private set; }
    
    /// <summary>
    /// User agent from which the token was created
    /// </summary>
    public string? CreatedFromUserAgent { get; private set; }

    // ISoftDelete implementation
    public bool IsDeleted { get; private set; }
    public DateTime? DeletedAt { get; private set; }
    public Guid? DeletedBy { get; private set; }

    /// <summary>
    /// Private constructor for EF Core
    /// </summary>
    private SecureToken() 
    {
        TokenType = string.Empty;
        EncryptedToken = string.Empty;
        TokenHash = string.Empty;
        Salt = string.Empty;
        InitializationVector = string.Empty;
    }

    /// <summary>
    /// Creates a new secure token
    /// </summary>
    public SecureToken(
        Guid tokenId,
        Guid userId,
        string tokenType,
        string encryptedToken,
        string tokenHash,
        string salt,
        string initializationVector,
        DateTime? expiresAt = null,
        string? metadata = null,
        string? createdFromIp = null,
        string? createdFromUserAgent = null)
    {
        TokenId = tokenId;
        UserId = userId;
        TokenType = tokenType ?? throw new ArgumentNullException(nameof(tokenType));
        EncryptedToken = encryptedToken ?? throw new ArgumentNullException(nameof(encryptedToken));
        TokenHash = tokenHash ?? throw new ArgumentNullException(nameof(tokenHash));
        Salt = salt ?? throw new ArgumentNullException(nameof(salt));
        InitializationVector = initializationVector ?? throw new ArgumentNullException(nameof(initializationVector));
        ExpiresAt = expiresAt;
        IsRevoked = false;
        UsageCount = 0;
        Metadata = metadata;
        CreatedFromIp = createdFromIp;
        CreatedFromUserAgent = createdFromUserAgent;
    }

    /// <summary>
    /// Records token usage
    /// </summary>
    public void RecordUsage()
    {
        LastUsedAt = DateTime.UtcNow;
        UsageCount++;
    }

    /// <summary>
    /// Revokes the token
    /// </summary>
    public void Revoke(string? reason = null)
    {
        if (IsRevoked)
            return;
            
        IsRevoked = true;
        RevokedAt = DateTime.UtcNow;
        RevocationReason = reason;
    }

    /// <summary>
    /// Checks if the token is valid (not expired and not revoked)
    /// </summary>
    public bool IsValid()
    {
        if (IsRevoked)
            return false;
            
        if (ExpiresAt.HasValue && ExpiresAt.Value <= DateTime.UtcNow)
            return false;
            
        return true;
    }
    
    /// <summary>
    /// Updates token metadata
    /// </summary>
    public void UpdateMetadata(string? metadata)
    {
        Metadata = metadata;
    }
    
    /// <summary>
    /// Soft delete the token
    /// </summary>
    public void Delete()
    {
        if (IsDeleted)
            return;
            
        IsDeleted = true;
        DeletedAt = DateTime.UtcNow;
    }
    
    /// <summary>
    /// Restore a soft-deleted token
    /// </summary>
    public void Restore()
    {
        if (!IsDeleted)
            return;
            
        IsDeleted = false;
        DeletedAt = null;
        DeletedBy = null;
    }
}