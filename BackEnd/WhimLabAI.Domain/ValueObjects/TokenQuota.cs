using WhimLabAI.Domain.Common;

namespace WhimLabAI.Domain.ValueObjects;

public class TokenQuota : ValueObject
{
    public int Total { get; private set; }
    public int Used { get; private set; }
    public int Remaining => Total - Used;
    public bool IsUnlimited => Total == -1;
    
    private TokenQuota() { }
    
    public TokenQuota(int total, int used = 0)
    {
        if (total < -1)
            throw new ArgumentException("Token total must be -1 (unlimited) or a positive value");
            
        if (used < 0)
            throw new ArgumentException("Used tokens cannot be negative");
            
        Total = total;
        Used = used;
    }
    
    public static TokenQuota Unlimited() => new TokenQuota(-1, 0);
    
    public static TokenQuota Create(int total) => new TokenQuota(total, 0);
    
    public static TokenQuota Create(int total, int remaining) => new TokenQuota(total, total - remaining);
    
    public bool CanUse(int tokens)
    {
        if (IsUnlimited) return true;
        return Remaining >= tokens;
    }
    
    public bool IsExhausted()
    {
        if (IsUnlimited) return false;
        return Remaining <= 0;
    }
    
    public TokenQuota Use(int tokens)
    {
        if (!CanUse(tokens))
            throw new InvalidOperationException("Insufficient tokens");
            
        return new TokenQuota(Total, Used + tokens);
    }
    
    public TokenQuota Reset()
    {
        return new TokenQuota(Total, 0);
    }
    
    protected override IEnumerable<object> GetEqualityComponents()
    {
        yield return Total;
        yield return Used;
    }
}