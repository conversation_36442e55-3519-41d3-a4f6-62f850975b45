using Microsoft.Extensions.Logging;
using WhimLabAI.Abstractions.Application;
using WhimLabAI.Domain.Repositories;
using WhimLabAI.Abstractions.Infrastructure;
using WhimLabAI.Domain.Entities.Subscription;
using WhimLabAI.Domain.Entities.Payment;
using WhimLabAI.Domain.ValueObjects;
using WhimLabAI.Shared.Dtos;
using WhimLabAI.Shared.Dtos.Subscription;
using WhimLabAI.Shared.Enums;
using WhimLabAI.Shared.Results;

namespace WhimLabAI.Application.Services.Subscription;

public class SubscriptionService : ISubscriptionService
{
    private readonly IUnitOfWork _unitOfWork;
    private readonly ICacheService _cacheService;
    private readonly ILogger<SubscriptionService> _logger;

    public SubscriptionService(
        IUnitOfWork unitOfWork,
        ICacheService cacheService,
        ILogger<SubscriptionService> logger)
    {
        _unitOfWork = unitOfWork;
        _cacheService = cacheService;
        _logger = logger;
    }

    public async Task<Result<List<SubscriptionPlanDto>>> GetSubscriptionPlansAsync(CancellationToken cancellationToken = default)
    {
        try
        {
            // Try to get from cache
            var cacheKey = "subscription-plans:active";
            var cachedPlans = await _cacheService.GetAsync<List<SubscriptionPlanDto>>(cacheKey, cancellationToken);
            if (cachedPlans != null)
            {
                return Result<List<SubscriptionPlanDto>>.Success(cachedPlans);
            }

            // Get from database
            var planRepository = _unitOfWork.Repository<SubscriptionPlan>();
            var plans = await planRepository.GetAsync(p => p.IsActive, cancellationToken);
            var planList = plans.ToList();

            var planDtos = planList.Select(p => new SubscriptionPlanDto
            {
                Id = p.Id,
                Name = p.Name,
                Code = p.Tier.ToString().ToUpper(),
                Tier = p.Tier,
                Description = p.Description,
                Price = p.Price.Amount,
                Period = SubscriptionPeriod.Monthly,
                TokenQuota = p.MonthlyTokens,
                MaxAgents = p.MaxAgents,
                MaxKnowledgeBases = p.AllowKnowledgeBase ? 10 : 0,  // Default values based on tier
                MaxDocuments = p.AllowKnowledgeBase ? 100 : 0,     // Default values based on tier
                IncludesSupport = p.Tier >= SubscriptionTier.Pro,
                IncludesTraining = p.Tier == SubscriptionTier.Ultra,
                Features = p.Features.ToList(),
                IsPopular = p.Tier == SubscriptionTier.Pro,
                IsActive = p.IsActive,
                SortOrder = (int)p.Tier
            }).OrderBy(p => p.SortOrder).ToList();

            // Cache for 1 hour
            await _cacheService.SetAsync(cacheKey, planDtos, TimeSpan.FromHours(1), cancellationToken);

            return Result<List<SubscriptionPlanDto>>.Success(planDtos);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting subscription plans");
            return Result<List<SubscriptionPlanDto>>.Failure("GET_PLANS_ERROR", "获取订阅套餐失败");
        }
    }

    public async Task<Result<SubscriptionDto>> GetCurrentSubscriptionAsync(Guid userId, CancellationToken cancellationToken = default)
    {
        try
        {
            var subscriptionRepository = _unitOfWork.Repository<Domain.Entities.Subscription.Subscription>();
            var subscription = await subscriptionRepository.FirstOrDefaultAsync(
                s => s.CustomerUserId == userId && (s.Status == SubscriptionStatus.Active || s.Status == SubscriptionStatus.Paused), cancellationToken);

            if (subscription == null || subscription.Plan == null)
            {
                return Result<SubscriptionDto>.Failure("INVALID_SUBSCRIPTION", "订阅数据无效");
            }

            // Get usage statistics
            var usageRepository = _unitOfWork.Repository<UsageRecord>();
            var usageRecords = await usageRepository.GetAsync(u => u.SubscriptionId == subscription.Id, cancellationToken);
            var totalUsed = usageRecords.Sum(u => u.TokensUsed);

            var dto = new SubscriptionDto
            {
                Id = subscription.Id,
                UserId = subscription.CustomerUserId,
                PlanId = subscription.PlanId,
                Plan = new SubscriptionPlanDto
                {
                    Id = subscription.Plan.Id,
                    Name = subscription.Plan.Name,
                    Code = subscription.Plan.Tier.ToString().ToUpper(),
                    Tier = subscription.Plan.Tier,
                    Description = subscription.Plan.Description,
                    Price = subscription.Plan.Price.Amount,
                    Period = SubscriptionPeriod.Monthly,
                    TokenQuota = subscription.Plan.MonthlyTokens,
                    MaxAgents = subscription.Plan.MaxAgents,
                    MaxKnowledgeBases = subscription.Plan.AllowKnowledgeBase ? 10 : 0,  // Default values based on tier
                    MaxDocuments = subscription.Plan.AllowKnowledgeBase ? 100 : 0,     // Default values based on tier
                    IncludesSupport = subscription.Plan.Tier >= SubscriptionTier.Pro,
                    IncludesTraining = subscription.Plan.Tier == SubscriptionTier.Ultra,
                    Features = subscription.Plan.Features.ToList(),
                    IsActive = subscription.Plan.IsActive,
                    IsPopular = subscription.Plan.Tier == SubscriptionTier.Pro,
                    SortOrder = (int)subscription.Plan.Tier
                },
                Status = subscription.Status,
                StartDate = subscription.StartDate,
                EndDate = subscription.EndDate,
                AutoRenew = subscription.AutoRenew,
                Price = subscription.Plan.Price.Amount,
                TokenQuotaUsed = subscription.Plan.MonthlyTokens - subscription.RemainingTokens,
                TokenQuotaTotal = subscription.Plan.MonthlyTokens,
                LastRenewalDate = subscription.LastRenewalDate,
                NextRenewalDate = subscription.EndDate,
                CancellationReason = subscription.CancellationReason,
                CreatedAt = subscription.CreatedAt
            };

            return Result<SubscriptionDto>.Success(dto);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting current subscription for user {UserId}", userId);
            return Result<SubscriptionDto>.Failure("GET_SUBSCRIPTION_ERROR", "获取订阅信息失败");
        }
    }

    public async Task<Result<Guid>> SubscribeAsync(SubscribeRequestDto request, Guid userId, CancellationToken cancellationToken = default)
    {
        try
        {
            // Check if user already has active subscription
            var subscriptionRepository = _unitOfWork.Repository<Domain.Entities.Subscription.Subscription>();
            var existingSubscription = await subscriptionRepository.FirstOrDefaultAsync(
                s => s.CustomerUserId == userId && s.Status == SubscriptionStatus.Active, cancellationToken);
            if (existingSubscription != null)
            {
                return Result<Guid>.Failure("ALREADY_SUBSCRIBED", "用户已有有效订阅");
            }

            // Get the plan
            var planRepository = _unitOfWork.Repository<SubscriptionPlan>();
            var plan = await planRepository.GetByIdAsync(request.PlanId, cancellationToken);
            if (plan == null || !plan.IsActive)
            {
                return Result<Guid>.Failure("PLAN_INACTIVE", "订阅套餐不可用");
            }

            // Begin transaction
            await _unitOfWork.BeginTransactionAsync(cancellationToken: cancellationToken);

            try
            {
                // Create subscription
                var subscription = new Domain.Entities.Subscription.Subscription(
                    userId,
                    plan.Id,
                    DateTime.UtcNow,
                    plan.Tier == SubscriptionTier.Free ? null : DateTime.UtcNow.AddDays(plan.DurationDays),
                    request.PaymentMethod.ToString(),
                    null, // orderId
                    request.AutoRenew);

                // Set plan info and activate
                subscription.SetPlanInfo(plan.MonthlyTokens, plan.Price);
                subscription.Activate();

                await subscriptionRepository.AddAsync(subscription, cancellationToken);

                // 处理非免费套餐的支付
                if (plan.Tier != SubscriptionTier.Free)
                {
                    // 创建订单
                    var orderNo = $"SUB{DateTime.UtcNow:yyyyMMddHHmmss}{userId.ToString().Substring(0, 8)}";
                    var order = new Domain.Entities.Payment.Order(
                        orderNo,
                        userId,
                        OrderType.Subscription,
                        plan.Price,
                        request.PaymentMethod,
                        plan.Id,
                        $"{plan.Name} 订阅套餐",
                        $"订阅 {plan.Name} 套餐");

                    await _unitOfWork.Repository<Domain.Entities.Payment.Order>()
                        .AddAsync(order, cancellationToken);

                    // 关联订单到订阅 - 需要等Order支付成功后在事件处理中关联
                    // subscription.SetOrderId(order.Id);

                    _logger.LogInformation("创建订阅订单 {OrderId} 用于套餐 {PlanName}", order.Id, plan.Name);
                }

                await _unitOfWork.CommitAsync(cancellationToken);

                _logger.LogInformation("User {UserId} subscribed to plan {PlanId}", userId, plan.Id);
                return Result<Guid>.Success(subscription.Id);
            }
            catch
            {
                await _unitOfWork.RollbackAsync(cancellationToken);
                throw;
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error subscribing user {UserId} to plan {PlanId}", userId, request.PlanId);
            return Result<Guid>.Failure("SUBSCRIBE_ERROR", "订阅失败");
        }
    }

    public async Task<Result> UpgradeSubscriptionAsync(UpgradeSubscriptionDto request, Guid userId, CancellationToken cancellationToken = default)
    {
        try
        {
            // Get current subscription
            var subscriptionRepository = _unitOfWork.Repository<Domain.Entities.Subscription.Subscription>();
            var subscription = await subscriptionRepository.FirstOrDefaultAsync(
                s => s.CustomerUserId == userId && s.Status == SubscriptionStatus.Active, cancellationToken);

            if (subscription == null)
            {
                return Result.Failure("INVALID_SUBSCRIPTION", "订阅数据无效");
            }

            // Check if it's an upgrade
            var planRepository = _unitOfWork.Repository<SubscriptionPlan>();
            var currentPlan = await planRepository.GetByIdAsync(subscription.PlanId, cancellationToken);
            var newPlan = await planRepository.GetByIdAsync(request.NewPlanId, cancellationToken);

            if (currentPlan == null || newPlan == null || newPlan.Tier <= currentPlan.Tier)
            {
                return Result.Failure("NOT_UPGRADE", "只能升级到更高级的套餐");
            }

            // Begin transaction
            await _unitOfWork.BeginTransactionAsync(cancellationToken: cancellationToken);

            try
            {
                if (request.ImmediateUpgrade)
                {
                    // Calculate prorated amount
                    var daysRemaining = subscription.EndDate.HasValue
                        ? (subscription.EndDate.Value - DateTime.UtcNow).Days
                        : 30;

                    var dailyPriceDiff = (newPlan.Price.Amount - currentPlan.Price.Amount) / 30;
                    var proratedAmount = dailyPriceDiff * daysRemaining;

                    // 处理按比例计算的差价支付
                    if (proratedAmount > 0)
                    {
                        var orderNo = $"UPG{DateTime.UtcNow:yyyyMMddHHmmss}{userId.ToString().Substring(0, 8)}";
                        var upgradeAmount = Money.Create(proratedAmount, currentPlan.Price.Currency);

                        var upgradeOrder = new Domain.Entities.Payment.Order(
                            orderNo,
                            userId,
                            OrderType.Subscription, // 使用Subscription类型，通过备注说明是升级
                            upgradeAmount,
                            Enum.Parse<PaymentMethod>(subscription.PaymentMethod ?? "Alipay"), // 使用当前订阅的支付方式
                            subscription.Id,
                            $"升级到 {newPlan.Name} 套餐",
                            $"订阅升级：{currentPlan.Name} → {newPlan.Name}");

                        await _unitOfWork.Repository<Domain.Entities.Payment.Order>()
                            .AddAsync(upgradeOrder, cancellationToken);

                        _logger.LogInformation("创建升级订单 {OrderId}，金额 {Amount}",
                            upgradeOrder.Id, proratedAmount);
                    }

                    // Upgrade subscription - update plan and reset tokens
                    subscription.PlanId = request.NewPlanId;
                    subscription.SetPlanInfo(newPlan.MonthlyTokens, newPlan.Price);
                    await _unitOfWork.SaveChangesAsync(cancellationToken);
                }
                else
                {
                    // Schedule upgrade for next billing cycle
                    subscription.NextPlanId = request.NewPlanId;
                    await _unitOfWork.SaveChangesAsync(cancellationToken);
                }

                await _unitOfWork.CommitAsync(cancellationToken);

                _logger.LogInformation("User {UserId} upgraded subscription to plan {PlanId}", userId, request.NewPlanId);
                return Result.Success();
            }
            catch
            {
                await _unitOfWork.RollbackAsync(cancellationToken);
                throw;
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error upgrading subscription for user {UserId}", userId);
            return Result.Failure("UPGRADE_ERROR", "升级订阅失败");
        }
    }

    public async Task<Result> CancelSubscriptionAsync(Guid userId, CancellationToken cancellationToken = default)
    {
        try
        {
            var subscriptionRepository = _unitOfWork.Repository<Domain.Entities.Subscription.Subscription>();
            var subscription = await subscriptionRepository.FirstOrDefaultAsync(
                s => s.CustomerUserId == userId && s.Status == SubscriptionStatus.Active, cancellationToken);

            if (subscription == null)
            {
                return Result.Failure("NO_SUBSCRIPTION", "用户没有有效订阅");
            }

            // Cancel subscription
            subscription.Cancel("用户主动取消");

            await _unitOfWork.SaveChangesAsync(cancellationToken);

            _logger.LogInformation("User {UserId} cancelled subscription {SubscriptionId}", userId, subscription.Id);
            return Result.Success();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error cancelling subscription for user {UserId}", userId);
            return Result.Failure("CANCEL_ERROR", "取消订阅失败");
        }
    }

    public async Task<Result<QuotaUsageDto>> GetQuotaUsageAsync(Guid userId, CancellationToken cancellationToken = default)
    {
        try
        {
            var subscriptionRepository = _unitOfWork.Repository<Domain.Entities.Subscription.Subscription>();
            var subscription = await subscriptionRepository.FirstOrDefaultAsync(
                s => s.CustomerUserId == userId && s.Status == SubscriptionStatus.Active, cancellationToken);

            if (subscription == null)
            {
                // Return free tier usage if no subscription
                return Result<QuotaUsageDto>.Success(new QuotaUsageDto
                {
                    UserId = userId,
                    TokensTotal = 5000,
                    TokensUsed = 0,
                    UsagePercentage = 0,
                    PeriodStart = DateTime.UtcNow.Date,
                    PeriodEnd = DateTime.UtcNow.AddMonths(1).Date
                });
            }

            if (subscription.Plan == null)
            {
                return Result<QuotaUsageDto>.Failure("INVALID_SUBSCRIPTION", "订阅数据无效");
            }

            // Get usage statistics
            var usageRepository = _unitOfWork.Repository<UsageRecord>();
            var startDate = DateTime.UtcNow.AddDays(-30);
            var endDate = DateTime.UtcNow;

            var usageRecords = await usageRepository.GetAsync(
                u => u.SubscriptionId == subscription.Id &&
                     u.UsageTime >= startDate &&
                     u.UsageTime <= endDate,
                cancellationToken);
            var usageRecordList = usageRecords.ToList();

            // Calculate daily usage
            var dailyUsage = usageRecordList
                .GroupBy(u => u.UsageTime.Date)
                .ToDictionary(g => g.Key, g => g.Sum(u => u.TokensUsed));

            // Calculate total used tokens
            var totalUsed = subscription.Plan.MonthlyTokens - subscription.RemainingTokens;
            
            // Get usage by model (simplified for now)
            var usageByModel = new Dictionary<string, long>
            {
                ["gpt-3.5-turbo"] = totalUsed // Default to single model for now
            };

            var quotaUsage = new QuotaUsageDto
            {
                UserId = userId,
                TokensTotal = subscription.Plan.MonthlyTokens,
                TokensUsed = totalUsed,
                UsagePercentage = subscription.Plan.MonthlyTokens > 0 ? (double)totalUsed / subscription.Plan.MonthlyTokens * 100 : 0,
                PeriodStart = subscription.LastResetDate ?? subscription.StartDate,
                PeriodEnd = subscription.LastResetDate?.AddMonths(1) ?? DateTime.UtcNow.AddMonths(1).Date,
                DailyUsage = dailyUsage.Select(kvp => new DailyUsageDto
                {
                    Date = kvp.Key,
                    TokensUsed = kvp.Value,
                    ConversationsCount = usageRecordList.Count(u => u.UsageTime.Date == kvp.Key)
                }).ToList(),
                UsageByModel = usageByModel
            };

            return Result<QuotaUsageDto>.Success(quotaUsage);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting quota usage for user {UserId}", userId);
            return Result<QuotaUsageDto>.Failure("GET_USAGE_ERROR", "获取用量信息失败");
        }
    }

    public async Task<Result> PauseSubscriptionAsync(Guid userId, DateTime resumeDate, CancellationToken cancellationToken = default)
    {
        try
        {
            // Validate resume date
            if (resumeDate <= DateTime.UtcNow)
            {
                return Result.Failure("INVALID_RESUME_DATE", "恢复日期必须是未来的日期");
            }

            // Maximum pause duration is 3 months
            if (resumeDate > DateTime.UtcNow.AddMonths(3))
            {
                return Result.Failure("PAUSE_TOO_LONG", "暂停时间不能超过3个月");
            }

            var subscriptionRepository = _unitOfWork.Repository<Domain.Entities.Subscription.Subscription>() as ISubscriptionRepository;
            if (subscriptionRepository == null)
            {
                return Result.Failure("REPOSITORY_ERROR", "无法访问订阅仓储");
            }

            var subscription = await subscriptionRepository.FirstOrDefaultAsync(
                s => s.CustomerUserId == userId && s.Status == SubscriptionStatus.Active, cancellationToken);

            if (subscription == null)
            {
                return Result.Failure("NO_ACTIVE_SUBSCRIPTION", "用户没有有效订阅");
            }

            // Don't allow pausing free tier subscriptions
            if (subscription.Plan != null && subscription.Plan.Tier == SubscriptionTier.Free)
            {
                return Result.Failure("CANNOT_PAUSE_FREE", "免费套餐不支持暂停");
            }

            var success = await subscriptionRepository.PauseSubscriptionAsync(subscription.Id, resumeDate, cancellationToken);
            if (!success)
            {
                return Result.Failure("PAUSE_FAILED", "暂停订阅失败");
            }

            // Clear cache
            var cacheKey = $"subscription:active:{userId}";
            await _cacheService.RemoveAsync(cacheKey, cancellationToken);

            _logger.LogInformation("User {UserId} paused subscription until {ResumeDate}", userId, resumeDate);
            return Result.Success();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error pausing subscription for user {UserId}", userId);
            return Result.Failure("PAUSE_ERROR", "暂停订阅失败");
        }
    }

    public async Task<Result> ResumeSubscriptionAsync(Guid userId, CancellationToken cancellationToken = default)
    {
        try
        {
            var subscriptionRepository = _unitOfWork.Repository<Domain.Entities.Subscription.Subscription>() as ISubscriptionRepository;
            if (subscriptionRepository == null)
            {
                return Result.Failure("REPOSITORY_ERROR", "无法访问订阅仓储");
            }

            var subscription = await subscriptionRepository.FirstOrDefaultAsync(
                s => s.CustomerUserId == userId && s.Status == SubscriptionStatus.Paused, cancellationToken);

            if (subscription == null)
            {
                return Result.Failure("NO_PAUSED_SUBSCRIPTION", "用户没有暂停的订阅");
            }

            var success = await subscriptionRepository.ResumeSubscriptionAsync(subscription.Id, cancellationToken);
            if (!success)
            {
                return Result.Failure("RESUME_FAILED", "恢复订阅失败");
            }

            // Clear cache
            var cacheKey = $"subscription:active:{userId}";
            await _cacheService.RemoveAsync(cacheKey, cancellationToken);

            _logger.LogInformation("User {UserId} resumed subscription", userId);
            return Result.Success();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error resuming subscription for user {UserId}", userId);
            return Result.Failure("RESUME_ERROR", "恢复订阅失败");
        }
    }
}
