using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Logging;
using WhimLabAI.Abstractions.Application;
using WhimLabAI.Abstractions.Infrastructure;
using WhimLabAI.Domain.Entities.Agent;
using WhimLabAI.Domain.Entities.Conversation;
using WhimLabAI.Domain.Entities.Payment;
using WhimLabAI.Domain.Entities.Subscription;
using WhimLabAI.Domain.Entities.User;
using WhimLabAI.Domain.Repositories;
using WhimLabAI.Shared.Dtos;
using WhimLabAI.Shared.Enums;
using System.Linq;

namespace WhimLabAI.Application.Services.Admin;

public class DashboardService : IDashboardService
{
    private readonly ICustomerUserRepository _customerUserRepository;
    private readonly IAgentRepository _agentRepository;
    private readonly IConversationRepository _conversationRepository;
    private readonly IOrderRepository _orderRepository;
    private readonly ISubscriptionRepository _subscriptionRepository;
    private readonly ITokenUsageRepository _tokenUsageRepository;
    private readonly ISubscriptionPlanRepository _subscriptionPlanRepository;
    private readonly IAgentCategoryRepository _agentCategoryRepository;
    private readonly IAgentRatingRepository _agentRatingRepository;
    private readonly IUsageRecordRepository _usageRecordRepository;
    private readonly IMemoryCache _cache;
    private readonly ILogger<DashboardService> _logger;

    private const string CACHE_KEY_DASHBOARD_STATS = "dashboard:stats";
    private const string CACHE_KEY_POPULAR_AGENTS = "dashboard:popular-agents";
    private const string CACHE_KEY_SUBSCRIPTION_DIST = "dashboard:subscription-dist";
    private const int CACHE_DURATION_MINUTES = 5;

    public DashboardService(
        ICustomerUserRepository customerUserRepository,
        IAgentRepository agentRepository,
        IConversationRepository conversationRepository,
        IOrderRepository orderRepository,
        ISubscriptionRepository subscriptionRepository,
        ITokenUsageRepository tokenUsageRepository,
        ISubscriptionPlanRepository subscriptionPlanRepository,
        IAgentCategoryRepository agentCategoryRepository,
        IAgentRatingRepository agentRatingRepository,
        IUsageRecordRepository usageRecordRepository,
        IMemoryCache cache,
        ILogger<DashboardService> logger)
    {
        _customerUserRepository = customerUserRepository;
        _agentRepository = agentRepository;
        _conversationRepository = conversationRepository;
        _orderRepository = orderRepository;
        _subscriptionRepository = subscriptionRepository;
        _tokenUsageRepository = tokenUsageRepository;
        _subscriptionPlanRepository = subscriptionPlanRepository;
        _agentCategoryRepository = agentCategoryRepository;
        _agentRatingRepository = agentRatingRepository;
        _usageRecordRepository = usageRecordRepository;
        _cache = cache;
        _logger = logger;
    }

    public async Task<DashboardStatsDto> GetDashboardStatsAsync()
    {
        return await _cache.GetOrCreateAsync(CACHE_KEY_DASHBOARD_STATS, async entry =>
        {
            entry.SlidingExpiration = TimeSpan.FromMinutes(CACHE_DURATION_MINUTES);
            
            var now = DateTime.UtcNow;
            var startOfToday = now.Date;
            var startOfMonth = new DateTime(now.Year, now.Month, 1);
            var startOfLastMonth = startOfMonth.AddMonths(-1);
            var endOfLastMonth = startOfMonth.AddSeconds(-1);

            // User statistics
            var totalUsers = await _customerUserRepository.CountAsync(_ => true);
            var activeUsersToday = await _customerUserRepository.CountAsync(u => 
                u.LastLoginAt >= startOfToday);
            var newUsersToday = await _customerUserRepository.CountAsync(u => 
                u.CreatedAt >= startOfToday);
            
            var usersLastMonth = await _customerUserRepository.CountAsync(u => 
                u.CreatedAt < startOfMonth);
            var userGrowthRate = usersLastMonth > 0 
                ? ((double)(totalUsers - usersLastMonth) / usersLastMonth) * 100 
                : 100;

            // Agent statistics
            var totalAgents = await _agentRepository.CountAsync(_ => true);
            var activeAgents = await _agentRepository.CountAsync(a => 
                a.Status == Shared.Enums.AgentStatus.Published && 
                a.UpdatedAt >= now.AddDays(-7));

            // Revenue statistics
            var monthlyRevenue = await _orderRepository
                .GetQueryable()
                .Where(o => o.Status == OrderStatus.Paid && 
                           o.CreatedAt >= startOfMonth && 
                           o.CreatedAt < now)
                .SumAsync(o => o.Amount.Amount);

            var lastMonthRevenue = await _orderRepository
                .GetQueryable()
                .Where(o => o.Status == OrderStatus.Paid && 
                           o.CreatedAt >= startOfLastMonth && 
                           o.CreatedAt <= endOfLastMonth)
                .SumAsync(o => o.Amount.Amount);

            var revenueGrowthRate = lastMonthRevenue > 0 
                ? ((double)(monthlyRevenue - lastMonthRevenue) / (double)lastMonthRevenue) * 100 
                : 100;

            // Token usage statistics - using the repository specific methods
            var todayTokens = await _tokenUsageRepository.GetTotalUsageAsync(Guid.Empty, startOfToday);
            var monthlyTokens = await _tokenUsageRepository.GetTotalUsageAsync(Guid.Empty, startOfMonth);

            // Conversation statistics
            var totalConversations = await _conversationRepository.CountAsync(_ => true);
            var activeConversations = await _conversationRepository
                .CountAsync(c => !c.IsArchived && c.LastMessageAt >= now.AddHours(-24));
            
            var conversationsLastMonth = await _conversationRepository
                .CountAsync(c => c.CreatedAt >= startOfLastMonth && c.CreatedAt <= endOfLastMonth);
            
            var conversationsThisMonth = await _conversationRepository
                .CountAsync(c => c.CreatedAt >= startOfMonth);
            
            var conversationGrowthRate = conversationsLastMonth > 0 
                ? ((double)(conversationsThisMonth - conversationsLastMonth) / conversationsLastMonth) * 100 
                : 100;

            return new DashboardStatsDto
            {
                TotalUsers = totalUsers,
                UserGrowthRate = Math.Round(userGrowthRate, 2),
                ActiveAgents = activeAgents,
                TotalAgents = totalAgents,
                MonthlyRevenue = monthlyRevenue,
                RevenueGrowthRate = Math.Round(revenueGrowthRate, 2),
                TodayTokens = todayTokens,
                MonthlyTokens = monthlyTokens,
                ActiveConversations = activeConversations,
                TotalConversations = totalConversations,
                ConversationGrowthRate = Math.Round(conversationGrowthRate, 2),
                NewCustomersToday = newUsersToday,
                ActiveCustomersToday = activeUsersToday
            };
        });
    }

    public async Task<IEnumerable<RecentOrderDto>> GetRecentOrdersAsync(int limit = 10)
    {
        var recentOrders = await _orderRepository
            .GetQueryable()
            .OrderByDescending(o => o.CreatedAt)
            .Take(limit)
            .ToListAsync();

        // Get customer details separately
        var customerIds = recentOrders.Select(o => o.CustomerUserId).Distinct().ToList();
        var customers = await _customerUserRepository.GetAsync(u => customerIds.Contains(u.Id));
        var customerDict = customers.ToDictionary(c => c.Id);

        return recentOrders.Select(o => new RecentOrderDto
        {
            OrderNo = o.OrderNo,
            UserName = customerDict.ContainsKey(o.CustomerUserId) 
                ? (customerDict[o.CustomerUserId].Username ?? customerDict[o.CustomerUserId].Email)
                : "Unknown",
            UserEmail = customerDict.ContainsKey(o.CustomerUserId) 
                ? customerDict[o.CustomerUserId].Email
                : "Unknown",
            Amount = o.Amount.Amount,
            Status = GetOrderStatusDisplay(o.Status),
            PaymentMethod = GetPaymentMethodDisplay(o.PaymentMethod),
            CreatedAt = o.CreatedAt
        });
    }

    public async Task<IEnumerable<DashboardPopularAgentDto>> GetPopularAgentsAsync(int limit = 10)
    {
        return await _cache.GetOrCreateAsync(CACHE_KEY_POPULAR_AGENTS, async entry =>
        {
            entry.SlidingExpiration = TimeSpan.FromMinutes(CACHE_DURATION_MINUTES);
            
            var now = DateTime.UtcNow;
            var sevenDaysAgo = now.AddDays(-7);

            // Get all published agents
            var agents = await _agentRepository.GetAsync(a => a.Status == Shared.Enums.AgentStatus.Published);
            
            // Get conversation counts for each agent
            var agentIds = agents.Select(a => a.Id).ToList();
            var conversations = await _conversationRepository.GetAsync(c => agentIds.Contains(c.AgentId));
            
            // Group conversations by agent
            var conversationsByAgent = conversations
                .GroupBy(c => c.AgentId)
                .ToDictionary(g => g.Key, g => new
                {
                    Total = g.Count(),
                    Recent = g.Count(c => c.CreatedAt >= sevenDaysAgo)
                });

            // Get ratings directly from agents
            var ratings = agents.ToDictionary(
                a => a.Id,
                a => new
                {
                    AverageScore = a.AverageRating,
                    Count = a.RatingCount
                });

            // Get categories
            var categoryIds = agents.Where(a => a.CategoryId.HasValue).Select(a => a.CategoryId!.Value).Distinct().ToList();
            var categories = await _agentCategoryRepository.GetAsync(c => categoryIds.Contains(c.Id));
            var categoryDict = categories.ToDictionary(c => c.Id);

            // Build popular agents list
            var popularAgents = agents
                .Select(a => new
                {
                    Agent = a,
                    RecentUsageCount = conversationsByAgent.ContainsKey(a.Id) ? conversationsByAgent[a.Id].Recent : 0,
                    TotalConversations = conversationsByAgent.ContainsKey(a.Id) ? conversationsByAgent[a.Id].Total : 0,
                    AverageRating = ratings.ContainsKey(a.Id) ? ratings[a.Id].AverageScore : 0,
                    RatingCount = ratings.ContainsKey(a.Id) ? ratings[a.Id].Count : 0
                })
                .OrderByDescending(x => x.RecentUsageCount)
                .Take(limit)
                .ToList();

            return popularAgents.Select(x => new DashboardPopularAgentDto
            {
                Id = x.Agent.Id,
                Name = x.Agent.Name,
                Category = x.Agent.CategoryId.HasValue && categoryDict.ContainsKey(x.Agent.CategoryId.Value)
                    ? categoryDict[x.Agent.CategoryId.Value].Name
                    : "未分类",
                UsageCount = x.RecentUsageCount,
                ConversationCount = x.TotalConversations,
                Rating = Math.Round((decimal)x.AverageRating, 1),
                RatingCount = x.RatingCount,
                GrowthRate = 0 // Simplified for now
            });
        });
    }

    public async Task<Dictionary<string, int>> GetSubscriptionDistributionAsync()
    {
        return await _cache.GetOrCreateAsync(CACHE_KEY_SUBSCRIPTION_DIST, async entry =>
        {
            entry.SlidingExpiration = TimeSpan.FromMinutes(CACHE_DURATION_MINUTES);
            
            var plans = await _subscriptionPlanRepository.GetAllAsync();
            var distribution = new Dictionary<string, int>();

            foreach (var plan in plans)
            {
                var count = await _subscriptionRepository
                    .CountAsync(s => s.PlanId == plan.Id && s.Status == SubscriptionStatus.Active);
                distribution[plan.Name] = count;
            }

            // Add free tier users (users without active subscription)
            var activeSubscriptions = await _subscriptionRepository.GetAsync(s => s.Status == SubscriptionStatus.Active);
            var subscribedUserIds = activeSubscriptions.Select(s => s.CustomerUserId).Distinct().ToList();
            var freeUsers = await _customerUserRepository.CountAsync(u => !subscribedUserIds.Contains(u.Id));
            
            distribution["免费版"] = freeUsers;

            return distribution;
        });
    }

    public async Task<RevenueChartDto> GetRevenueChartAsync(string period = "month")
    {
        var now = DateTime.UtcNow;
        var (labels, startDates, previousStartDates) = GetChartPeriodData(period, now);
        
        var values = new double[labels.Length];
        var previousValues = new double[labels.Length];
        
        for (int i = 0; i < labels.Length; i++)
        {
            var endDate = i < labels.Length - 1 ? startDates[i + 1] : now;
            var previousEndDate = i < labels.Length - 1 ? previousStartDates[i + 1] : startDates[0];
            
            values[i] = (double)await _orderRepository
                .GetQueryable()
                .Where(o => o.Status == OrderStatus.Paid && 
                           o.CreatedAt >= startDates[i] && 
                           o.CreatedAt < endDate)
                .SumAsync(o => o.Amount.Amount);

            previousValues[i] = (double)await _orderRepository
                .GetQueryable()
                .Where(o => o.Status == OrderStatus.Paid && 
                           o.CreatedAt >= previousStartDates[i] && 
                           o.CreatedAt < previousEndDate)
                .SumAsync(o => o.Amount.Amount);
        }

        var totalRevenue = (decimal)values.Sum();
        var previousTotalRevenue = (decimal)previousValues.Sum();
        var growthRate = previousTotalRevenue > 0 
            ? ((double)(totalRevenue - previousTotalRevenue) / (double)previousTotalRevenue) * 100 
            : 100;

        return new RevenueChartDto
        {
            Labels = labels,
            Values = values,
            PreviousPeriodValues = previousValues,
            TotalRevenue = totalRevenue,
            GrowthRate = Math.Round(growthRate, 2)
        };
    }

    public async Task<CustomerStatsDto> GetCustomerStatsAsync()
    {
        var now = DateTime.UtcNow;
        var thirtyDaysAgo = now.AddDays(-30);
        var startOfMonth = new DateTime(now.Year, now.Month, 1);
        var startOfLastMonth = startOfMonth.AddMonths(-1);

        var totalCustomers = await _customerUserRepository.CountAsync(_ => true);
        var activeCustomers = await _customerUserRepository
            .CountAsync(u => u.LastLoginAt >= thirtyDaysAgo);
        
        var newCustomersThisMonth = await _customerUserRepository
            .CountAsync(u => u.CreatedAt >= startOfMonth);
        
        var newCustomersLastMonth = await _customerUserRepository
            .CountAsync(u => u.CreatedAt >= startOfLastMonth && u.CreatedAt < startOfMonth);

        var monthlyGrowthRate = newCustomersLastMonth > 0 
            ? ((double)(newCustomersThisMonth - newCustomersLastMonth) / newCustomersLastMonth) * 100 
            : 100;

        // Get subscription distribution
        var subscriptionTiers = await GetSubscriptionDistributionAsync();

        // Get registration sources - simplified without navigation
        var registrationSources = new Dictionary<string, int>
        {
            ["Email"] = await _customerUserRepository.CountAsync(u => u.Email != null),
            ["WeChat"] = 0, // Will need to query OAuth bindings separately
            ["Google"] = 0,
            ["GitHub"] = 0
        };

        return new CustomerStatsDto
        {
            TotalCustomers = totalCustomers,
            ActiveCustomers = activeCustomers,
            NewCustomersThisMonth = newCustomersThisMonth,
            NewCustomersLastMonth = newCustomersLastMonth,
            MonthlyGrowthRate = Math.Round(monthlyGrowthRate, 2),
            CustomersBySubscriptionTier = subscriptionTiers,
            CustomersByRegistrationSource = registrationSources
        };
    }

    public async Task<DashboardAgentStatsDto> GetAgentStatsAsync()
    {
        var now = DateTime.UtcNow;
        var sevenDaysAgo = now.AddDays(-7);

        var totalAgents = await _agentRepository.CountAsync(_ => true);
        var publishedAgents = await _agentRepository
            .CountAsync(a => a.Status == Shared.Enums.AgentStatus.Published);
        var draftAgents = await _agentRepository
            .CountAsync(a => a.Status == Shared.Enums.AgentStatus.Draft);
        var activeAgents = await _agentRepository
            .CountAsync(a => a.UpdatedAt >= sevenDaysAgo);

        // Calculate average rating
        var allRatings = await _agentRatingRepository.GetAllAsync();
        var averageRating = allRatings.Any() ? allRatings.Average(r => r.Score) : 0;

        // Get agents by category
        var agents = await _agentRepository.GetAllAsync();
        var categories = await _agentCategoryRepository.GetAllAsync();
        var categoryDict = categories.ToDictionary(c => c.Id, c => c.Name);
        
        var agentsByCategory = agents
            .Where(a => a.CategoryId.HasValue && categoryDict.ContainsKey(a.CategoryId.Value))
            .GroupBy(a => categoryDict[a.CategoryId!.Value])
            .ToDictionary(g => g.Key, g => g.Count());

        // Get usage trends for last 7 days
        var usageTrends = new List<DashboardAgentUsageTrendDto>();
        for (int i = 6; i >= 0; i--)
        {
            var date = now.Date.AddDays(-i);
            var nextDate = date.AddDays(1);
            
            var usageCount = await _conversationRepository
                .CountAsync(c => c.CreatedAt >= date && c.CreatedAt < nextDate);
            
            usageTrends.Add(new DashboardAgentUsageTrendDto
            {
                Date = date,
                UsageCount = usageCount
            });
        }

        return new DashboardAgentStatsDto
        {
            TotalAgents = totalAgents,
            PublishedAgents = publishedAgents,
            DraftAgents = draftAgents,
            ActiveAgents = activeAgents,
            AverageRating = Math.Round(averageRating, 2),
            AgentsByCategory = agentsByCategory,
            UsageTrends = usageTrends
        };
    }

    public async Task<DashboardConversationStatsDto> GetConversationStatsAsync()
    {
        var now = DateTime.UtcNow;
        var startOfToday = now.Date;
        var startOfMonth = new DateTime(now.Year, now.Month, 1);

        var totalConversations = await _conversationRepository.CountAsync(_ => true);
        var activeConversations = await _conversationRepository
            .CountAsync(c => !c.IsArchived && c.LastMessageAt >= now.AddHours(-24));
        var conversationsToday = await _conversationRepository
            .CountAsync(c => c.CreatedAt >= startOfToday);
        var conversationsThisMonth = await _conversationRepository
            .CountAsync(c => c.CreatedAt >= startOfMonth);

        // Get all conversations to calculate averages
        var allConversations = await _conversationRepository.GetAllAsync();
        var averageMessages = allConversations.Any() ? allConversations.Average(c => c.MessageCount) : 0;
        
        // Calculate average duration
        var durations = allConversations
            .Select(c => c.IsArchived 
                ? (c.LastMessageAt - c.StartedAt).TotalMinutes 
                : (now - c.StartedAt).TotalMinutes)
            .ToList();
        
        var averageDuration = durations.Any() 
            ? TimeSpan.FromMinutes(durations.Average())
            : TimeSpan.Zero;

        // Get conversations by agent
        var agents = await _agentRepository.GetAllAsync();
        var agentDict = agents.ToDictionary(a => a.Id, a => a.Name);
        
        var conversationsByAgent = allConversations
            .Where(c => agentDict.ContainsKey(c.AgentId))
            .GroupBy(c => agentDict[c.AgentId])
            .OrderByDescending(g => g.Count())
            .Take(10)
            .ToDictionary(g => g.Key, g => g.Count());

        // Get daily trends for last 7 days
        var dailyTrends = new List<ConversationTrendDto>();
        for (int i = 6; i >= 0; i--)
        {
            var date = now.Date.AddDays(-i);
            var nextDate = date.AddDays(1);
            
            var dayConversations = allConversations
                .Where(c => c.CreatedAt >= date && c.CreatedAt < nextDate)
                .ToList();
            
            var dayDurations = dayConversations
                .Select(c => c.IsArchived 
                    ? (c.LastMessageAt - c.StartedAt).TotalMinutes 
                    : (now - c.StartedAt).TotalMinutes)
                .ToList();
            
            dailyTrends.Add(new ConversationTrendDto
            {
                Date = date,
                Count = dayConversations.Count,
                AverageDuration = dayDurations.Any() 
                    ? Math.Round(dayDurations.Average(), 2)
                    : 0
            });
        }

        return new DashboardConversationStatsDto
        {
            TotalConversations = totalConversations,
            ActiveConversations = activeConversations,
            ConversationsToday = conversationsToday,
            ConversationsThisMonth = conversationsThisMonth,
            AverageMessagesPerConversation = Math.Round(averageMessages, 2),
            AverageDuration = averageDuration,
            ConversationsByAgent = conversationsByAgent,
            DailyTrends = dailyTrends
        };
    }

    public async Task<TokenUsageStatsDto> GetTokenUsageStatsAsync()
    {
        var now = DateTime.UtcNow;
        var startOfToday = now.Date;
        var startOfMonth = new DateTime(now.Year, now.Month, 1);
        var startOfLastMonth = startOfMonth.AddMonths(-1);

        // Use repository specific methods for token usage
        var totalTokensUsed = await _tokenUsageRepository.GetTotalUsageAsync(Guid.Empty);
        var tokensUsedToday = await _tokenUsageRepository.GetTotalUsageAsync(Guid.Empty, startOfToday);
        var tokensUsedThisMonth = await _tokenUsageRepository.GetTotalUsageAsync(Guid.Empty, startOfMonth);
        
        // Calculate last month's usage manually
        var lastMonthUsage = await _tokenUsageRepository.GetUsageByDateRangeAsync(
            Guid.Empty, startOfLastMonth, startOfMonth.AddSeconds(-1));
        var tokensUsedLastMonth = lastMonthUsage.Sum(u => u.Tokens);

        var monthlyGrowthRate = tokensUsedLastMonth > 0 
            ? ((double)(tokensUsedThisMonth - tokensUsedLastMonth) / tokensUsedLastMonth) * 100 
            : 100;

        // Get tokens by subscription tier - simplified
        var tokensByTier = new Dictionary<string, long>
        {
            ["免费版"] = 0,
            ["基础版"] = 0,
            ["专业版"] = 0,
            ["超级版"] = 0
        };

        // Get top agents by token usage
        var topUsers = await _tokenUsageRepository.GetTopUsersAsync(startOfMonth, now, 10);
        var tokensByAgent = new Dictionary<string, long>();
        
        // Get daily trends for last 7 days
        var dailyTrends = new List<TokenUsageTrendDto>();
        for (int i = 6; i >= 0; i--)
        {
            var date = now.Date.AddDays(-i);
            var dayUsage = await _tokenUsageRepository.GetDailyUsageAsync(Guid.Empty, i);
            var dayTokens = dayUsage.ContainsKey(date) ? dayUsage[date] : 0;
            
            // Estimate cost (example: $0.002 per 1K tokens)
            var cost = (decimal)(dayTokens / 1000.0) * 0.002m;
            
            dailyTrends.Add(new TokenUsageTrendDto
            {
                Date = date,
                TokenCount = dayTokens,
                Cost = Math.Round(cost, 2)
            });
        }

        return new TokenUsageStatsDto
        {
            TotalTokensUsed = totalTokensUsed,
            TokensUsedToday = tokensUsedToday,
            TokensUsedThisMonth = tokensUsedThisMonth,
            TokensUsedLastMonth = tokensUsedLastMonth,
            MonthlyGrowthRate = Math.Round(monthlyGrowthRate, 2),
            TokensBySubscriptionTier = tokensByTier,
            TokensByAgent = tokensByAgent,
            DailyTrends = dailyTrends
        };
    }

    private (string[] labels, DateTime[] startDates, DateTime[] previousStartDates) GetChartPeriodData(
        string period, DateTime now)
    {
        switch (period.ToLower())
        {
            case "week":
                var weekLabels = new[] { "周一", "周二", "周三", "周四", "周五", "周六", "周日" };
                var weekStart = now.Date.AddDays(-(int)now.DayOfWeek + 1);
                var weekDates = Enumerable.Range(0, 7)
                    .Select(i => weekStart.AddDays(i))
                    .ToArray();
                var previousWeekDates = weekDates.Select(d => d.AddDays(-7)).ToArray();
                return (weekLabels, weekDates, previousWeekDates);

            case "year":
                var yearLabels = new[] { "1月", "2月", "3月", "4月", "5月", "6月", 
                                         "7月", "8月", "9月", "10月", "11月", "12月" };
                var yearDates = Enumerable.Range(1, 12)
                    .Select(m => new DateTime(now.Year, m, 1))
                    .ToArray();
                var previousYearDates = yearDates.Select(d => d.AddYears(-1)).ToArray();
                return (yearLabels, yearDates, previousYearDates);

            default: // month
                var daysInMonth = DateTime.DaysInMonth(now.Year, now.Month);
                var monthLabels = new[] { "1-5日", "6-10日", "11-15日", "16-20日", "21-25日", $"26-{daysInMonth}日" };
                var monthDates = new[]
                {
                    new DateTime(now.Year, now.Month, 1),
                    new DateTime(now.Year, now.Month, 6),
                    new DateTime(now.Year, now.Month, 11),
                    new DateTime(now.Year, now.Month, 16),
                    new DateTime(now.Year, now.Month, 21),
                    new DateTime(now.Year, now.Month, 26)
                };
                var previousMonthDates = monthDates.Select(d => d.AddMonths(-1)).ToArray();
                return (monthLabels, monthDates, previousMonthDates);
        }
    }

    private string GetOrderStatusDisplay(OrderStatus status)
    {
        return status switch
        {
            OrderStatus.Pending => "待支付",
            OrderStatus.Paid => "已支付",
            OrderStatus.Cancelled => "已取消",
            OrderStatus.Refunded => "已退款",
            OrderStatus.PartiallyRefunded => "部分退款",
            OrderStatus.Failed => "支付失败",
            OrderStatus.Expired => "已过期",
            _ => status.ToString()
        };
    }

    private string GetPaymentMethodDisplay(PaymentMethod? method)
    {
        if (method == null) return "未知";
        
        return method switch
        {
            PaymentMethod.Alipay => "支付宝",
            PaymentMethod.WeChatPay => "微信支付",
            PaymentMethod.CreditCard => "信用卡",
            PaymentMethod.Balance => "余额支付",
            PaymentMethod.Coupon => "优惠券",
            _ => method.ToString()
        };
    }
}