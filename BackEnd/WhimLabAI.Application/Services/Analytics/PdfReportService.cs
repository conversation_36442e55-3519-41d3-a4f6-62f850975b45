using System;
using System.Globalization;
using System.Text.Json;
using QuestPDF.Fluent;
using QuestPDF.Helpers;
using QuestPDF.Infrastructure;
using WhimLabAI.Shared.DTOs.Analytics;
using WhimLabAI.Shared.Enums;

namespace WhimLabAI.Application.Services.Analytics;

/// <summary>
/// PDF报告生成服务
/// </summary>
public class PdfReportService : IPdfReportService
{
    private readonly CultureInfo _zhCN = new CultureInfo("zh-CN");
    
    public byte[] GeneratePdfReport(object data, ReportType reportType)
    {
        QuestPDF.Settings.License = LicenseType.Community;
        
        var document = Document.Create(container =>
        {
            container.Page(page =>
            {
                page.Size(PageSizes.A4);
                page.Margin(2, Unit.Centimetre);
                page.PageColor(Colors.White);
                page.DefaultTextStyle(x => x.FontSize(10).FontFamily("Arial"));
                
                page.Header()
                    .Height(50)
                    .AlignCenter()
                    .Text(GetReportTitle(reportType))
                    .SemiBold().FontSize(20).FontColor(Colors.Blue.Medium);
                
                page.Content()
                    .PaddingVertical(1, Unit.Centimetre)
                    .Column(column =>
                    {
                        column.Spacing(20);
                        
                        // 报告元信息
                        column.Item().Row(row =>
                        {
                            row.RelativeItem().Text($"生成时间: {DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss", _zhCN)}");
                            row.RelativeItem().AlignRight().Text($"报告类型: {GetReportTypeDisplay(reportType)}");
                        });
                        
                        column.Item().LineHorizontal(1).LineColor(Colors.Grey.Lighten2);
                        
                        // 根据报告类型渲染内容
                        switch (reportType)
                        {
                            case ReportType.UserAnalysis:
                                RenderUserAnalyticsReport(column, data);
                                break;
                            case ReportType.FinancialAnalysis:
                                RenderRevenueAnalyticsReport(column, data);
                                break;
                            case ReportType.TokenUsageAnalysis:
                                RenderTokenUsageReport(column, data);
                                break;
                            case ReportType.ConversationAnalysis:
                                RenderConversationAnalyticsReport(column, data);
                                break;
                            case ReportType.ExecutiveSummary:
                                RenderComprehensiveReport(column, data);
                                break;
                            case ReportType.Custom:
                            default:
                                RenderDefaultReport(column, data);
                                break;
                        }
                    });
                
                page.Footer()
                    .Height(30)
                    .AlignCenter()
                    .Text(x =>
                    {
                        x.Span("第 ");
                        x.CurrentPageNumber();
                        x.Span(" 页，共 ");
                        x.TotalPages();
                        x.Span(" 页");
                    });
            });
        });
        
        return document.GeneratePdf();
    }
    
    private string GetReportTitle(ReportType reportType) => reportType switch
    {
        ReportType.UserAnalysis => "用户分析报告",
        ReportType.FinancialAnalysis => "财务分析报告",
        ReportType.TokenUsageAnalysis => "Token使用分析报告",
        ReportType.ConversationAnalysis => "对话分析报告",
        ReportType.ExecutiveSummary => "综合分析报告",
        ReportType.Custom => "自定义报告",
        _ => "数据分析报告"
    };
    
    private string GetReportTypeDisplay(ReportType reportType) => reportType switch
    {
        ReportType.UserAnalysis => "用户分析",
        ReportType.FinancialAnalysis => "财务分析",
        ReportType.TokenUsageAnalysis => "Token使用分析",
        ReportType.ConversationAnalysis => "对话分析",
        ReportType.ExecutiveSummary => "综合分析",
        ReportType.Custom => "自定义",
        _ => "其他"
    };
    
    private void RenderUserAnalyticsReport(ColumnDescriptor column, object data)
    {
        if (data is UserAnalyticsReportDto report)
        {
            // 用户概览部分
            column.Item().Text("用户概览").SemiBold().FontSize(14);
            column.Item().Table(table =>
            {
                table.ColumnsDefinition(columns =>
                {
                    columns.RelativeColumn();
                    columns.RelativeColumn();
                    columns.RelativeColumn();
                    columns.RelativeColumn();
                });
                
                // 表头
                table.Header(header =>
                {
                    header.Cell().Background(Colors.Grey.Lighten3).Padding(5).Text("指标").SemiBold();
                    header.Cell().Background(Colors.Grey.Lighten3).Padding(5).Text("数值").SemiBold();
                    header.Cell().Background(Colors.Grey.Lighten3).Padding(5).Text("比率").SemiBold();
                    header.Cell().Background(Colors.Grey.Lighten3).Padding(5).Text("变化").SemiBold();
                });
                
                // 数据行
                table.Cell().Padding(5).Text("总用户数");
                table.Cell().Padding(5).Text(report.Overview.TotalUsers.ToString("N0", _zhCN));
                table.Cell().Padding(5).Text("-");
                table.Cell().Padding(5).Text("-");
                
                table.Cell().Padding(5).Text("活跃用户");
                table.Cell().Padding(5).Text(report.Overview.ActiveUsers.ToString("N0", _zhCN));
                table.Cell().Padding(5).Text($"{report.Overview.ActiveRate:F2}%");
                table.Cell().Padding(5).Text(FormatComparison(report.Overview.MonthlyActiveComparison));
                
                table.Cell().Padding(5).Text("付费用户");
                table.Cell().Padding(5).Text(report.Overview.PaidUsers.ToString("N0", _zhCN));
                table.Cell().Padding(5).Text($"{report.Overview.PaidRate:F2}%");
                table.Cell().Padding(5).Text("-");
                
                table.Cell().Padding(5).Text("新增用户");
                table.Cell().Padding(5).Text(report.Overview.NewUsers.ToString("N0", _zhCN));
                table.Cell().Padding(5).Text("-");
                table.Cell().Padding(5).Text("-");
            });
            
            // 增长趋势
            if (report.GrowthTrends?.Any() == true)
            {
                column.Item().Text("用户增长趋势").SemiBold().FontSize(14);
                column.Item().Table(table =>
                {
                    table.ColumnsDefinition(columns =>
                    {
                        columns.RelativeColumn();
                        columns.RelativeColumn();
                        columns.RelativeColumn();
                        columns.RelativeColumn();
                    });
                    
                    table.Header(header =>
                    {
                        header.Cell().Background(Colors.Grey.Lighten3).Padding(5).Text("日期").SemiBold();
                        header.Cell().Background(Colors.Grey.Lighten3).Padding(5).Text("新增用户").SemiBold();
                        header.Cell().Background(Colors.Grey.Lighten3).Padding(5).Text("活跃用户").SemiBold();
                        header.Cell().Background(Colors.Grey.Lighten3).Padding(5).Text("累计用户").SemiBold();
                    });
                    
                    foreach (var trend in report.GrowthTrends.Take(10))
                    {
                        table.Cell().Padding(5).Text(trend.Date.ToString("yyyy-MM-dd"));
                        table.Cell().Padding(5).Text(trend.NewUsers.ToString("N0", _zhCN));
                        table.Cell().Padding(5).Text(trend.ActiveUsers.ToString("N0", _zhCN));
                        table.Cell().Padding(5).Text(trend.TotalUsers.ToString("N0", _zhCN));
                    }
                });
            }
        }
    }
    
    private void RenderRevenueAnalyticsReport(ColumnDescriptor column, object data)
    {
        if (data is RevenueAnalyticsReportDto report)
        {
            // 收入概览
            column.Item().Text("收入概览").SemiBold().FontSize(14);
            column.Item().Table(table =>
            {
                table.ColumnsDefinition(columns =>
                {
                    columns.RelativeColumn();
                    columns.RelativeColumn();
                });
                
                table.Cell().Padding(5).Text("总收入");
                table.Cell().Padding(5).Text($"¥{report.Overview.TotalRevenue:N2}");
                
                table.Cell().Padding(5).Text("订阅收入");
                table.Cell().Padding(5).Text($"¥{report.Overview.SubscriptionRevenue:N2}");
                
                table.Cell().Padding(5).Text("一次性收入");
                table.Cell().Padding(5).Text($"¥{report.Overview.OneTimeRevenue:N2}");
                
                table.Cell().Padding(5).Text("ARPU");
                table.Cell().Padding(5).Text($"¥{report.Overview.AverageRevenuePerUser:N2}");
                
                table.Cell().Padding(5).Text("付费用户ARPU");
                table.Cell().Padding(5).Text($"¥{report.Overview.AverageRevenuePerPaidUser:N2}");
            });
            
            // 订阅分布
            if (report.SubscriptionDistribution?.Any() == true)
            {
                column.Item().Text("订阅分布").SemiBold().FontSize(14);
                column.Item().Table(table =>
                {
                    table.ColumnsDefinition(columns =>
                    {
                        columns.RelativeColumn();
                        columns.RelativeColumn();
                        columns.RelativeColumn();
                        columns.RelativeColumn();
                    });
                    
                    table.Header(header =>
                    {
                        header.Cell().Background(Colors.Grey.Lighten3).Padding(5).Text("订阅级别").SemiBold();
                        header.Cell().Background(Colors.Grey.Lighten3).Padding(5).Text("用户数").SemiBold();
                        header.Cell().Background(Colors.Grey.Lighten3).Padding(5).Text("收入").SemiBold();
                        header.Cell().Background(Colors.Grey.Lighten3).Padding(5).Text("占比").SemiBold();
                    });
                    
                    foreach (var tier in report.SubscriptionDistribution)
                    {
                        table.Cell().Padding(5).Text(tier.Tier);
                        table.Cell().Padding(5).Text(tier.UserCount.ToString("N0", _zhCN));
                        table.Cell().Padding(5).Text($"¥{tier.Revenue:N2}");
                        table.Cell().Padding(5).Text($"{tier.Percentage:F1}%");
                    }
                });
            }
        }
    }
    
    private void RenderTokenUsageReport(ColumnDescriptor column, object data)
    {
        if (data is TokenUsageReportDto report)
        {
            // Token使用概览
            column.Item().Text("Token使用概览").SemiBold().FontSize(14);
            column.Item().Table(table =>
            {
                table.ColumnsDefinition(columns =>
                {
                    columns.RelativeColumn();
                    columns.RelativeColumn();
                });
                
                table.Cell().Padding(5).Text("总Token使用量");
                table.Cell().Padding(5).Text(report.Overview.TotalTokensUsed.ToString("N0", _zhCN));
                
                table.Cell().Padding(5).Text("活跃用户数");
                table.Cell().Padding(5).Text(report.Overview.ActiveUsers.ToString("N0", _zhCN));
                
                table.Cell().Padding(5).Text("平均每用户使用量");
                table.Cell().Padding(5).Text(report.Overview.AverageTokensPerUser.ToString("N0", _zhCN));
                
                table.Cell().Padding(5).Text("估算成本");
                table.Cell().Padding(5).Text($"¥{report.Overview.EstimatedCost:N2}");
            });
            
            // 模型分布
            if (report.ModelDistribution?.Any() == true)
            {
                column.Item().Text("模型使用分布").SemiBold().FontSize(14);
                column.Item().Table(table =>
                {
                    table.ColumnsDefinition(columns =>
                    {
                        columns.RelativeColumn();
                        columns.RelativeColumn();
                        columns.RelativeColumn();
                        columns.RelativeColumn();
                    });
                    
                    table.Header(header =>
                    {
                        header.Cell().Background(Colors.Grey.Lighten3).Padding(5).Text("模型").SemiBold();
                        header.Cell().Background(Colors.Grey.Lighten3).Padding(5).Text("使用量").SemiBold();
                        header.Cell().Background(Colors.Grey.Lighten3).Padding(5).Text("占比").SemiBold();
                        header.Cell().Background(Colors.Grey.Lighten3).Padding(5).Text("成本").SemiBold();
                    });
                    
                    foreach (var model in report.ModelDistribution)
                    {
                        table.Cell().Padding(5).Text(model.ModelName);
                        table.Cell().Padding(5).Text(model.TokensUsed.ToString("N0", _zhCN));
                        table.Cell().Padding(5).Text($"{model.Percentage:F1}%");
                        table.Cell().Padding(5).Text($"¥{model.EstimatedCost:N2}");
                    }
                });
            }
        }
    }
    
    private void RenderConversationAnalyticsReport(ColumnDescriptor column, object data)
    {
        if (data is ConversationAnalyticsReportDto report)
        {
            // 对话概览
            column.Item().Text("对话概览").SemiBold().FontSize(14);
            column.Item().Table(table =>
            {
                table.ColumnsDefinition(columns =>
                {
                    columns.RelativeColumn();
                    columns.RelativeColumn();
                });
                
                table.Cell().Padding(5).Text("总对话数");
                table.Cell().Padding(5).Text(report.Overview.TotalConversations.ToString("N0", _zhCN));
                
                table.Cell().Padding(5).Text("总消息数");
                table.Cell().Padding(5).Text(report.Overview.TotalMessages.ToString("N0", _zhCN));
                
                table.Cell().Padding(5).Text("平均对话长度");
                table.Cell().Padding(5).Text($"{report.Overview.AverageConversationLength:F1} 消息");
                
                table.Cell().Padding(5).Text("平均响应时间");
                table.Cell().Padding(5).Text($"{report.Overview.AverageResponseTime:F2} 秒");
            });
        }
    }
    
    private void RenderCostAnalysisReport(ColumnDescriptor column, object data)
    {
        if (data is CostAnalysisReportDto report)
        {
            // 成本概览
            column.Item().Text("成本概览").SemiBold().FontSize(14);
            column.Item().Table(table =>
            {
                table.ColumnsDefinition(columns =>
                {
                    columns.RelativeColumn();
                    columns.RelativeColumn();
                });
                
                table.Cell().Padding(5).Text("总成本");
                table.Cell().Padding(5).Text($"¥{report.Overview.TotalCost:N2}");
                
                table.Cell().Padding(5).Text("AI服务成本");
                table.Cell().Padding(5).Text($"¥{report.Overview.AICost:N2}");
                
                table.Cell().Padding(5).Text("基础设施成本");
                table.Cell().Padding(5).Text($"¥{report.Overview.InfrastructureCost:N2}");
                
                table.Cell().Padding(5).Text("每用户成本");
                table.Cell().Padding(5).Text($"¥{report.Overview.CostPerUser:N2}");
            });
        }
    }
    
    private void RenderChurnAnalysisReport(ColumnDescriptor column, object data)
    {
        if (data is ChurnAnalysisReportDto report)
        {
            // 流失概览
            column.Item().Text("流失分析概览").SemiBold().FontSize(14);
            column.Item().Table(table =>
            {
                table.ColumnsDefinition(columns =>
                {
                    columns.RelativeColumn();
                    columns.RelativeColumn();
                });
                
                table.Cell().Padding(5).Text("整体流失率");
                table.Cell().Padding(5).Text($"{report.Overview.OverallChurnRate:F2}%");
                
                table.Cell().Padding(5).Text("本月流失用户");
                table.Cell().Padding(5).Text(report.Overview.ChurnedUsersCount.ToString("N0", _zhCN));
                
                table.Cell().Padding(5).Text("高风险用户");
                table.Cell().Padding(5).Text(report.Overview.AtRiskUsersCount.ToString("N0", _zhCN));
                
                table.Cell().Padding(5).Text("预计收入损失");
                table.Cell().Padding(5).Text($"¥{report.Overview.EstimatedRevenueLoss:N2}");
            });
        }
    }
    
    private void RenderMarketingPerformanceReport(ColumnDescriptor column, object data)
    {
        if (data is MarketingPerformanceReportDto report)
        {
            // 营销概览
            column.Item().Text("营销绩效概览").SemiBold().FontSize(14);
            column.Item().Table(table =>
            {
                table.ColumnsDefinition(columns =>
                {
                    columns.RelativeColumn();
                    columns.RelativeColumn();
                });
                
                table.Cell().Padding(5).Text("客户获取成本(CAC)");
                table.Cell().Padding(5).Text($"¥{report.Overview.CustomerAcquisitionCost:N2}");
                
                table.Cell().Padding(5).Text("客户生命周期价值(LTV)");
                table.Cell().Padding(5).Text($"¥{report.Overview.CustomerLifetimeValue:N2}");
                
                table.Cell().Padding(5).Text("LTV:CAC比率");
                table.Cell().Padding(5).Text($"{report.Overview.LTVCACRatio:F2}");
                
                table.Cell().Padding(5).Text("转化率");
                table.Cell().Padding(5).Text($"{report.Overview.ConversionRate:F2}%");
            });
        }
    }
    
    private void RenderComprehensiveReport(ColumnDescriptor column, object data)
    {
        if (data is ComprehensiveAnalyticsDto report)
        {
            // 综合概览
            column.Item().Text("企业运营综合概览").SemiBold().FontSize(16);
            
            // 用户指标
            column.Item().Text("用户指标").SemiBold().FontSize(14);
            column.Item().Table(table =>
            {
                table.ColumnsDefinition(columns =>
                {
                    columns.RelativeColumn();
                    columns.RelativeColumn();
                });
                
                table.Cell().Padding(5).Text("总用户数");
                table.Cell().Padding(5).Text(report.UserMetrics.TotalUsers.ToString("N0", _zhCN));
                
                table.Cell().Padding(5).Text("月活跃用户");
                table.Cell().Padding(5).Text(report.UserMetrics.MonthlyActiveUsers.ToString("N0", _zhCN));
                
                table.Cell().Padding(5).Text("付费转化率");
                table.Cell().Padding(5).Text($"{report.UserMetrics.PaidConversionRate:F2}%");
            });
            
            // 财务指标
            column.Item().Text("财务指标").SemiBold().FontSize(14);
            column.Item().Table(table =>
            {
                table.ColumnsDefinition(columns =>
                {
                    columns.RelativeColumn();
                    columns.RelativeColumn();
                });
                
                table.Cell().Padding(5).Text("月度经常性收入(MRR)");
                table.Cell().Padding(5).Text($"¥{report.FinancialMetrics.MonthlyRecurringRevenue:N2}");
                
                table.Cell().Padding(5).Text("年度经常性收入(ARR)");
                table.Cell().Padding(5).Text($"¥{report.FinancialMetrics.AnnualRecurringRevenue:N2}");
                
                table.Cell().Padding(5).Text("毛利率");
                table.Cell().Padding(5).Text($"{report.FinancialMetrics.GrossMargin:F2}%");
            });
        }
    }
    
    private void RenderDefaultReport(ColumnDescriptor column, object data)
    {
        column.Item().Text("报告内容").SemiBold().FontSize(14);
        column.Item().Text(JsonSerializer.Serialize(data, new JsonSerializerOptions 
        { 
            WriteIndented = true,
            PropertyNamingPolicy = JsonNamingPolicy.CamelCase
        }))
        .FontSize(8)
        .FontFamily("Courier New");
    }
    
    private string FormatComparison(ComparisonData? comparison)
    {
        if (comparison == null) return "-";
        
        var trend = comparison.CurrentValue >= comparison.PreviousValue ? "↑" : "↓";
        var change = Math.Abs(comparison.ChangePercentage);
        return $"{trend} {change:F1}%";
    }
}

/// <summary>
/// PDF报告生成服务接口
/// </summary>
public interface IPdfReportService
{
    byte[] GeneratePdfReport(object data, ReportType reportType);
}