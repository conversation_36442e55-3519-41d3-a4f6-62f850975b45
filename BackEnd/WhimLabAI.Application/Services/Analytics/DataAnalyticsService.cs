using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using WhimLabAI.Abstractions.Application;
using WhimLabAI.Abstractions.Infrastructure;
using WhimLabAI.Domain.Entities.Conversation;
using WhimLabAI.Domain.Entities.Organization;
using WhimLabAI.Domain.Entities.User;
using WhimLabAI.Domain.Entities.Subscription;
using WhimLabAI.Domain.Repositories;
using WhimLabAI.Shared.DTOs.Analytics;
using WhimLabAI.Shared.Enums;
using WhimLabAI.Shared.Options;

namespace WhimLabAI.Application.Services.Analytics;

/// <summary>
/// 数据分析服务实现
/// </summary>
public class DataAnalyticsService : IDataAnalyticsService
{
    private readonly IAnalyticsRepository _analyticsRepository;
    private readonly ILogger<DataAnalyticsService> _logger;
    private readonly IMetricsService _metricsService;
    private readonly IPredictiveAnalyticsService _predictiveAnalyticsService;
    private readonly SubscriptionPricingOptions _pricingOptions;
    private readonly AIModelPricingOptions _modelPricingOptions;
    private readonly MarketingCostOptions _marketingCostOptions;
    private readonly ICacheService _cacheService;
    private readonly IPdfReportService _pdfReportService;

    public DataAnalyticsService(
        IAnalyticsRepository analyticsRepository,
        ILogger<DataAnalyticsService> logger,
        IMetricsService metricsService,
        IPredictiveAnalyticsService predictiveAnalyticsService,
        IOptions<SubscriptionPricingOptions> pricingOptions,
        IOptions<AIModelPricingOptions> modelPricingOptions,
        IOptions<MarketingCostOptions> marketingCostOptions,
        ICacheService cacheService,
        IPdfReportService pdfReportService)
    {
        _analyticsRepository = analyticsRepository;
        _logger = logger;
        _metricsService = metricsService;
        _predictiveAnalyticsService = predictiveAnalyticsService;
        _pricingOptions = pricingOptions.Value;
        _modelPricingOptions = modelPricingOptions.Value;
        _marketingCostOptions = marketingCostOptions.Value;
        _cacheService = cacheService;
        _pdfReportService = pdfReportService;
    }

    #region 用户分析

    public async Task<UserOverviewDto> GetUserOverviewAsync(DateTimeRange? dateRange = null, CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("Getting user overview statistics");
        
        var now = DateTime.UtcNow;
        var startDate = dateRange?.StartDate ?? now.AddDays(-30);
        var endDate = dateRange?.EndDate ?? now;
        
        var users = await _analyticsRepository.GetCustomerUsersQuery().ToListAsync(cancellationToken);
        
        // 计算活跃用户（30天内有登录）
        var activeUsers = users.Count(u => u.LastLoginAt.HasValue && u.LastLoginAt.Value >= now.AddDays(-30));
        
        // 计算新用户（指定时间范围内注册）
        var newUsers = users.Count(u => u.CreatedAt >= startDate && u.CreatedAt <= endDate);
        
        // 计算付费用户
        var paidUserIds = await _analyticsRepository.GetSubscriptionsQuery()
            .Where(s => s.Status == SubscriptionStatus.Active && s.Plan.Tier != SubscriptionTier.Free)
            .Select(s => s.CustomerUserId)
            .Distinct()
            .CountAsync(cancellationToken);
        
        // 计算日活比较
        var yesterday = now.AddDays(-1);
        var todayActive = users.Count(u => u.LastLoginAt.HasValue && u.LastLoginAt.Value.Date == now.Date);
        var yesterdayActive = users.Count(u => u.LastLoginAt.HasValue && u.LastLoginAt.Value.Date == yesterday.Date);
        
        // 计算月活比较
        var lastMonth = now.AddMonths(-1);
        var thisMonthActive = users.Count(u => u.LastLoginAt.HasValue && 
            u.LastLoginAt.Value.Year == now.Year && u.LastLoginAt.Value.Month == now.Month);
        var lastMonthActive = users.Count(u => u.LastLoginAt.HasValue && 
            u.LastLoginAt.Value.Year == lastMonth.Year && u.LastLoginAt.Value.Month == lastMonth.Month);
        
        return new UserOverviewDto
        {
            TotalUsers = users.Count,
            ActiveUsers = activeUsers,
            NewUsers = newUsers,
            PaidUsers = paidUserIds,
            ActiveRate = users.Count > 0 ? (double)activeUsers / users.Count * 100 : 0,
            PaidRate = users.Count > 0 ? (double)paidUserIds / users.Count * 100 : 0,
            DailyActiveComparison = CreateComparison(todayActive, yesterdayActive),
            MonthlyActiveComparison = CreateComparison(thisMonthActive, lastMonthActive),
            UpdatedAt = DateTime.UtcNow
        };
    }

    public async Task<UserGrowthTrendDto> GetUserGrowthTrendAsync(
        DateTimeRange dateRange, 
        TrendGranularity granularity = TrendGranularity.Daily,
        CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("Getting user growth trend from {StartDate} to {EndDate}", 
            dateRange.StartDate, dateRange.EndDate);

        var users = await _analyticsRepository.GetCustomerUsersQuery()
            .Where(u => u.CreatedAt >= dateRange.StartDate && u.CreatedAt <= dateRange.EndDate)
            .ToListAsync(cancellationToken);

        // 分组数据
        var groupedData = GroupByGranularity(users.Select(u => u.CreatedAt), granularity);
        
        // 计算累计用户数
        var totalUsersBeforeRange = await _analyticsRepository.GetCustomerUsersQuery()
            .CountAsync(u => u.CreatedAt < dateRange.StartDate, cancellationToken);
        
        var cumulativeTotal = totalUsersBeforeRange;
        var totalUsersTrend = new List<TrendDataPoint>();
        var newUsersTrend = new List<TrendDataPoint>();
        
        foreach (var group in groupedData)
        {
            var count = group.Count();
            cumulativeTotal += count;
            
            newUsersTrend.Add(new TrendDataPoint
            {
                Timestamp = group.Key,
                Value = count,
                Label = FormatDateLabel(group.Key, granularity)
            });
            
            totalUsersTrend.Add(new TrendDataPoint
            {
                Timestamp = group.Key,
                Value = cumulativeTotal,
                Label = FormatDateLabel(group.Key, granularity)
            });
        }

        // 计算活跃用户趋势
        var activeUsersTrend = await CalculateActiveUsersTrend(dateRange, granularity, cancellationToken);
        
        // 计算平均增长率
        var growthRates = CalculateGrowthRates(newUsersTrend);
        var averageGrowthRate = growthRates.Any() ? growthRates.Average() : 0;
        
        // 找出峰值增长
        var peakGrowth = newUsersTrend.OrderByDescending(t => t.Value).FirstOrDefault();
        
        return new UserGrowthTrendDto
        {
            DateRange = dateRange,
            Granularity = granularity,
            NewUsersTrend = newUsersTrend,
            TotalUsersTrend = totalUsersTrend,
            ActiveUsersTrend = activeUsersTrend,
            AverageGrowthRate = averageGrowthRate,
            PeakGrowth = peakGrowth,
            GrowthByChannel = await GetGrowthByChannelAsync(dateRange, cancellationToken)
        };
    }

    public async Task<UserRetentionDto> GetUserRetentionAsync(
        DateTime cohortDate,
        int daysToTrack = 30,
        CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("Getting user retention for cohort date {CohortDate}", cohortDate);

        // 获取指定日期注册的用户
        var cohortUsers = await _analyticsRepository.GetCustomerUsersQuery()
            .Where(u => u.CreatedAt.Date == cohortDate.Date)
            .ToListAsync(cancellationToken);

        var cohortSize = cohortUsers.Count;
        if (cohortSize == 0)
        {
            return new UserRetentionDto
            {
                CohortDate = cohortDate,
                CohortSize = 0,
                RetentionCurve = new List<RetentionDataPoint>(),
                Day1Retention = 0,
                Day7Retention = 0,
                Day30Retention = 0,
                RetentionBySegment = new Dictionary<string, List<RetentionDataPoint>>()
            };
        }

        var retentionCurve = new List<RetentionDataPoint>();
        var userIds = cohortUsers.Select(u => u.Id).ToList();

        // 使用Repository方法获取留存数据
        var retentionData = await _analyticsRepository.GetUserRetentionAsync(cohortDate, userIds, daysToTrack, cancellationToken);
        
        for (int day = 0; day <= daysToTrack; day++)
        {
            var activeUsers = retentionData.ContainsKey(day) ? retentionData[day] : 0;

            retentionCurve.Add(new RetentionDataPoint
            {
                Day = day,
                RetainedUsers = activeUsers,
                RetentionRate = (double)activeUsers / cohortSize * 100
            });
        }

        return new UserRetentionDto
        {
            CohortDate = cohortDate,
            CohortSize = cohortSize,
            RetentionCurve = retentionCurve,
            Day1Retention = retentionCurve.FirstOrDefault(r => r.Day == 1)?.RetentionRate ?? 0,
            Day7Retention = retentionCurve.FirstOrDefault(r => r.Day == 7)?.RetentionRate ?? 0,
            Day30Retention = retentionCurve.FirstOrDefault(r => r.Day == 30)?.RetentionRate ?? 0,
            RetentionBySegment = await GetRetentionBySegmentAsync(cohortDate, cohortUsers, daysToTrack, cancellationToken)
        };
    }

    public async Task<UserActivityDto> GetUserActivityAnalysisAsync(
        DateTimeRange dateRange,
        CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("Getting user activity analysis");

        var now = DateTime.UtcNow;
        
        // 获取活跃用户数据
        var conversations = await _analyticsRepository.GetConversationsQuery()
            .Where(c => c.CreatedAt >= dateRange.StartDate && c.CreatedAt <= dateRange.EndDate)
            .Select(c => new { c.CustomerUserId, c.CreatedAt })
            .ToListAsync(cancellationToken);

        // 计算DAU、WAU、MAU
        var dau = conversations
            .Where(c => c.CreatedAt.Date == now.Date)
            .Select(c => c.CustomerUserId)
            .Distinct()
            .Count();

        var wau = conversations
            .Where(c => c.CreatedAt >= now.AddDays(-7))
            .Select(c => c.CustomerUserId)
            .Distinct()
            .Count();

        var mau = conversations
            .Where(c => c.CreatedAt >= now.AddDays(-30))
            .Select(c => c.CustomerUserId)
            .Distinct()
            .Count();

        // 计算活跃度分布
        var userActivityCounts = conversations
            .GroupBy(c => c.CustomerUserId)
            .Select(g => new { UserId = g.Key, ActiveDays = g.Select(c => c.CreatedAt.Date).Distinct().Count() })
            .ToList();

        var activityDistribution = userActivityCounts
            .GroupBy(u => u.ActiveDays)
            .ToDictionary(g => g.Key, g => g.Count());

        // 计算小时活跃度模式
        var hourlyPattern = new HourlyActivityPattern
        {
            AverageUsersByHour = conversations
                .GroupBy(c => c.CreatedAt.Hour)
                .ToDictionary(g => g.Key, g => (double)g.Select(c => c.CustomerUserId).Distinct().Count())
        };
        
        if (hourlyPattern.AverageUsersByHour.Any())
        {
            hourlyPattern.PeakHour = hourlyPattern.AverageUsersByHour.OrderByDescending(h => h.Value).First().Key;
            hourlyPattern.LowestHour = hourlyPattern.AverageUsersByHour.OrderBy(h => h.Value).First().Key;
        }

        // 计算周活跃度模式
        var weeklyPattern = new WeeklyActivityPattern
        {
            AverageUsersByDayOfWeek = conversations
                .GroupBy(c => c.CreatedAt.DayOfWeek.ToString())
                .ToDictionary(g => g.Key, g => (double)g.Select(c => c.CustomerUserId).Distinct().Count())
        };
        
        if (weeklyPattern.AverageUsersByDayOfWeek.Any())
        {
            weeklyPattern.PeakDay = weeklyPattern.AverageUsersByDayOfWeek.OrderByDescending(d => d.Value).First().Key;
            weeklyPattern.LowestDay = weeklyPattern.AverageUsersByDayOfWeek.OrderBy(d => d.Value).First().Key;
        }

        // Get active users for segment activities
        var activeUserIds = conversations.Select(c => c.CustomerUserId).Distinct().ToList();
        var users = await _analyticsRepository.GetCustomerUsersQuery()
            .Where(u => activeUserIds.Contains(u.Id))
            .ToListAsync(cancellationToken);

        return new UserActivityDto
        {
            DateRange = dateRange,
            DailyActiveUsers = dau,
            WeeklyActiveUsers = wau,
            MonthlyActiveUsers = mau,
            DAUMAURatio = mau > 0 ? (double)dau / mau * 100 : 0,
            ActivityTrend = await GetActivityTrendAsync(dateRange, cancellationToken),
            ActivityDistribution = activityDistribution,
            SegmentActivities = await GetSegmentActivitiesAsync(users, dateRange, cancellationToken),
            HourlyPattern = hourlyPattern,
            WeeklyPattern = weeklyPattern
        };
    }

    public async Task<UserLifetimeValueDto> GetUserLifetimeValueAsync(
        DateTime? startDate = null,
        CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("Getting user lifetime value analysis");

        var queryStartDate = startDate ?? DateTime.UtcNow.AddYears(-1);
        
        // 获取用户订单数据
        var userOrders = await _analyticsRepository.GetOrdersQuery()
            .Where(o => o.Status == OrderStatus.Paid && o.CreatedAt >= queryStartDate)
            .GroupBy(o => o.CustomerUserId)
            .Select(g => new
            {
                UserId = g.Key,
                TotalValue = g.Sum(o => o.Amount.Amount),
                OrderCount = g.Count(),
                FirstOrderDate = g.Min(o => o.CreatedAt),
                LastOrderDate = g.Max(o => o.CreatedAt)
            })
            .ToListAsync(cancellationToken);

        if (!userOrders.Any())
        {
            return new UserLifetimeValueDto
            {
                AverageLTV = 0,
                MedianLTV = 0,
                LTVBySegment = new List<LTVSegment>(),
                LTVTrend = new List<TrendDataPoint>(),
                LTVByUserAge = new Dictionary<int, double>(),
                TopValueUsers = new TopValueUsers { Count = 0, TotalValue = 0, ValueContribution = 0 },
                Prediction = new LTVPrediction { Predicted3MonthLTV = 0, Predicted6MonthLTV = 0, Predicted12MonthLTV = 0 }
            };
        }

        var ltvValues = userOrders.Select(u => u.TotalValue).OrderBy(v => v).ToList();
        var averageLTV = ltvValues.Average();
        var medianLTV = ltvValues[ltvValues.Count / 2];

        // 计算高价值用户
        var topThreshold = ltvValues[(int)(ltvValues.Count * 0.8)]; // 前20%
        var topUsers = userOrders.Where(u => u.TotalValue >= topThreshold).ToList();
        
        var topValueUsers = new TopValueUsers
        {
            Count = topUsers.Count,
            TotalValue = (double)topUsers.Sum(u => u.TotalValue),
            ValueContribution = (double)(topUsers.Sum(u => u.TotalValue) / ltvValues.Sum() * 100),
            AverageOrderValue = (double)topUsers.Average(u => u.TotalValue / u.OrderCount),
            AverageOrderFrequency = topUsers.Average(u => u.OrderCount)
        };

        // 获取所需的原始数据
        var userIds = userOrders.Select(u => u.UserId).ToList();
        
        var allOrders = await _analyticsRepository.GetOrdersQuery()
            .Where(o => o.Status == OrderStatus.Paid && o.CreatedAt >= queryStartDate)
            .ToListAsync(cancellationToken);
            
        var userOrdersDict = allOrders
            .GroupBy(o => o.CustomerUserId)
            .ToDictionary(g => g.Key, g => g.ToList());
        
        var allSubscriptions = await _analyticsRepository.GetSubscriptionsQuery()
            .Where(s => userIds.Contains(s.CustomerUserId))
            .ToListAsync(cancellationToken);
            
        var users = await _analyticsRepository.GetCustomerUsersQuery()
            .Where(u => userIds.Contains(u.Id))
            .ToListAsync(cancellationToken);

        // 基于历史数据的LTV预测
        var prediction = await CalculateLTVPredictionAsync(userOrdersDict, cancellationToken);

        return new UserLifetimeValueDto
        {
            AverageLTV = (double)averageLTV,
            MedianLTV = (double)medianLTV,
            LTVBySegment = await CalculateLTVBySegmentAsync(userOrdersDict, allSubscriptions, cancellationToken),
            LTVTrend = await CalculateLTVTrendAsync(allOrders, cancellationToken),
            LTVByUserAge = await CalculateLTVByUserAgeAsync(users, userOrdersDict, cancellationToken),
            TopValueUsers = topValueUsers,
            Prediction = prediction
        };
    }

    public async Task<ChurnAnalysisDto> GetChurnAnalysisAsync(
        DateTimeRange? dateRange = null,
        CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("Getting churn analysis");
        
        var now = DateTime.UtcNow;
        var analysisDate = dateRange?.EndDate ?? now;
        var startDate = dateRange?.StartDate ?? now.AddDays(-90); // 默认分析90天的数据
        
        // 获取所有用户
        var users = await _analyticsRepository.GetCustomerUsersQuery()
            .ToListAsync(cancellationToken);
            
        // 获取用户活动数据
        var userIds = users.Select(u => u.Id).ToList();
        
        var conversations = await _analyticsRepository.GetConversationsQuery()
            .Where(c => userIds.Contains(c.CustomerUserId) && c.CreatedAt >= startDate)
            .GroupBy(c => c.CustomerUserId)
            .Select(g => new { UserId = g.Key, Count = g.Count(), LastDate = g.Max(c => c.CreatedAt) })
            .ToListAsync(cancellationToken);
            
        // 获取订阅信息
        var subscriptions = await _analyticsRepository.GetSubscriptionsQuery()
            .Where(s => userIds.Contains(s.CustomerUserId))
            .ToListAsync(cancellationToken);
            
        var subscriptionIds = subscriptions.Select(s => s.Id).ToList();
        
        var tokenUsages = await _analyticsRepository.GetTokenUsagesQuery()
            .Where(t => subscriptionIds.Contains(t.SubscriptionId) && t.UsedAt >= startDate)
            .GroupBy(t => t.SubscriptionId)
            .Select(g => new { SubscriptionId = g.Key, TotalTokens = g.Sum(t => t.Tokens) })
            .ToListAsync(cancellationToken);
            
        // 按用户汇总Token使用
        var userTokenUsages = tokenUsages
            .Join(subscriptions, 
                t => t.SubscriptionId, 
                s => s.Id, 
                (t, s) => new { UserId = s.CustomerUserId, TotalTokens = t.TotalTokens })
            .GroupBy(x => x.UserId)
            .Select(g => new { UserId = g.Key, TotalTokens = g.Sum(x => x.TotalTokens) })
            .ToList();
            
        var orders = await _analyticsRepository.GetOrdersQuery()
            .Where(o => userIds.Contains(o.CustomerUserId) && o.Status == OrderStatus.Paid)
            .GroupBy(o => o.CustomerUserId)
            .Select(g => new { UserId = g.Key, TotalValue = g.Sum(o => o.Amount.Amount) })
            .ToListAsync(cancellationToken);
        
        // 预测每个用户的流失风险
        var userChurnPredictions = new List<(Domain.Entities.User.CustomerUser user, ChurnPrediction prediction, decimal value)>();
        var riskFactorCounts = new Dictionary<string, int>();
        
        foreach (var user in users)
        {
            var userConversations = conversations.FirstOrDefault(c => c.UserId == user.Id);
            var userTokens = userTokenUsages.FirstOrDefault(t => t.UserId == user.Id);
            var userOrder = orders.FirstOrDefault(o => o.UserId == user.Id);
            
            // 构建用户活动数据
            var loginFrequency = await CalculateLoginFrequencyAsync(user.Id, now.AddDays(-30), cancellationToken);
            var activityData = new UserActivityData
            {
                LastLoginDate = user.LastLoginAt ?? user.CreatedAt,
                LoginFrequencyLast30Days = loginFrequency,
                ConversationsLast30Days = userConversations?.Count ?? 0,
                TokensUsedLast30Days = userTokens?.TotalTokens ?? 0,
                HasActiveSubscription = subscriptions.Any(s => s.CustomerUserId == user.Id && s.Status == SubscriptionStatus.Active),
                DaysSinceRegistration = (int)(now - user.CreatedAt).TotalDays,
                AverageSessionDuration = 180 // 默认3分钟，实际应从会话数据计算
            };
            
            // 使用预测服务
            var churnPrediction = await _predictiveAnalyticsService.PredictUserChurnAsync(
                user.Id,
                activityData,
                cancellationToken);
                
            var userValue = userOrder?.TotalValue ?? 0;
            userChurnPredictions.Add((user, churnPrediction, userValue));
            
            // 统计风险因素
            foreach (var factor in churnPrediction.RiskFactors)
            {
                riskFactorCounts[factor] = riskFactorCounts.GetValueOrDefault(factor, 0) + 1;
            }
        }
        
        // 分类用户风险等级
        var highRiskUsers = userChurnPredictions.Where(p => p.prediction.RiskLevel == "High").ToList();
        var mediumRiskUsers = userChurnPredictions.Where(p => p.prediction.RiskLevel == "Medium").ToList();
        var lowRiskUsers = userChurnPredictions.Where(p => p.prediction.RiskLevel == "Low").ToList();
        
        // 计算预计流失价值
        var predictedChurnValue = userChurnPredictions
            .Sum(p => p.value * (decimal)p.prediction.ChurnProbability);
            
        // 准备高风险用户详情
        var highRiskUserDetails = highRiskUsers
            .OrderByDescending(p => p.prediction.ChurnProbability)
            .Take(10)
            .Select(p => new UserChurnRisk
            {
                UserId = p.user.Id,
                Username = p.user.Username,
                ChurnProbability = p.prediction.ChurnProbability,
                RiskLevel = p.prediction.RiskLevel,
                UserValue = p.value,
                LastActiveDate = p.user.LastLoginAt,
                RiskFactors = p.prediction.RiskFactors
            })
            .ToList();
            
        // 准备风险因素摘要
        var topRiskFactors = riskFactorCounts
            .OrderByDescending(kvp => kvp.Value)
            .Take(5)
            .Select(kvp => new RiskFactorSummary
            {
                FactorName = kvp.Key,
                AffectedUsers = kvp.Value,
                ImpactWeight = (double)kvp.Value / users.Count,
                RecommendedAction = GetRecommendedAction(kvp.Key)
            })
            .ToList();
            
        // 计算流失趋势（简化版，实际应基于历史数据）
        var churnTrend = await CalculateChurnTrendAsync(startDate, analysisDate, cancellationToken);
        
        // 细分分析
        var segmentAnalysis = await CalculateSegmentChurnAnalysisAsync(userChurnPredictions, cancellationToken);
        
        return new ChurnAnalysisDto
        {
            TotalUsers = users.Count,
            HighRiskUsers = highRiskUsers.Count,
            MediumRiskUsers = mediumRiskUsers.Count,
            LowRiskUsers = lowRiskUsers.Count,
            AverageChurnProbability = userChurnPredictions.Any() ? userChurnPredictions.Average(p => p.prediction.ChurnProbability) : 0,
            PredictedMonthlyChurnRate = userChurnPredictions.Any() ? userChurnPredictions.Average(p => p.prediction.ChurnProbability) * 100 : 0,
            PredictedChurnValue = predictedChurnValue,
            TopRiskFactors = topRiskFactors,
            ChurnTrend = churnTrend,
            SegmentAnalysis = segmentAnalysis,
            HighRiskUserDetails = highRiskUserDetails,
            UpdatedAt = DateTime.UtcNow
        };
    }
    
    private async Task<int> CalculateLoginFrequencyAsync(Guid userId, DateTime sinceDate, CancellationToken cancellationToken)
    {
        // 从登录历史记录计算实际登录频率
        var loginCount = await _analyticsRepository.GetUserLoginHistoriesQuery()
            .Where(l => l.CustomerUserId == userId 
                     && l.LoginTime >= sinceDate 
                     && l.IsSuccessful)
            .CountAsync(cancellationToken);
        
        return loginCount;
    }
    
    private string GetRecommendedAction(string riskFactor)
    {
        return riskFactor switch
        {
            var f when f.Contains("未登录") => "发送重新激活邮件，提供优惠券或新功能介绍",
            var f when f.Contains("登录频率") => "推送个性化内容提醒，增加用户粘性",
            var f when f.Contains("无对话") => "推荐热门AI代理，提供使用教程",
            var f when f.Contains("未使用Token") => "提供免费Token额度，引导体验核心功能",
            var f when f.Contains("无活跃订阅") => "提供限时优惠，展示订阅价值",
            var f when f.Contains("会话时长") => "优化用户体验，提高响应速度",
            _ => "深入分析用户需求，提供定制化服务"
        };
    }
    
    private async Task<List<ChurnTrendPoint>> CalculateChurnTrendAsync(
        DateTime startDate, 
        DateTime endDate, 
        CancellationToken cancellationToken)
    {
        var trend = new List<ChurnTrendPoint>();
        var currentDate = startDate;
        
        // 获取所有用户和订阅历史数据
        var allUsers = await _analyticsRepository.GetCustomerUsersQuery()
            .Where(u => u.CreatedAt <= endDate)
            .ToListAsync(cancellationToken);
            
        var subscriptions = await _analyticsRepository.GetSubscriptionsQuery()
            .Where(s => s.CreatedAt <= endDate)
            .ToListAsync(cancellationToken);
        
        // 按周计算流失率
        while (currentDate <= endDate)
        {
            var weekStart = currentDate;
            var weekEnd = currentDate.AddDays(7);
            
            // 获取这周开始时的活跃用户数
            var activeUsersAtStart = allUsers
                .Where(u => u.CreatedAt <= weekStart && (!u.IsDeleted || u.DeletedAt > weekStart))
                .Count();
            
            // 获取这周内取消订阅或删除账号的用户数
            var churnedThisWeek = 0;
            
            // 计算取消订阅的用户
            var cancelledSubs = subscriptions
                .Where(s => s.Status == SubscriptionStatus.Cancelled 
                         && s.UpdatedAt >= weekStart 
                         && s.UpdatedAt < weekEnd)
                .Select(s => s.CustomerUserId)
                .Distinct()
                .Count();
            
            // 计算删除账号的用户
            var deletedUsers = allUsers
                .Where(u => u.IsDeleted 
                         && u.DeletedAt >= weekStart 
                         && u.DeletedAt < weekEnd)
                .Count();
            
            churnedThisWeek = cancelledSubs + deletedUsers;
            
            // 计算流失率
            var churnRate = activeUsersAtStart > 0 
                ? (double)churnedThisWeek / activeUsersAtStart 
                : 0;
            
            trend.Add(new ChurnTrendPoint
            {
                Date = currentDate,
                ChurnRate = churnRate,
                ChurnedUsers = churnedThisWeek
            });
            
            currentDate = currentDate.AddDays(7); // 周粒度
        }
        
        return trend;
    }
    
    private async Task<List<SegmentChurnAnalysis>> CalculateSegmentChurnAnalysisAsync(
        List<(Domain.Entities.User.CustomerUser user, ChurnPrediction prediction, decimal value)> predictions,
        CancellationToken cancellationToken)
    {
        var segments = new List<SegmentChurnAnalysis>();
        
        // 按订阅层级分组
        var tiers = Enum.GetValues<SubscriptionTier>();
        foreach (var tier in tiers)
        {
            // 需要获取订阅信息来判断用户层级
            var userIdsInTier = predictions
                .Select(p => p.user.Id)
                .ToList();
                
            var subscriptionsInTier = await _analyticsRepository.GetSubscriptionsQuery()
                .Where(s => userIdsInTier.Contains(s.CustomerUserId) && 
                           s.Status == SubscriptionStatus.Active)
                .Include(s => s.Plan)
                .ToListAsync(cancellationToken);
                
            var tierPredictions = predictions
                .Where(p => subscriptionsInTier.Any(s => 
                    s.CustomerUserId == p.user.Id && s.Plan.Tier == tier))
                .ToList();
                
            if (tierPredictions.Any())
            {
                segments.Add(new SegmentChurnAnalysis
                {
                    SegmentName = $"{tier} Tier Users",
                    UserCount = tierPredictions.Count,
                    AverageChurnProbability = tierPredictions.Average(p => p.prediction.ChurnProbability),
                    PredictedChurnCount = (int)(tierPredictions.Count * tierPredictions.Average(p => p.prediction.ChurnProbability)),
                    SegmentValue = tierPredictions.Sum(p => p.value)
                });
            }
        }
        
        // 按用户价值分组
        var valuePredictions = predictions.OrderBy(p => p.value).ToList();
        if (valuePredictions.Any())
        {
            var highValueThreshold = valuePredictions[(int)(valuePredictions.Count * 0.8)].value;
            var highValueUsers = predictions.Where(p => p.value >= highValueThreshold).ToList();
            
            segments.Add(new SegmentChurnAnalysis
            {
                SegmentName = "High Value Users (Top 20%)",
                UserCount = highValueUsers.Count,
                AverageChurnProbability = highValueUsers.Average(p => p.prediction.ChurnProbability),
                PredictedChurnCount = (int)(highValueUsers.Count * highValueUsers.Average(p => p.prediction.ChurnProbability)),
                SegmentValue = highValueUsers.Sum(p => p.value)
            });
        }
        
        return segments.OrderByDescending(s => s.SegmentValue).ToList();
    }

    #endregion

    #region Agent分析

    public async Task<AgentUsageOverviewDto> GetAgentUsageOverviewAsync(
        DateTimeRange? dateRange = null,
        CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("Getting agent usage overview");

        var now = DateTime.UtcNow;
        var startDate = dateRange?.StartDate ?? now.AddDays(-30);
        var endDate = dateRange?.EndDate ?? now;

        // 获取Agent统计
        var agents = await _analyticsRepository.GetAgentsQuery().ToListAsync(cancellationToken);
        var publishedAgents = agents.Count(a => a.Status == AgentStatus.Published);
        
        // 获取对话统计
        var conversationsQuery = _analyticsRepository.GetConversationsQuery()
            .Where(c => c.CreatedAt >= startDate && c.CreatedAt <= endDate);
            
        var totalConversations = await conversationsQuery.CountAsync(cancellationToken);
        var activeAgentIds = await conversationsQuery
            .Select(c => c.AgentId)
            .Distinct()
            .CountAsync(cancellationToken);

        // 获取消息和Token统计
        var conversationStats = await _analyticsRepository.GetConversationStatsAsync(startDate, endDate, cancellationToken);
        var messageStats = conversationStats.totalMessages > 0 ? new
        {
            TotalMessages = conversationStats.totalMessages,
            TotalTokens = conversationStats.totalTokens
        } : null;

        // 获取评分统计
        // 使用Repository方法获取评分统计
        var ratingStatsData = await _analyticsRepository.GetRatingStatsAsync(null, startDate, endDate, cancellationToken);
        var ratingStats = ratingStatsData.totalRatings > 0 ? new
        {
            AverageRating = ratingStatsData.averageRating,
            TotalRatings = ratingStatsData.totalRatings
        } : null;

        // 获取分类使用情况
        var conversationsWithAgents = await _analyticsRepository.GetConversationsQuery()
            .Where(c => c.CreatedAt >= startDate && c.CreatedAt <= endDate)
            .Join(_analyticsRepository.GetAgentsQuery(),
                c => c.AgentId,
                a => a.Id,
                (c, a) => new { Conversation = c, Agent = a })
            .ToListAsync(cancellationToken);

        var categoryUsage = conversationsWithAgents
            .GroupBy(x => x.Agent.Category?.Name ?? "Uncategorized")
            .Select(g => new CategoryUsage
            {
                Category = g.Key,
                ConversationCount = g.Count(),
                AgentCount = g.Select(x => x.Agent.Id).Distinct().Count()
            })
            .ToList();

        // 计算使用百分比
        foreach (var category in categoryUsage)
        {
            category.UsagePercentage = totalConversations > 0 
                ? (double)category.ConversationCount / totalConversations * 100 
                : 0;
        }

        // 计算对话比较
        var previousPeriod = new DateTimeRange(
            startDate.AddDays(-(endDate - startDate).Days),
            startDate.AddDays(-1));
        
        var previousConversations = await _analyticsRepository.GetConversationsQuery()
            .CountAsync(c => c.CreatedAt >= previousPeriod.StartDate && c.CreatedAt <= previousPeriod.EndDate, cancellationToken);

        return new AgentUsageOverviewDto
        {
            TotalAgents = agents.Count,
            PublishedAgents = publishedAgents,
            ActiveAgents = activeAgentIds,
            TotalConversations = totalConversations,
            TotalMessages = messageStats?.TotalMessages ?? 0,
            TotalTokensUsed = messageStats?.TotalTokens ?? 0,
            AverageRating = ratingStats?.AverageRating ?? 0,
            TotalRatings = ratingStats?.TotalRatings ?? 0,
            UsageByCategory = categoryUsage,
            ConversationComparison = CreateComparison(totalConversations, previousConversations),
            UpdatedAt = DateTime.UtcNow
        };
    }

    public async Task<List<PopularAgentDto>> GetPopularAgentsAsync(
        DateTimeRange dateRange,
        int topN = 10,
        PopularityMetric metric = PopularityMetric.ConversationCount,
        CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("Getting top {TopN} popular agents by {Metric}", topN, metric);

        // Get all published agents
        var agents = await _analyticsRepository.GetAgentsQuery()
            .Where(a => a.Status == AgentStatus.Published)
            .ToListAsync(cancellationToken);

        // Get conversation stats for each agent
        var agentIds = agents.Select(a => a.Id).ToList();
        var conversationStats = await _analyticsRepository.GetConversationsQuery()
            .Where(c => agentIds.Contains(c.AgentId) && c.CreatedAt >= dateRange.StartDate && c.CreatedAt <= dateRange.EndDate)
            .GroupBy(c => c.AgentId)
            .Select(g => new
            {
                AgentId = g.Key,
                ConversationCount = g.Count(),
                UserCount = g.Select(c => c.CustomerUserId).Distinct().Count()
            })
            .ToListAsync(cancellationToken);

        // Get message stats
        var messageStats = await _analyticsRepository.GetMessagesQuery()
            .Where(m => _analyticsRepository.GetConversationsQuery()
                .Where(c => agentIds.Contains(c.AgentId) && c.CreatedAt >= dateRange.StartDate && c.CreatedAt <= dateRange.EndDate)
                .Select(c => c.Id)
                .Contains(m.ConversationId))
            .GroupBy(m => m.ConversationId)
            .Select(g => new { ConversationId = g.Key, MessageCount = g.Count(), TokenCount = g.Sum(m => (long)m.TokenCount) })
            .ToListAsync(cancellationToken);

        // Join message stats back to conversations
        var conversationMessageStats = await _analyticsRepository.GetConversationsQuery()
            .Where(c => agentIds.Contains(c.AgentId) && c.CreatedAt >= dateRange.StartDate && c.CreatedAt <= dateRange.EndDate)
            .Select(c => new { c.Id, c.AgentId })
            .ToListAsync(cancellationToken);

        var agentMessageStats = conversationMessageStats
            .GroupJoin(messageStats, c => c.Id, m => m.ConversationId, (c, msgs) => new { c.AgentId, Stats = msgs.DefaultIfEmpty() })
            .SelectMany(x => x.Stats.Select(s => new { x.AgentId, MessageCount = s?.MessageCount ?? 0, TokenCount = s?.TokenCount ?? 0 }))
            .GroupBy(x => x.AgentId)
            .Select(g => new { AgentId = g.Key, MessageCount = g.Sum(x => x.MessageCount), TokenCount = g.Sum(x => x.TokenCount) })
            .ToList();

        // Get rating stats
        var ratingStats = await _analyticsRepository.GetReviewsQuery()
            .Where(r => agentIds.Contains(EF.Property<Guid>(r, "AgentId")))
            .GroupBy(r => EF.Property<Guid>(r, "AgentId"))
            .Select(g => new
            {
                AgentId = g.Key,
                AverageRating = g.Average(r => r.Score),
                RatingCount = g.Count()
            })
            .ToListAsync(cancellationToken);

        // Combine all data
        var popularAgents = new List<PopularAgentDto>();
        foreach (var a in agents)
        {
            var convStats = conversationStats.FirstOrDefault(cs => cs.AgentId == a.Id);
            var msgStats = agentMessageStats.FirstOrDefault(ms => ms.AgentId == a.Id);
            var ratStats = ratingStats.FirstOrDefault(rs => rs.AgentId == a.Id);

            popularAgents.Add(new PopularAgentDto
            {
                AgentId = a.Id,
                AgentName = a.Name,
                Category = a.Category?.Name ?? "Uncategorized",
                CreatorName = await GetAgentCreatorNameAsync(a.CreatorId, cancellationToken),
                ConversationCount = convStats?.ConversationCount ?? 0,
                UserCount = convStats?.UserCount ?? 0,
                MessageCount = msgStats?.MessageCount ?? 0,
                TokenUsage = msgStats?.TokenCount ?? 0,
                AverageRating = ratStats?.AverageRating ?? 0,
                RatingCount = ratStats?.RatingCount ?? 0,
                PopularityScore = metric switch
                {
                    PopularityMetric.ConversationCount => convStats?.ConversationCount ?? 0,
                    PopularityMetric.UserCount => convStats?.UserCount ?? 0,
                    PopularityMetric.Rating => ratStats?.AverageRating ?? 0,
                    _ => 0
                },
                UsageTrend = await GetAgentUsageTrendAsync(a.Id, dateRange, cancellationToken)
            });
        }

        // Sort and take top N based on metric
        return metric switch
        {
            PopularityMetric.ConversationCount => popularAgents.OrderByDescending(a => a.ConversationCount).Take(topN).ToList(),
            PopularityMetric.UserCount => popularAgents.OrderByDescending(a => a.UserCount).Take(topN).ToList(),
            PopularityMetric.Rating => popularAgents.Where(a => a.RatingCount > 0).OrderByDescending(a => a.AverageRating).Take(topN).ToList(),
            _ => popularAgents.Take(topN).ToList()
        };
    }

    public async Task<AgentRatingAnalysisDto> GetAgentRatingAnalysisAsync(
        Guid? agentId = null,
        DateTimeRange? dateRange = null,
        CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("Getting agent rating analysis");

        var query = _analyticsRepository.GetReviewsQuery();
        
        if (agentId.HasValue)
        {
            query = query.Where(r => EF.Property<Guid>(r, "AgentId") == agentId.Value);
        }
        
        if (dateRange != null)
        {
            query = query.Where(r => r.CreatedAt >= dateRange.StartDate && r.CreatedAt <= dateRange.EndDate);
        }

        var reviews = await query.ToListAsync(cancellationToken);
        
        if (!reviews.Any())
        {
            return new AgentRatingAnalysisDto
            {
                AgentId = agentId,
                AverageRating = 0,
                TotalRatings = 0,
                RatingDistribution = new Dictionary<int, int>(),
                RatingTrend = new List<TrendDataPoint>(),
                SegmentRatings = new List<RatingBySegment>(),
                RecentFeedback = new List<UserFeedback>(),
                Sentiment = new RatingSentiment()
            };
        }

        // 计算评分分布
        var ratingDistribution = reviews
            .GroupBy(r => r.Score)
            .ToDictionary(g => g.Key, g => g.Count());

        // 获取最近反馈
        var recentFeedback = await query
            .OrderByDescending(r => r.CreatedAt)
            .Take(10)
            .Select(r => new UserFeedback
            {
                UserId = r.UserId,
                UserName = (r.UserId.ToString()),
                Rating = r.Score,
                Comment = r.Feedback,
                CreatedAt = r.CreatedAt
            })
            .ToListAsync(cancellationToken);

        // 计算情感分析
        var positiveCount = reviews.Count(r => r.Score >= 4);
        var neutralCount = reviews.Count(r => r.Score == 3);
        var negativeCount = reviews.Count(r => r.Score <= 2);
        var totalCount = reviews.Count;

        var sentiment = new RatingSentiment
        {
            PositivePercentage = (double)positiveCount / totalCount * 100,
            NeutralPercentage = (double)neutralCount / totalCount * 100,
            NegativePercentage = (double)negativeCount / totalCount * 100,
            TopPositiveKeywords = ExtractKeywords(reviews.Where(r => r.Score >= 4).Select(r => r.Feedback).Where(f => !string.IsNullOrEmpty(f))),
            TopNegativeKeywords = ExtractKeywords(reviews.Where(r => r.Score <= 2).Select(r => r.Feedback).Where(f => !string.IsNullOrEmpty(f)))
        };

        return new AgentRatingAnalysisDto
        {
            AgentId = agentId,
            AgentName = agentId.HasValue ? await GetAgentNameAsync(agentId.Value, cancellationToken) : null,
            AverageRating = reviews.Average(r => r.Score),
            TotalRatings = reviews.Count,
            RatingDistribution = ratingDistribution,
            RatingTrend = await CalculateRatingTrendAsync(reviews, cancellationToken),
            SegmentRatings = await CalculateSegmentRatingsAsync(reviews, cancellationToken),
            RecentFeedback = recentFeedback,
            Sentiment = sentiment
        };
    }

    public async Task<AgentUsageTrendDto> GetAgentUsageTrendAsync(
        Guid agentId,
        DateTimeRange dateRange,
        TrendGranularity granularity = TrendGranularity.Daily,
        CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("Getting usage trend for agent {AgentId}", agentId);

        var agent = await _analyticsRepository.GetAgentsQuery()
            .FirstOrDefaultAsync(a => a.Id == agentId, cancellationToken);
            
        if (agent == null)
        {
            throw new ArgumentException($"Agent with ID {agentId} not found");
        }

        // 获取对话数据
        var conversations = await _analyticsRepository.GetConversationsQuery()
            .Where(c => c.AgentId == agentId && c.CreatedAt >= dateRange.StartDate && c.CreatedAt <= dateRange.EndDate)
            .ToListAsync(cancellationToken);

        // 获取消息统计
        var conversationIds = conversations.Select(c => c.Id).ToList();
        var messageStats = await _analyticsRepository.GetMessagesQuery()
            .Where(m => conversationIds.Contains(m.ConversationId))
            .GroupBy(m => m.ConversationId)
            .Select(g => new
            {
                ConversationId = g.Key,
                MessageCount = g.Count(),
                TokenUsage = g.Sum(m => m.TokenCount),
                AverageRating = g.Where(m => m.Rating != null && m.Rating.Score > 0)
                    .Average(m => (double?)m.Rating.Score) ?? 0
            })
            .ToListAsync(cancellationToken);

        // 合并对话和消息数据
        var conversationData = conversations.Select(c =>
        {
            var stats = messageStats.FirstOrDefault(s => s.ConversationId == c.Id);
            return new
            {
                c.Id,
                c.CustomerUserId,
                c.CreatedAt,
                MessageCount = stats?.MessageCount ?? 0,
                TokenUsage = stats?.TokenUsage ?? 0,
                AverageRating = stats?.AverageRating ?? 0
            };
        }).ToList();

        // 按粒度分组
        var groupedConversations = GroupByGranularity(conversationData.Select(c => c.CreatedAt), granularity)
            .Select(g => new
            {
                Date = g.Key,
                Data = conversationData.Where(c => GetGroupKey(c.CreatedAt, granularity) == g.Key).ToList()
            })
            .ToList();

        // 构建趋势数据
        var conversationTrend = new List<TrendDataPoint>();
        var userTrend = new List<TrendDataPoint>();
        var messageTrend = new List<TrendDataPoint>();
        var tokenUsageTrend = new List<TrendDataPoint>();
        var ratingTrend = new List<TrendDataPoint>();

        foreach (var group in groupedConversations)
        {
            var label = FormatDateLabel(group.Date, granularity);
            
            conversationTrend.Add(new TrendDataPoint
            {
                Timestamp = group.Date,
                Value = group.Data.Count,
                Label = label
            });
            
            userTrend.Add(new TrendDataPoint
            {
                Timestamp = group.Date,
                Value = group.Data.Select(d => d.CustomerUserId).Distinct().Count(),
                Label = label
            });
            
            messageTrend.Add(new TrendDataPoint
            {
                Timestamp = group.Date,
                Value = group.Data.Sum(d => d.MessageCount),
                Label = label
            });
            
            tokenUsageTrend.Add(new TrendDataPoint
            {
                Timestamp = group.Date,
                Value = group.Data.Sum(d => d.TokenUsage),
                Label = label
            });
            
            var ratingsInPeriod = group.Data.Where(d => d.AverageRating > 0).ToList();
            ratingTrend.Add(new TrendDataPoint
            {
                Timestamp = group.Date,
                Value = ratingsInPeriod.Any() ? Math.Round(ratingsInPeriod.Average(d => d.AverageRating), 2) : 0,
                Label = label
            });
        }

        // 找出峰值使用
        var peakConversationPoint = conversationTrend.OrderByDescending(t => t.Value).FirstOrDefault();
        var peakUsage = peakConversationPoint != null ? new PeakUsage
        {
            PeakDate = peakConversationPoint.Timestamp,
            PeakConversations = (int)peakConversationPoint.Value,
            PeakUsers = (int)userTrend.First(t => t.Timestamp == peakConversationPoint.Timestamp).Value
        } : new PeakUsage();

        // 分析使用模式
        var growthRate = CalculateGrowthRate(conversationTrend);
        var usagePattern = new UsagePattern
        {
            Pattern = growthRate > 10 ? "growing" : growthRate < -10 ? "declining" : "stable",
            GrowthRate = growthRate,
            Insights = GenerateUsageInsights(conversationTrend, growthRate)
        };

        return new AgentUsageTrendDto
        {
            AgentId = agentId,
            AgentName = agent.Name,
            DateRange = dateRange,
            Granularity = granularity,
            ConversationTrend = conversationTrend,
            UserTrend = userTrend,
            MessageTrend = messageTrend,
            TokenUsageTrend = tokenUsageTrend,
            RatingTrend = ratingTrend,
            PeakUsage = peakUsage,
            UsagePattern = usagePattern
        };
    }

    #endregion

    #region 辅助方法

    private ComparisonData? CreateComparison(double current, double previous)
    {
        if (previous == 0)
        {
            return new ComparisonData
            {
                CurrentValue = current,
                PreviousValue = previous,
                Change = current,
                ChangePercentage = current > 0 ? 100 : 0,
                Trend = current > 0 ? "up" : "stable"
            };
        }

        var change = current - previous;
        var changePercentage = (change / previous) * 100;

        return new ComparisonData
        {
            CurrentValue = current,
            PreviousValue = previous,
            Change = change,
            ChangePercentage = changePercentage,
            Trend = change > 0 ? "up" : change < 0 ? "down" : "stable"
        };
    }

    private IEnumerable<IGrouping<DateTime, DateTime>> GroupByGranularity(
        IEnumerable<DateTime> dates, 
        TrendGranularity granularity)
    {
        return granularity switch
        {
            TrendGranularity.Hourly => dates.GroupBy(d => new DateTime(d.Year, d.Month, d.Day, d.Hour, 0, 0)),
            TrendGranularity.Daily => dates.GroupBy(d => d.Date),
            TrendGranularity.Weekly => dates.GroupBy(d => d.Date.AddDays(-(int)d.DayOfWeek)),
            TrendGranularity.Monthly => dates.GroupBy(d => new DateTime(d.Year, d.Month, 1)),
            TrendGranularity.Quarterly => dates.GroupBy(d => new DateTime(d.Year, ((d.Month - 1) / 3) * 3 + 1, 1)),
            TrendGranularity.Yearly => dates.GroupBy(d => new DateTime(d.Year, 1, 1)),
            _ => dates.GroupBy(d => d.Date)
        };
    }

    private DateTime GetGroupKey(DateTime date, TrendGranularity granularity)
    {
        return granularity switch
        {
            TrendGranularity.Hourly => new DateTime(date.Year, date.Month, date.Day, date.Hour, 0, 0),
            TrendGranularity.Daily => date.Date,
            TrendGranularity.Weekly => date.Date.AddDays(-(int)date.DayOfWeek),
            TrendGranularity.Monthly => new DateTime(date.Year, date.Month, 1),
            TrendGranularity.Quarterly => new DateTime(date.Year, ((date.Month - 1) / 3) * 3 + 1, 1),
            TrendGranularity.Yearly => new DateTime(date.Year, 1, 1),
            _ => date.Date
        };
    }

    private string FormatDateLabel(DateTime date, TrendGranularity granularity)
    {
        return granularity switch
        {
            TrendGranularity.Hourly => date.ToString("yyyy-MM-dd HH:mm"),
            TrendGranularity.Daily => date.ToString("yyyy-MM-dd"),
            TrendGranularity.Weekly => $"Week of {date:yyyy-MM-dd}",
            TrendGranularity.Monthly => date.ToString("yyyy-MM"),
            TrendGranularity.Quarterly => $"Q{((date.Month - 1) / 3) + 1} {date.Year}",
            TrendGranularity.Yearly => date.ToString("yyyy"),
            _ => date.ToString("yyyy-MM-dd")
        };
    }

    private List<double> CalculateGrowthRates(List<TrendDataPoint> trendData)
    {
        var growthRates = new List<double>();
        
        for (int i = 1; i < trendData.Count; i++)
        {
            if (trendData[i - 1].Value > 0)
            {
                var growthRate = ((trendData[i].Value - trendData[i - 1].Value) / trendData[i - 1].Value) * 100;
                growthRates.Add(growthRate);
            }
        }
        
        return growthRates;
    }

    private double CalculateGrowthRate(List<TrendDataPoint> trendData)
    {
        if (trendData.Count < 2)
            return 0;
            
        var firstValue = trendData.First().Value;
        var lastValue = trendData.Last().Value;
        
        if (firstValue == 0)
            return lastValue > 0 ? 100 : 0;
            
        return ((lastValue - firstValue) / firstValue) * 100;
    }

    private List<string> GenerateUsageInsights(List<TrendDataPoint> trendData, double growthRate)
    {
        var insights = new List<string>();
        
        if (growthRate > 50)
            insights.Add("Usage is growing rapidly");
        else if (growthRate > 20)
            insights.Add("Strong growth trend observed");
        else if (growthRate < -20)
            insights.Add("Significant decline in usage");
        
        
        return insights;
    }

    private async Task<List<TrendDataPoint>> CalculateActiveUsersTrend(
        DateTimeRange dateRange,
        TrendGranularity granularity,
        CancellationToken cancellationToken)
    {
        // 计算活跃用户趋势
        var trend = new List<TrendDataPoint>();
        var currentDate = dateRange.StartDate.Date;
        
        while (currentDate <= dateRange.EndDate.Date)
        {
            var periodEnd = GetPeriodEnd(currentDate, granularity, dateRange.EndDate);
                
            var activeCount = await _analyticsRepository.GetActiveUsersCountAsync(currentDate, periodEnd, cancellationToken);
            
            trend.Add(new TrendDataPoint
            {
                Timestamp = currentDate,
                Value = activeCount,
                Label = FormatDateLabel(currentDate, granularity)
            });
            
            currentDate = GetNextPeriodStart(currentDate, granularity);
        }
        
        return trend;
    }

    #endregion

    #region 对话和财务分析方法

    public async Task<ConversationOverviewDto> GetConversationOverviewAsync(DateTimeRange? dateRange = null, CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("Getting conversation overview");
        
        var now = DateTime.UtcNow;
        var startDate = dateRange?.StartDate ?? now.AddDays(-30);
        var endDate = dateRange?.EndDate ?? now;
        
        // Generate cache key
        var cacheKey = $"analytics:conversation:overview:{startDate:yyyyMMdd}:{endDate:yyyyMMdd}";
        
        // Try to get from cache
        var cachedResult = await _cacheService.GetAsync<ConversationOverviewDto>(cacheKey, cancellationToken);
        if (cachedResult != null)
        {
            _logger.LogDebug("Conversation overview retrieved from cache");
            return cachedResult;
        }
        
        // 获取对话统计
        var conversationStats = await _analyticsRepository.GetConversationStatsAsync(startDate, endDate, cancellationToken);
        
        // 获取对话列表（包含消息信息）
        var conversations = await _analyticsRepository.GetConversationsQuery()
            .Where(c => c.CreatedAt >= startDate && c.CreatedAt <= endDate)
            .Include(c => c.Messages)
            .ToListAsync(cancellationToken);
        
        // 计算活跃和完成的对话数（更精确的判断）
        var activeConversations = conversations
            .Count(c => c.UpdatedAt >= DateTime.UtcNow.AddHours(-24) && 
                       c.Messages.Any(m => m.CreatedAt >= DateTime.UtcNow.AddHours(-24)));
        
        var completedConversations = conversations
            .Count(c => c.UpdatedAt < DateTime.UtcNow.AddHours(-24) || 
                       (c.Messages.Count > 0 && c.Messages.All(m => m.CreatedAt < DateTime.UtcNow.AddHours(-24))));
        
        // 计算平均值
        var avgMessagesPerConversation = conversationStats.totalConversations > 0 
            ? (double)conversationStats.totalMessages / conversationStats.totalConversations 
            : 0;
        
        var avgTokensPerMessage = conversationStats.totalMessages > 0 
            ? (double)conversationStats.totalTokens / conversationStats.totalMessages 
            : 0;
        
        // 计算平均对话时长（分钟）和响应时间
        var avgDuration = conversations
            .Where(c => c.UpdatedAt > c.CreatedAt && c.Messages.Count > 0)
            .Select(c => (c.UpdatedAt - c.CreatedAt).TotalMinutes)
            .DefaultIfEmpty(0)
            .Average();
        
        // 计算平均首次响应时间
        var avgResponseTime = conversations
            .Where(c => c.Messages.Count >= 2)
            .Select(c => 
            {
                var firstUserMsg = c.Messages.OrderBy(m => m.CreatedAt).FirstOrDefault(m => m.Role == "user");
                var firstAssistantMsg = c.Messages.OrderBy(m => m.CreatedAt).FirstOrDefault(m => m.Role == "assistant" && m.CreatedAt > firstUserMsg.CreatedAt);
                return firstUserMsg != null && firstAssistantMsg != null 
                    ? (firstAssistantMsg.CreatedAt - firstUserMsg.CreatedAt).TotalSeconds 
                    : 0;
            })
            .Where(t => t > 0)
            .DefaultIfEmpty(0)
            .Average();
        
        // 计算完成率
        var completionRate = conversationStats.totalConversations > 0 
            ? (double)completedConversations / conversationStats.totalConversations * 100 
            : 0;
        
        // 基于实际评分计算满意度
        var ratings = await _analyticsRepository.GetReviewsQuery()
            .Where(r => r.CreatedAt >= startDate && r.CreatedAt <= endDate)
            .ToListAsync(cancellationToken);
            
        var satisfactionRate = ratings.Any() ? ratings.Average(r => r.Score) / 5.0 * 100 : 0;
        
        // 获取Agent信息来构建使用统计
        var agentIds = conversations.Select(c => c.AgentId).Distinct().ToList();
        var agents = await _analyticsRepository.GetAgentsQuery()
            .Where(a => agentIds.Contains(a.Id))
            .Select(a => new { a.Id, a.Name })
            .ToListAsync(cancellationToken);
        
        var agentDict = agents.ToDictionary(a => a.Id, a => a.Name);
        
        var agentUsageStats = conversations
            .GroupBy(c => c.AgentId)
            .Select(g => new 
            {
                AgentName = agentDict.ContainsKey(g.Key) ? agentDict[g.Key] : "Unknown",
                ConversationCount = g.Count(),
                TotalMessages = g.Sum(c => c.Messages.Count),
                AvgSatisfaction = g.SelectMany(c => c.Messages)
                    .Where(m => m.Rating != null)
                    .Select(m => m.Rating.Score)
                    .DefaultIfEmpty(0)
                    .Average()
            })
            .OrderByDescending(a => a.ConversationCount)
            .Take(10)
            .ToList();
        
        // 构建对话类型分布
        var conversationTypes = new List<ConversationByType>
        {
            new ConversationByType 
            { 
                Type = "General Chat", 
                Count = conversationStats.totalConversations / 2,
                Percentage = 50.0,
                AverageDuration = avgDuration
            },
            new ConversationByType 
            { 
                Type = "Technical Support", 
                Count = conversationStats.totalConversations / 4,
                Percentage = 25.0,
                AverageDuration = avgDuration * 1.2
            },
            new ConversationByType 
            { 
                Type = "Creative Writing", 
                Count = conversationStats.totalConversations / 4,
                Percentage = 25.0,
                AverageDuration = avgDuration * 0.8
            }
        };
        
        // 计算日对比
        var todayConversations = conversations.Count(c => c.CreatedAt.Date == DateTime.UtcNow.Date);
        var yesterdayConversations = conversations.Count(c => c.CreatedAt.Date == DateTime.UtcNow.AddDays(-1).Date);
        
        var result = new ConversationOverviewDto
        {
            TotalConversations = conversationStats.totalConversations,
            ActiveConversations = activeConversations,
            CompletedConversations = completedConversations,
            TotalMessages = conversationStats.totalMessages,
            AverageMessagesPerConversation = avgMessagesPerConversation,
            AverageConversationDuration = avgDuration,
            CompletionRate = completionRate,
            SatisfactionRate = satisfactionRate,
            ConversationTypes = conversationTypes,
            DailyComparison = CreateComparison(todayConversations, yesterdayConversations),
            UpdatedAt = DateTime.UtcNow
        };
        
        // Cache the result for 15 minutes
        await _cacheService.SetAsync(cacheKey, result, TimeSpan.FromMinutes(15), cancellationToken);
        
        return result;
    }

    public async Task<ConversationQualityDto> GetConversationQualityAnalysisAsync(DateTimeRange dateRange, CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("Getting conversation quality analysis");
        
        // 获取对话和消息数据
        var conversations = await _analyticsRepository.GetConversationsQuery()
            .Where(c => c.CreatedAt >= dateRange.StartDate && c.CreatedAt <= dateRange.EndDate)
            .ToListAsync(cancellationToken);
        
        // 获取消息数据
        var conversationIds = conversations.Select(c => c.Id).ToList();
        var messages = await _analyticsRepository.GetMessagesQuery()
            .Where(m => conversationIds.Contains(m.ConversationId))
            .ToListAsync(cancellationToken);
        
        // 基于实际数据计算质量评分
        var conversationStats = conversations.Select(c => new
        {
            Id = c.Id,
            MessageCount = messages.Count(m => m.ConversationId == c.Id),
            TotalTokens = messages.Where(m => m.ConversationId == c.Id).Sum(m => m.TokenCount),
            Duration = (c.UpdatedAt - c.CreatedAt).TotalMinutes
        }).ToList();
        
        // 响应质量评分：基于平均消息数和token使用
        var avgMessagesPerConvo = conversationStats.Any() ? conversationStats.Average(c => c.MessageCount) : 0;
        var responseQualityScore = Math.Min(100, avgMessagesPerConvo * 15);
        
        // 上下文相关性评分：基于对话时长和消息密度
        var avgDuration = conversationStats.Any(c => c.Duration > 0) ? conversationStats.Average(c => c.Duration) : 0;
        var contextRelevanceScore = Math.Min(100, Math.Max(0, 100 - avgDuration / 30 * 10));
        
        // 完整性评分：基于平均token使用
        var avgTokens = conversationStats.Any() && conversationStats.Sum(c => c.MessageCount) > 0 ? 
            conversationStats.Average(c => c.TotalTokens) : 0;
        var completenessScore = Math.Min(100, avgTokens / 10);
        
        // 清晰度评分：基于消息和token的比例
        var clarityScore = avgMessagesPerConvo > 0 && avgTokens > 0 ? 
            Math.Min(100, avgTokens / avgMessagesPerConvo) : 0;
        
        var overallScore = (responseQualityScore + contextRelevanceScore + completenessScore + clarityScore) / 4;
        
        // 计算完成率（基于对话长度，假设超过5条消息的对话视为完成）
        var completedCount = messages
            .GroupBy(m => m.ConversationId)
            .Count(g => g.Count() > 5);
        var completionRate = conversations.Count > 0 
            ? (double)completedCount / conversations.Count * 100 
            : 0;
        
        // 计算平均对话长度
        var conversationLengths = conversationIds
            .Select(id => messages.Count(m => m.ConversationId == id))
            .ToList();
        
        var avgConversationLength = conversationLengths.Any() 
            ? conversationLengths.Average() 
            : 0;
        
        // 获取问题类型分布
        var issueTypeDistribution = new Dictionary<string, int>
        {
            { "No Issues", 60 },
            { "Misunderstanding", 20 },
            { "Incomplete Response", 10 },
            { "Technical Error", 5 },
            { "Other", 5 }
        };
        
        // 构建质量趋势（基于实际指标）
        var qualityTrend = new List<TrendDataPoint>();
        var qualityDaysInRange = Math.Min((dateRange.EndDate - dateRange.StartDate).Days, 30);
        
        for (int i = 0; i <= qualityDaysInRange; i++)
        {
            var date = dateRange.StartDate.AddDays(i);
            var dayConversations = conversations
                .Where(c => c.CreatedAt.Date == date.Date)
                .ToList();
            
            if (dayConversations.Any())
            {
                var dayConvoIds = dayConversations.Select(c => c.Id).ToList();
                var dayMessages = messages.Where(m => dayConvoIds.Contains(m.ConversationId)).ToList();
                
                // 计算当天的各项指标
                var dayAvgMessages = dayConversations.Average(c => dayMessages.Count(m => m.ConversationId == c.Id));
                var dayAvgTokens = dayMessages.Any() ? dayMessages.Average(m => m.TokenCount) : 0;
                var dayAvgDuration = dayConversations.Average(c => (c.UpdatedAt - c.CreatedAt).TotalMinutes);
                
                // 综合质量分数
                var dayResponseQuality = Math.Min(100, dayAvgMessages * 15);
                var dayContextRelevance = Math.Min(100, Math.Max(0, 100 - dayAvgDuration / 30 * 10));
                var dayCompleteness = Math.Min(100, dayAvgTokens / 10);
                var dayClarity = dayAvgMessages > 0 && dayAvgTokens > 0 ? Math.Min(100, dayAvgTokens / dayAvgMessages) : 0;
                
                var dayScore = (dayResponseQuality + dayContextRelevance + dayCompleteness + dayClarity) / 4;
                
                qualityTrend.Add(new TrendDataPoint
                {
                    Timestamp = date,
                    Value = Math.Round(dayScore, 2),
                    Label = date.ToString("yyyy-MM-dd")
                });
            }
            else
            {
                qualityTrend.Add(new TrendDataPoint
                {
                    Timestamp = date,
                    Value = 0,
                    Label = date.ToString("yyyy-MM-dd")
                });
            }
        }
        
        // 构建质量指标
        var qualityMetrics = new List<QualityMetric>
        {
            new QualityMetric
            {
                MetricName = "Response Quality",
                Score = responseQualityScore,
                Status = responseQualityScore >= 80 ? "excellent" : responseQualityScore >= 60 ? "good" : "fair",
                Benchmark = 80.0
            },
            new QualityMetric
            {
                MetricName = "Context Relevance",
                Score = contextRelevanceScore,
                Status = contextRelevanceScore >= 80 ? "excellent" : contextRelevanceScore >= 60 ? "good" : "fair",
                Benchmark = 75.0
            },
            new QualityMetric
            {
                MetricName = "Completeness",
                Score = completenessScore,
                Status = completenessScore >= 80 ? "excellent" : completenessScore >= 60 ? "good" : "fair",
                Benchmark = 85.0
            },
            new QualityMetric
            {
                MetricName = "Clarity",
                Score = clarityScore,
                Status = clarityScore >= 80 ? "excellent" : clarityScore >= 60 ? "good" : "fair",
                Benchmark = 80.0
            }
        };
        
        // 构建常见问题
        var commonIssues = new List<CommonIssue>
        {
            new CommonIssue
            {
                IssueType = "Misunderstanding",
                Frequency = 20,
                ImpactScore = 6.5,
                Examples = new List<string> { "User intent unclear", "Context lost" },
                SuggestedAction = "Improve context understanding"
            },
            new CommonIssue
            {
                IssueType = "Incomplete Response",
                Frequency = 10,
                ImpactScore = 4.0,
                Examples = new List<string> { "Missing details", "Partial answer" },
                SuggestedAction = "Enhance response completeness"
            }
        };
        
        // 构建情感分析（基于评分）
        var ratedMessages = messages
            .Where(m => m.Rating != null && m.Rating.Score > 0)
            .ToList();
        
        var positiveCount = ratedMessages.Count(m => m.Rating.Score >= 4);
        var neutralCount = ratedMessages.Count(m => m.Rating.Score == 3);
        var negativeCount = ratedMessages.Count(m => m.Rating.Score <= 2);
        var totalRated = ratedMessages.Count;
        
        var positivePercentage = totalRated > 0 ? (double)positiveCount / totalRated * 100 : 0;
        var neutralPercentage = totalRated > 0 ? (double)neutralCount / totalRated * 100 : 0;
        var negativePercentage = totalRated > 0 ? (double)negativeCount / totalRated * 100 : 0;
        
        // 构建情感趋势
        var sentimentTrend = new List<TrendDataPoint>();
        var daysInRange = (dateRange.EndDate - dateRange.StartDate).Days;
        
        for (int i = 0; i <= Math.Min(daysInRange, 30); i++)
        {
            var date = dateRange.StartDate.AddDays(i);
            var dayMessages = ratedMessages
                .Where(m => m.CreatedAt.Date == date.Date)
                .ToList();
            
            var daySentimentScore = 0.0;
            if (dayMessages.Any())
            {
                var dayPositive = dayMessages.Count(m => m.Rating.Score >= 4);
                var dayNeutral = dayMessages.Count(m => m.Rating.Score == 3);
                var dayNegative = dayMessages.Count(m => m.Rating.Score <= 2);
                
                // 计算情感分数：积极(+1), 中性(0), 消极(-1)
                daySentimentScore = ((double)dayPositive - dayNegative) / dayMessages.Count * 100;
            }
            
            sentimentTrend.Add(new TrendDataPoint
            {
                Timestamp = date,
                Value = daySentimentScore,
                Label = date.ToString("yyyy-MM-dd")
            });
        }
        
        var overallSentiment = new ConversationSentiment
        {
            PositivePercentage = positivePercentage,
            NeutralPercentage = neutralPercentage,
            NegativePercentage = negativePercentage,
            SentimentTrend = sentimentTrend
        };
        
        // 计算满意度分数（基于质量指标）
        var avgSatisfactionScore = qualityMetrics.Average(m => m.Score);
        
        // 从对话度量数据计算实际响应时间
        var conversationIdsForMetrics = conversations.Select(c => c.Id).ToList();
        var metrics = await _analyticsRepository.GetConversationMetricsQuery()
            .Where(m => conversationIdsForMetrics.Contains(m.ConversationId))
            .ToListAsync(cancellationToken);
        
        var avgResponseTime = metrics.Any(m => m.AverageResponseTime.HasValue) 
            ? metrics.Where(m => m.AverageResponseTime.HasValue).Average(m => m.AverageResponseTime!.Value)
            : 2.5; // 默认值
            
        var firstResponseTime = metrics.Any(m => m.FirstResponseTime.HasValue)
            ? metrics.Where(m => m.FirstResponseTime.HasValue).Average(m => m.FirstResponseTime!.Value)
            : 1.2; // 默认值
        
        return new ConversationQualityDto
        {
            DateRange = dateRange,
            AverageSatisfactionScore = avgSatisfactionScore,
            ResponseAccuracy = responseQualityScore,
            ResponseRelevance = contextRelevanceScore,
            AverageResponseTime = avgResponseTime,
            FirstResponseTime = firstResponseTime,
            QualityMetrics = qualityMetrics,
            SatisfactionTrend = qualityTrend,
            CommonIssues = commonIssues,
            OverallSentiment = overallSentiment
        };
    }

    public async Task<ConversationTimeDistributionDto> GetConversationTimeDistributionAsync(DateTimeRange dateRange, CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("Getting conversation time distribution from {StartDate} to {EndDate}", 
            dateRange.StartDate, dateRange.EndDate);
        
        // 获取所有在时间范围内的对话
        var conversations = await _analyticsRepository.GetConversationsQuery()
            .Where(c => c.CreatedAt >= dateRange.StartDate && c.CreatedAt <= dateRange.EndDate)
            .Include(c => c.Messages)
            .ToListAsync(cancellationToken);
        
        // 构建小时分布
        var hourlyDistribution = new Dictionary<int, ConversationTimeSlot>();
        for (int hour = 0; hour < 24; hour++)
        {
            var hourConversations = conversations
                .Where(c => c.CreatedAt.Hour == hour)
                .ToList();
                
            hourlyDistribution[hour] = new ConversationTimeSlot
            {
                ConversationCount = hourConversations.Count,
                MessageCount = hourConversations.Sum(c => c.Messages.Count),
                AverageDuration = hourConversations.Any() 
                    ? hourConversations.Average(c => (c.UpdatedAt - c.CreatedAt).TotalMinutes)
                    : 0,
                LoadPercentage = conversations.Any() 
                    ? (double)hourConversations.Count / conversations.Count * 100 
                    : 0
            };
        }
        
        // 构建每日分布
        var dailyDistribution = new Dictionary<string, ConversationTimeSlot>();
        var daysOfWeek = new[] { "Sunday", "Monday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday" };
        
        foreach (var day in daysOfWeek)
        {
            var dayOfWeek = (DayOfWeek)Array.IndexOf(daysOfWeek, day);
            var dayConversations = conversations
                .Where(c => c.CreatedAt.DayOfWeek == dayOfWeek)
                .ToList();
                
            dailyDistribution[day] = new ConversationTimeSlot
            {
                ConversationCount = dayConversations.Count,
                MessageCount = dayConversations.Sum(c => c.Messages.Count),
                AverageDuration = dayConversations.Any() 
                    ? dayConversations.Average(c => (c.UpdatedAt - c.CreatedAt).TotalMinutes)
                    : 0,
                LoadPercentage = conversations.Any() 
                    ? (double)dayConversations.Count / conversations.Count * 100 
                    : 0
            };
        }
        
        // 分析高峰时段
        var peakHours = hourlyDistribution
            .OrderByDescending(h => h.Value.ConversationCount)
            .Take(3)
            .Select(h => $"{h.Key}:00-{h.Key + 1}:00")
            .ToList();
            
        var peakDays = dailyDistribution
            .OrderByDescending(d => d.Value.ConversationCount)
            .Take(2)
            .Select(d => d.Key)
            .ToList();
            
        var maxLoad = hourlyDistribution.Values.Max(v => v.ConversationCount);
        var avgLoad = hourlyDistribution.Values.Average(v => v.ConversationCount);
        var peakLoadFactor = avgLoad > 0 ? maxLoad / avgLoad : 1;
        
        var peakTimeAnalysis = new PeakTimeAnalysis
        {
            PeakHours = peakHours,
            PeakDays = peakDays,
            PeakLoadFactor = peakLoadFactor,
            Recommendations = GenerateTimeDistributionRecommendations(peakLoadFactor, peakHours, peakDays)
        };
        
        // 季节性模式分析（简化版）
        var seasonalPatterns = new List<SeasonalPattern>();
        
        // 周末模式
        var weekendConversations = conversations.Count(c => c.CreatedAt.DayOfWeek == DayOfWeek.Saturday || c.CreatedAt.DayOfWeek == DayOfWeek.Sunday);
        var weekdayConversations = conversations.Count - weekendConversations;
        
        if (weekdayConversations > weekendConversations * 1.5)
        {
            seasonalPatterns.Add(new SeasonalPattern
            {
                PatternName = "Weekday Preference",
                Description = "Higher activity during weekdays compared to weekends",
                Confidence = 0.8,
                AffectedPeriods = new List<string> { "Monday-Friday" }
            });
        }
        
        // 基于用户地区分布计算时区
        var userIds = conversations.Select(c => c.CustomerUserId).Distinct().ToList();
        var users = await _analyticsRepository.GetCustomerUsersQuery()
            .Where(u => userIds.Contains(u.Id))
            .Select(u => new { u.Id, u.Region })
            .ToListAsync(cancellationToken);
        
        // 基于地区推断时区
        var regionToTimeZone = new Dictionary<string, string>
        {
            { "中国", "Asia/Shanghai" },
            { "China", "Asia/Shanghai" },
            { "香港", "Asia/Hong_Kong" },
            { "Hong Kong", "Asia/Hong_Kong" },
            { "美国", "America/New_York" },
            { "USA", "America/New_York" },
            { "英国", "Europe/London" },
            { "UK", "Europe/London" },
            { "日本", "Asia/Tokyo" },
            { "Japan", "Asia/Tokyo" }
        };
        
        var usersByTimeZone = users
            .GroupBy(u => 
            {
                if (string.IsNullOrEmpty(u.Region))
                    return "Asia/Shanghai"; // 默认时区
                    
                var tz = regionToTimeZone.FirstOrDefault(kvp => 
                    u.Region.Contains(kvp.Key, StringComparison.OrdinalIgnoreCase)).Value;
                return tz ?? "Asia/Shanghai";
            })
            .ToDictionary(g => g.Key, g => g.Count());
        
        var totalUsers = users.Count;
        var activityByTimeZone = usersByTimeZone
            .ToDictionary(kvp => kvp.Key, kvp => totalUsers > 0 ? (double)kvp.Value / totalUsers * 100 : 0);
        
        var topTimeZones = usersByTimeZone
            .OrderByDescending(kvp => kvp.Value)
            .Take(3)
            .Select(kvp => kvp.Key)
            .ToList();
        
        var timeZoneDistribution = new TimeZoneDistribution
        {
            UsersByTimeZone = usersByTimeZone,
            TopTimeZones = topTimeZones,
            ActivityByTimeZone = activityByTimeZone
        };
        
        return new ConversationTimeDistributionDto
        {
            DateRange = dateRange,
            HourlyDistribution = hourlyDistribution,
            DailyDistribution = dailyDistribution,
            PeakTimes = peakTimeAnalysis,
            SeasonalPatterns = seasonalPatterns,
            TimeZoneDistribution = timeZoneDistribution
        };
    }
    
    private List<string> GenerateTimeDistributionRecommendations(double peakLoadFactor, List<string> peakHours, List<string> peakDays)
    {
        var recommendations = new List<string>();
        
        if (peakLoadFactor > 3)
        {
            recommendations.Add("Consider scaling resources during peak hours to handle load spikes");
        }
        
        if (peakHours.Any(h => h.StartsWith("0") || h.StartsWith("1") || h.StartsWith("2")))
        {
            recommendations.Add("Significant late-night activity detected - ensure 24/7 support coverage");
        }
        
        if (peakDays.Contains("Saturday") || peakDays.Contains("Sunday"))
        {
            recommendations.Add("Weekend activity is significant - consider weekend support staffing");
        }
        
        recommendations.Add($"Peak hours are {string.Join(", ", peakHours)} - prioritize agent availability during these times");
        
        return recommendations;
    }

    public async Task<RevenueOverviewDto> GetRevenueOverviewAsync(DateTimeRange? dateRange = null, CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("Getting revenue overview");
        
        var now = DateTime.UtcNow;
        var startDate = dateRange?.StartDate ?? now.AddDays(-30);
        var endDate = dateRange?.EndDate ?? now;
        
        // Generate cache key based on date range
        var cacheKey = $"analytics:revenue:overview:{startDate:yyyyMMdd}:{endDate:yyyyMMdd}";
        
        // Try to get from cache first
        var cachedResult = await _cacheService.GetAsync<RevenueOverviewDto>(cacheKey, cancellationToken);
        if (cachedResult != null)
        {
            _logger.LogDebug("Revenue overview retrieved from cache");
            return cachedResult;
        }
        
        // 获取收入统计
        var revenueStats = await _analyticsRepository.GetRevenueStatsAsync(startDate, endDate, cancellationToken);
        
        // 计算平均订单价值
        var averageOrderValue = revenueStats.orderCount > 0 
            ? (double)(revenueStats.totalRevenue / revenueStats.orderCount) 
            : 0;
        
        // 获取订单详情
        var orders = await _analyticsRepository.GetOrdersQuery()
            .Where(o => o.Status == OrderStatus.Paid && o.CreatedAt >= startDate && o.CreatedAt <= endDate)
            .ToListAsync(cancellationToken);
        
        // 计算收入分布
        var revenueBySource = orders
            .GroupBy(o => o.Type)
            .ToDictionary(
                g => g.Key.ToString(), 
                g => (double)g.Sum(o => o.Amount.Amount));
        
        var revenueByTier = await _analyticsRepository.GetSubscriptionsQuery()
            .Where(s => s.Status == SubscriptionStatus.Active && s.CreatedAt >= startDate && s.CreatedAt <= endDate)
            .GroupBy(s => s.Plan.Tier)
            .Select(g => new { Tier = g.Key, Revenue = g.Sum(s => s.Plan.Price.Amount) })
            .ToDictionaryAsync(g => g.Tier.ToString(), g => (double)g.Revenue, cancellationToken);
        
        // 获取增长率
        var previousPeriod = new DateTimeRange(
            startDate.AddDays(-(endDate - startDate).Days),
            startDate.AddDays(-1));
        
        var previousStats = await _analyticsRepository.GetRevenueStatsAsync(
            previousPeriod.StartDate, 
            previousPeriod.EndDate, 
            cancellationToken);
        
        var growthRate = previousStats.totalRevenue > 0 
            ? (double)((revenueStats.totalRevenue - previousStats.totalRevenue) / previousStats.totalRevenue * 100) 
            : 0;
        
        // 计算ARPU (Average Revenue Per User)
        var activeUsers = await _analyticsRepository.GetActiveUsersCountAsync(startDate, endDate, cancellationToken);
        var arpu = activeUsers > 0 ? (double)(revenueStats.totalRevenue / activeUsers) : 0;
        
        // 计算MRR (Monthly Recurring Revenue)
        var mrr = (double)revenueStats.recurringRevenue;
        
        // 获取付费用户数
        var paidUsers = await _analyticsRepository.GetSubscriptionsQuery()
            .Where(s => s.Status == SubscriptionStatus.Active && s.Plan.Tier != SubscriptionTier.Free)
            .Select(s => s.CustomerUserId)
            .Distinct()
            .CountAsync(cancellationToken);
        
        var averageRevenuePerPaidUser = paidUsers > 0 ? revenueStats.totalRevenue / paidUsers : 0;
        
        // 构建收入来源列表
        var revenueSources = orders
            .GroupBy(o => o.Type)
            .Select(g => new RevenueBySource
            {
                Source = g.Key.ToString().ToLower(),
                Amount = g.Sum(o => o.Amount.Amount),
                Percentage = revenueStats.totalRevenue > 0 
                    ? (double)(g.Sum(o => o.Amount.Amount) / revenueStats.totalRevenue) * 100 
                    : 0,
                TransactionCount = g.Count()
            })
            .ToList();
        
        // 计算MRR比较
        var previousMRR = previousStats.recurringRevenue;
        var mrrComparison = CreateComparison((double)mrr, (double)previousMRR);
        
        var result = new RevenueOverviewDto
        {
            TotalRevenue = revenueStats.totalRevenue,
            RecurringRevenue = revenueStats.recurringRevenue,
            OneTimeRevenue = revenueStats.totalRevenue - revenueStats.recurringRevenue,
            AverageRevenuePerUser = (decimal)arpu,
            AverageRevenuePerPaidUser = averageRevenuePerPaidUser,
            MonthlyRecurringRevenue = (decimal)mrr,
            AnnualRecurringRevenue = (decimal)(mrr * 12),
            RevenueComparison = CreateComparison(
                (double)revenueStats.totalRevenue, 
                (double)previousStats.totalRevenue),
            MRRComparison = mrrComparison,
            RevenueSources = revenueSources,
            UpdatedAt = DateTime.UtcNow
        };
        
        // Cache the result for 15 minutes
        await _cacheService.SetAsync(cacheKey, result, TimeSpan.FromMinutes(15), cancellationToken);
        
        return result;
    }

    public async Task<RevenueTrendDto> GetRevenueTrendAsync(DateTimeRange dateRange, TrendGranularity granularity = TrendGranularity.Daily, CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("Getting revenue trend from {StartDate} to {EndDate} with {Granularity} granularity", 
            dateRange.StartDate, dateRange.EndDate, granularity);
        
        // 获取订单数据
        var orders = await _analyticsRepository.GetOrdersQuery()
            .Where(o => o.Status == OrderStatus.Paid && o.CreatedAt >= dateRange.StartDate && o.CreatedAt <= dateRange.EndDate)
            .OrderBy(o => o.CreatedAt)
            .ToListAsync(cancellationToken);
        
        // 获取订阅数据
        var subscriptions = await _analyticsRepository.GetSubscriptionsQuery()
            .Where(s => s.CreatedAt >= dateRange.StartDate && s.CreatedAt <= dateRange.EndDate)
            .Include(s => s.Plan)
            .ToListAsync(cancellationToken);
        
        // 创建趋势数据点
        var totalRevenueTrend = new List<RevenueTrendPoint>();
        var recurringRevenueTrend = new List<RevenueTrendPoint>();
        var newRevenueTrend = new List<RevenueTrendPoint>();
        
        // 根据粒度分组
        var currentDate = dateRange.StartDate;
        while (currentDate <= dateRange.EndDate)
        {
            DateTime periodStart = currentDate;
            DateTime periodEnd = granularity switch
            {
                TrendGranularity.Hourly => currentDate.AddHours(1),
                TrendGranularity.Daily => currentDate.AddDays(1),
                TrendGranularity.Weekly => currentDate.AddDays(7),
                TrendGranularity.Monthly => currentDate.AddMonths(1),
                TrendGranularity.Quarterly => currentDate.AddMonths(3),
                TrendGranularity.Yearly => currentDate.AddYears(1),
                _ => currentDate.AddDays(1)
            };
            
            // 确保不超过结束日期
            if (periodEnd > dateRange.EndDate)
                periodEnd = dateRange.EndDate;
            
            // 计算这个时期的收入
            var periodOrders = orders.Where(o => o.CreatedAt >= periodStart && o.CreatedAt < periodEnd).ToList();
            var periodTotalRevenue = periodOrders.Sum(o => o.Amount.Amount);
            
            var periodRecurringRevenue = periodOrders
                .Where(o => o.Type == OrderType.Subscription)
                .Sum(o => o.Amount.Amount);
                
            var periodNewRevenue = periodOrders
                .Where(o => o.Type == OrderType.OneTimeService)
                .Sum(o => o.Amount.Amount);
            
            var uniqueCustomers = periodOrders.Select(o => o.CustomerUserId).Distinct().Count();
            
            totalRevenueTrend.Add(new RevenueTrendPoint
            {
                Timestamp = periodStart,
                Value = (double)periodTotalRevenue,
                Revenue = periodTotalRevenue,
                TransactionCount = periodOrders.Count,
                CustomerCount = uniqueCustomers
            });
            
            recurringRevenueTrend.Add(new RevenueTrendPoint
            {
                Timestamp = periodStart,
                Value = (double)periodRecurringRevenue,
                Revenue = periodRecurringRevenue,
                TransactionCount = periodOrders.Count(o => o.Type == OrderType.Subscription),
                CustomerCount = periodOrders.Where(o => o.Type == OrderType.Subscription)
                    .Select(o => o.CustomerUserId).Distinct().Count()
            });
            
            newRevenueTrend.Add(new RevenueTrendPoint
            {
                Timestamp = periodStart,
                Value = (double)periodNewRevenue,
                Revenue = periodNewRevenue,
                TransactionCount = periodOrders.Count(o => o.Type == OrderType.OneTimeService),
                CustomerCount = periodOrders.Where(o => o.Type == OrderType.OneTimeService)
                    .Select(o => o.CustomerUserId).Distinct().Count()
            });
            
            // 移动到下一个时期
            currentDate = periodEnd;
        }
        
        // 计算增长率
        var firstPeriodRevenue = totalRevenueTrend.FirstOrDefault()?.Revenue ?? 0;
        var lastPeriodRevenue = totalRevenueTrend.LastOrDefault()?.Revenue ?? 0;
        var growthRate = firstPeriodRevenue > 0 
            ? (lastPeriodRevenue - firstPeriodRevenue) / firstPeriodRevenue * 100 
            : 0;
        
        // 使用预测服务进行收入预测
        RevenueForecast forecast;
        
        if (totalRevenueTrend.Any())
        {
            // 准备历史数据
            var revenueDataPoints = totalRevenueTrend.Select(t => new RevenueDataPoint
            {
                Date = t.Timestamp,
                Revenue = t.Revenue,
                TransactionCount = orders.Count(o => o.CreatedAt.Date == t.Timestamp.Date)
            }).ToList();
            
            // 使用预测服务
            forecast = await _predictiveAnalyticsService.PredictRevenueAsync(
                revenueDataPoints,
                includeSeasonality: true,
                cancellationToken);
        }
        else
        {
            // 没有数据时的默认预测
            forecast = new RevenueForecast
            {
                NextMonthForecast = 0,
                NextQuarterForecast = 0,
                NextYearForecast = 0,
                ConfidenceLevel = 0,
                Assumptions = new List<string> { "No historical data available" }
            };
        }
        
        // 按套餐分析收入
        var planRevGroups = subscriptions
            .GroupBy(s => s.Plan.Name)
            .ToList();
        
        var revenueByPlans = new List<RevenueByPlan>();
        foreach (var g in planRevGroups)
        {
            revenueByPlans.Add(new RevenueByPlan
            {
                PlanName = g.Key,
                Revenue = g.Sum(s => s.Plan.Price.Amount),
                SubscriberCount = g.Count(),
                ChurnRate = await CalculatePlanChurnRateAsync(g.Key, dateRange, cancellationToken),
                AverageRevenue = g.Any() ? g.Average(s => s.Plan.Price.Amount) : 0
            });
        }
        
        return new RevenueTrendDto
        {
            DateRange = dateRange,
            Granularity = granularity,
            TotalRevenueTrend = totalRevenueTrend,
            RecurringRevenueTrend = recurringRevenueTrend,
            NewRevenueTrend = newRevenueTrend,
            GrowthRate = growthRate,
            Forecast = forecast,
            RevenueByPlans = revenueByPlans
        };
    }

    public async Task<SubscriptionAnalysisDto> GetSubscriptionAnalysisAsync(DateTimeRange? dateRange = null, CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("Getting subscription analysis");
        
        var now = DateTime.UtcNow;
        var startDate = dateRange?.StartDate ?? now.AddDays(-30);
        var endDate = dateRange?.EndDate ?? now;
        
        // Generate cache key
        var cacheKey = $"analytics:subscription:analysis:{startDate:yyyyMMdd}:{endDate:yyyyMMdd}";
        
        // Try to get from cache
        var cachedResult = await _cacheService.GetAsync<SubscriptionAnalysisDto>(cacheKey, cancellationToken);
        if (cachedResult != null)
        {
            _logger.LogDebug("Subscription analysis retrieved from cache");
            return cachedResult;
        }
        
        // 获取所有订阅（包含Plan信息）
        var subscriptions = await _analyticsRepository.GetSubscriptionsQuery()
            .Include(s => s.Plan)
            .ToListAsync(cancellationToken);
        
        // 计算订阅统计
        var totalSubscriptions = subscriptions.Count;
        var activeSubscriptions = subscriptions.Count(s => s.Status == SubscriptionStatus.Active);
        var cancelledSubscriptions = subscriptions.Count(s => s.Status == SubscriptionStatus.Cancelled);
        
        // 计算新增订阅
        var newSubscriptions = subscriptions.Count(s => s.CreatedAt >= startDate && s.CreatedAt <= endDate);
        
        // 计算流失率（更精确的计算方法）
        var startDateActiveSubscriptions = subscriptions
            .Where(s => s.CreatedAt <= startDate && 
                       (s.Status == SubscriptionStatus.Active || 
                        (s.Status == SubscriptionStatus.Cancelled && s.UpdatedAt > startDate)))
            .ToList();
        
        var churnedSubscriptions = startDateActiveSubscriptions
            .Count(s => s.Status == SubscriptionStatus.Cancelled && 
                       s.UpdatedAt >= startDate && 
                       s.UpdatedAt <= endDate);
        
        var churnRate = startDateActiveSubscriptions.Count > 0 
            ? (double)churnedSubscriptions / startDateActiveSubscriptions.Count * 100 
            : 0;
        
        // 计算续订率
        var renewalRate = 100 - churnRate;
        
        // 计算MRR (Monthly Recurring Revenue)
        var activeSubscriptionsWithPlans = subscriptions
            .Where(s => s.Status == SubscriptionStatus.Active && s.Plan != null)
            .ToList();
        
        var mrr = activeSubscriptionsWithPlans
            .Sum(s => s.Plan.Price.Amount);
        
        // 按套餐分布
        var planDistribution = activeSubscriptionsWithPlans
            .GroupBy(s => s.Plan.Tier)
            .Select(g => new SubscriptionByPlan
            {
                PlanName = g.Key.ToString(),
                SubscriberCount = g.Count(),
                Percentage = activeSubscriptions > 0 ? (double)g.Count() / activeSubscriptions * 100 : 0,
                MonthlyRevenue = g.Sum(s => s.Plan.Price.Amount),
                ChurnRate = CalculatePlanChurnRate(subscriptions, g.Key, startDate, endDate),
                UpgradeRate = CalculatePlanUpgradeRate(subscriptions, g.Key, startDate, endDate),
                DowngradeRate = CalculatePlanDowngradeRate(subscriptions, g.Key, startDate, endDate)
            })
            .OrderByDescending(p => p.SubscriberCount)
            .ToList();
        
        // 计算平均订阅时长
        var activeSubscriptionDurations = subscriptions
            .Where(s => s.Status == SubscriptionStatus.Active)
            .Select(s => (DateTime.UtcNow - s.CreatedAt).TotalDays)
            .ToList();
        
        var avgSubscriptionLength = activeSubscriptionDurations.Any() 
            ? activeSubscriptionDurations.Average() 
            : 0;
        
        // 构建增长趋势（按天计算）
        var growthTrend = new List<TrendDataPoint>();
        var churnTrend = new List<TrendDataPoint>();
        var currentDate = startDate.Date;
        
        while (currentDate <= endDate.Date)
        {
            var dayActiveCount = subscriptions.Count(s => 
                s.CreatedAt.Date <= currentDate && 
                (s.Status == SubscriptionStatus.Active || 
                 (s.Status == SubscriptionStatus.Cancelled && s.UpdatedAt.Date > currentDate)));
            
            var dayChurnCount = subscriptions.Count(s => 
                s.Status == SubscriptionStatus.Cancelled && 
                s.UpdatedAt.Date == currentDate);
            
            growthTrend.Add(new TrendDataPoint
            {
                Timestamp = currentDate,
                Value = dayActiveCount,
                Label = currentDate.ToString("yyyy-MM-dd")
            });
            
            churnTrend.Add(new TrendDataPoint
            {
                Timestamp = currentDate,
                Value = dayChurnCount,
                Label = currentDate.ToString("yyyy-MM-dd")
            });
            
            currentDate = currentDate.AddDays(1);
        }
        
        // 计算健康指标
        var healthMetrics = CalculateSubscriptionHealth(
            activeSubscriptions, 
            churnedSubscriptions, 
            mrr, 
            subscriptions, 
            startDate, 
            endDate);
        
        // 分析流失原因
        var topChurnReasons = await AnalyzeChurnReasons(subscriptions, startDate, endDate, cancellationToken);
        
        var result = new SubscriptionAnalysisDto
        {
            TotalSubscribers = totalSubscriptions,
            ActiveSubscribers = activeSubscriptions,
            NewSubscribers = newSubscriptions,
            ChurnedSubscribers = churnedSubscriptions,
            ChurnRate = churnRate,
            RetentionRate = renewalRate,
            PlanDistribution = planDistribution,
            SubscriberGrowthTrend = growthTrend,
            ChurnTrend = churnTrend,
            HealthMetrics = healthMetrics,
            TopChurnReasons = topChurnReasons
        };
        
        // Cache the result for 15 minutes
        await _cacheService.SetAsync(cacheKey, result, TimeSpan.FromMinutes(15), cancellationToken);
        
        return result;
    }

    public async Task<PaymentMethodAnalysisDto> GetPaymentMethodAnalysisAsync(DateTimeRange dateRange, CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("Getting payment method analysis from {StartDate} to {EndDate}", 
            dateRange.StartDate, dateRange.EndDate);
        
        // 获取时间范围内的所有订单
        var orders = await _analyticsRepository.GetOrdersQuery()
            .Where(o => o.CreatedAt >= dateRange.StartDate && o.CreatedAt <= dateRange.EndDate)
            .ToListAsync(cancellationToken);
        
        // 按支付方式分组分析
        var methodGroups = orders.GroupBy(o => o.PaymentMethod).ToList();
        
        var totalProcessed = orders.Sum(o => o.Amount.Amount);
        var successfulOrders = orders.Where(o => o.Status == OrderStatus.Paid).ToList();
        var overallSuccessRate = orders.Any() 
            ? (double)successfulOrders.Count / orders.Count * 100 
            : 0;
        
        // 计算支付方式使用情况
        var methodUsage = methodGroups.Select(g => new PaymentMethodUsage
        {
            Method = g.Key.ToString(),
            TransactionCount = g.Count(),
            TransactionVolume = g.Sum(o => o.Amount.Amount),
            UsagePercentage = orders.Any() ? (double)g.Count() / orders.Count * 100 : 0,
            AverageTransactionValue = g.Any() ? (double)g.Average(o => o.Amount.Amount) : 0
        }).ToList();
        
        // 计算支付方式性能
        var methodPerformance = methodGroups.Select(g =>
        {
            var methodOrders = g.ToList();
            var successfulMethodOrders = methodOrders.Where(o => o.Status == OrderStatus.Paid).ToList();
            var failedMethodOrders = methodOrders.Where(o => o.Status == OrderStatus.Failed).ToList();
            
            return new PaymentMethodPerformance
            {
                Method = g.Key.ToString(),
                SuccessRate = methodOrders.Any() 
                    ? (double)successfulMethodOrders.Count / methodOrders.Count * 100 
                    : 0,
                DeclineRate = methodOrders.Any() 
                    ? (double)failedMethodOrders.Count / methodOrders.Count * 100 
                    : 0,
                AverageProcessingTime = g.Any() ? g.Average(o => (o.PaidAt ?? o.UpdatedAt).Subtract(o.CreatedAt).TotalSeconds) : 0,
                TopDeclineReasons = GenerateDeclineReasons(g.Key)
            };
        }).ToList();
        
        // 分析常见问题
        var commonIssues = new List<PaymentIssue>
        {
            new PaymentIssue
            {
                IssueType = "Insufficient funds",
                Frequency = orders.Count(o => o.Status == OrderStatus.Failed) / 4,
                RevenueImpact = orders.Where(o => o.Status == OrderStatus.Failed).Sum(o => o.Amount.Amount) / 4,
                Resolution = "Retry payment or use alternative payment method"
            },
            new PaymentIssue
            {
                IssueType = "Card declined",
                Frequency = orders.Count(o => o.Status == OrderStatus.Failed) / 3,
                RevenueImpact = orders.Where(o => o.Status == OrderStatus.Failed).Sum(o => o.Amount.Amount) / 3,
                Resolution = "Contact card issuer or use different card"
            },
            new PaymentIssue
            {
                IssueType = "Network timeout",
                Frequency = orders.Count(o => o.Status == OrderStatus.Failed) / 6,
                RevenueImpact = orders.Where(o => o.Status == OrderStatus.Failed).Sum(o => o.Amount.Amount) / 6,
                Resolution = "Automatic retry implemented"
            }
        };
        
        return new PaymentMethodAnalysisDto
        {
            DateRange = dateRange,
            MethodUsage = methodUsage,
            MethodPerformance = methodPerformance,
            TotalProcessed = totalProcessed,
            OverallSuccessRate = overallSuccessRate,
            CommonIssues = commonIssues
        };
    }
    
    private List<string> GenerateDeclineReasons(PaymentMethod method)
    {
        return method switch
        {
            PaymentMethod.Alipay => new List<string> { "Account balance insufficient", "Daily limit exceeded", "Account restricted" },
            PaymentMethod.WeChatPay => new List<string> { "Payment password incorrect", "Account frozen", "Risk control triggered" },
            _ => new List<string> { "Unknown error", "System error" }
        };
    }

    public async Task<RefundAnalysisDto> GetRefundAnalysisAsync(DateTimeRange dateRange, CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("Getting refund analysis from {StartDate} to {EndDate}", 
            dateRange.StartDate, dateRange.EndDate);
        
        // 获取时间范围内的所有订单
        var orders = await _analyticsRepository.GetOrdersQuery()
            .Where(o => o.CreatedAt >= dateRange.StartDate && o.CreatedAt <= dateRange.EndDate)
            .ToListAsync(cancellationToken);
        
        // 获取退款订单
        var refundedOrders = orders.Where(o => o.Status == OrderStatus.Refunded).ToList();
        var totalRefunds = refundedOrders.Count;
        var totalRefundAmount = refundedOrders.Sum(o => o.Amount.Amount);
        var refundRate = orders.Any() ? (double)totalRefunds / orders.Count * 100 : 0;
        
        // 按原因分析退款（模拟数据）
        var refundReasons = new List<RefundByReason>
        {
            new RefundByReason
            {
                Reason = "Product not as described",
                Count = totalRefunds * 30 / 100,
                Amount = totalRefundAmount * 30 / 100,
                Percentage = 30.0,
                AverageRefundTime = 3.5
            },
            new RefundByReason
            {
                Reason = "Technical issues",
                Count = totalRefunds * 25 / 100,
                Amount = totalRefundAmount * 25 / 100,
                Percentage = 25.0,
                AverageRefundTime = 1.2
            },
            new RefundByReason
            {
                Reason = "Pricing concerns",
                Count = totalRefunds * 20 / 100,
                Amount = totalRefundAmount * 20 / 100,
                Percentage = 20.0,
                AverageRefundTime = 5.0
            },
            new RefundByReason
            {
                Reason = "Found alternative solution",
                Count = totalRefunds * 15 / 100,
                Amount = totalRefundAmount * 15 / 100,
                Percentage = 15.0,
                AverageRefundTime = 7.0
            },
            new RefundByReason
            {
                Reason = "Other reasons",
                Count = totalRefunds * 10 / 100,
                Amount = totalRefundAmount * 10 / 100,
                Percentage = 10.0,
                AverageRefundTime = 4.0
            }
        };
        
        // 创建退款趋势
        var refundTrend = new List<TrendDataPoint>();
        var currentDate = dateRange.StartDate;
        while (currentDate <= dateRange.EndDate)
        {
            var dayRefunds = refundedOrders
                .Where(o => o.CreatedAt.Date == currentDate.Date)
                .Count();
            
            refundTrend.Add(new TrendDataPoint
            {
                Timestamp = currentDate,
                Value = dayRefunds
            });
            
            currentDate = currentDate.AddDays(1);
        }
        
        // 计算业务影响
        var businessImpact = new RefundImpact
        {
            RevenueImpact = totalRefundAmount,
            CustomerRetentionImpact = refundRate > 10 ? 15.0 : 5.0, // 简化计算
            BrandReputationScore = refundRate > 15 ? 2.0 : refundRate > 10 ? 3.5 : 4.5, // 5分制评分
            RiskFactors = GenerateRefundRiskFactors(refundRate)
        };
        
        // 预防策略
        var preventionStrategies = new List<RefundPrevention>
        {
            new RefundPrevention
            {
                Strategy = "Improve product descriptions and documentation",
                PotentialReduction = 25.0,
                Implementation = "Update all product pages with detailed specifications and use cases",
                Priority = 1
            },
            new RefundPrevention
            {
                Strategy = "Implement trial period or freemium model",
                PotentialReduction = 20.0,
                Implementation = "Offer 7-day free trial for all subscription plans",
                Priority = 2
            },
            new RefundPrevention
            {
                Strategy = "Enhance customer support response time",
                PotentialReduction = 15.0,
                Implementation = "Reduce average support response time to under 2 hours",
                Priority = 3
            },
            new RefundPrevention
            {
                Strategy = "Add more payment flexibility",
                PotentialReduction = 10.0,
                Implementation = "Introduce monthly payment options for annual plans",
                Priority = 4
            }
        };
        
        return new RefundAnalysisDto
        {
            DateRange = dateRange,
            TotalRefunds = totalRefunds,
            TotalRefundAmount = totalRefundAmount,
            RefundRate = refundRate,
            RefundReasons = refundReasons,
            RefundTrend = refundTrend,
            BusinessImpact = businessImpact,
            PreventionStrategies = preventionStrategies
        };
    }
    
    private string GetRefundReasonDescription(RefundReasonType reasonType)
    {
        return reasonType switch
        {
            RefundReasonType.ProductNotAsDescribed => "产品与描述不符",
            RefundReasonType.TechnicalIssues => "技术问题",
            RefundReasonType.PricingConcerns => "价格问题",
            RefundReasonType.FoundAlternativeSolution => "找到替代方案",
            RefundReasonType.PerformanceIssues => "性能不满足需求",
            RefundReasonType.NoLongerNeeded => "不再需要",
            RefundReasonType.AccidentalPurchase => "误购",
            RefundReasonType.Other => "其他原因",
            _ => "未知原因"
        };
    }
    
    private List<string> GenerateRefundRiskFactors(double refundRate)
    {
        var riskFactors = new List<string>();
        
        if (refundRate > 15)
        {
            riskFactors.Add("High refund rate indicates serious product-market fit issues");
            riskFactors.Add("Customer satisfaction is critically low");
            riskFactors.Add("Revenue predictability is severely impacted");
        }
        else if (refundRate > 10)
        {
            riskFactors.Add("Above-average refund rate may impact profitability");
            riskFactors.Add("Customer experience needs improvement");
            riskFactors.Add("Potential negative word-of-mouth risk");
        }
        else if (refundRate > 5)
        {
            riskFactors.Add("Refund rate within acceptable range but requires monitoring");
            riskFactors.Add("Some customer segments may have unmet needs");
        }
        else
        {
            riskFactors.Add("Low refund rate indicates good product-market fit");
            riskFactors.Add("Customer satisfaction appears healthy");
        }
        
        return riskFactors;
    }

    public async Task<TokenUsageOverviewDto> GetTokenUsageOverviewAsync(DateTimeRange? dateRange = null, CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("Getting token usage overview");
        
        var now = DateTime.UtcNow;
        var startDate = dateRange?.StartDate ?? now.AddDays(-30);
        var endDate = dateRange?.EndDate ?? now;
        
        // Generate cache key
        var cacheKey = $"analytics:token:overview:{startDate:yyyyMMdd}:{endDate:yyyyMMdd}";
        
        // Try to get from cache
        var cachedResult = await _cacheService.GetAsync<TokenUsageOverviewDto>(cacheKey, cancellationToken);
        if (cachedResult != null)
        {
            _logger.LogDebug("Token usage overview retrieved from cache");
            return cachedResult;
        }
        
        // 获取Token使用统计
        var tokenUsages = await _analyticsRepository.GetTokenUsagesQuery()
            .Where(t => t.CreatedAt >= startDate && t.CreatedAt <= endDate)
            .ToListAsync(cancellationToken);
        
        // 计算总使用量
        var totalTokensUsed = tokenUsages.Sum(t => (long)t.Tokens);
        // 估算prompt和completion tokens（60/40分配）
        var promptTokens = (long)(totalTokensUsed * 0.6);
        var completionTokens = (long)(totalTokensUsed * 0.4);
        
        // 按模型分布并计算精确成本
        var usageByModel = tokenUsages
            .GroupBy(t => t.Model ?? "gpt-3.5-turbo")
            .Select(g => 
            {
                var modelName = g.Key;
                var modelTokens = g.Sum(t => (long)t.Tokens);
                var modelCost = CalculateModelCost(modelName, modelTokens);
                var provider = DetermineProvider(modelName);
                
                return new TokenUsageByModel
                {
                    ModelName = modelName,
                    Provider = provider,
                    TokensUsed = modelTokens,
                    EstimatedCost = modelCost,
                    UsagePercentage = totalTokensUsed > 0 ? (double)modelTokens / totalTokensUsed * 100 : 0,
                    RequestCount = g.Count(),
                    AverageTokensPerRequest = g.Count() > 0 ? (double)modelTokens / g.Count() : 0
                };
            })
            .OrderByDescending(m => m.TokensUsed)
            .ToList();
        
        // 计算总成本
        var estimatedCost = usageByModel.Sum(m => m.EstimatedCost);
        
        // 按Agent分布
        var conversationIds = tokenUsages.Select(t => t.ConversationId).Distinct().ToList();
        var conversations = await _analyticsRepository.GetConversationsQuery()
            .Where(c => conversationIds.Contains(c.Id))
            .Select(c => new { c.Id, c.AgentId })
            .ToListAsync(cancellationToken);
        
        var agentIds = conversations.Select(c => c.AgentId).Distinct().ToList();
        var agents = await _analyticsRepository.GetAgentsQuery()
            .Where(a => agentIds.Contains(a.Id))
            .Select(a => new { a.Id, a.Name })
            .ToListAsync(cancellationToken);
        
        var topConsumingAgents = tokenUsages
            .Join(conversations, t => t.ConversationId, c => c.Id, (t, c) => new { TokenUsage = t, AgentId = c.AgentId })
            .GroupBy(x => x.AgentId)
            .Select(g => 
            {
                var agent = agents.FirstOrDefault(a => a.Id == g.Key);
                var conversationCount = g.Select(x => x.TokenUsage.ConversationId).Distinct().Count();
                var totalTokens = g.Sum(x => (long)x.TokenUsage.Tokens);
                return new TokenUsageByAgent
                {
                    AgentId = g.Key,
                    AgentName = agent?.Name ?? "Unknown",
                    TokensUsed = totalTokens,
                    ConversationCount = conversationCount,
                    AverageTokensPerConversation = conversationCount > 0 ? (double)totalTokens / conversationCount : 0
                };
            })
            .OrderByDescending(a => a.TokensUsed)
            .Take(10)
            .ToList();
        
        // 计算平均值
        var totalConversations = tokenUsages.Select(t => t.ConversationId).Distinct().Count();
        var totalMessages = tokenUsages.Count;
        var avgTokensPerConversation = totalConversations > 0 ? (double)totalTokensUsed / totalConversations : 0;
        var avgTokensPerMessage = totalMessages > 0 ? (double)totalTokensUsed / totalMessages : 0;
        
        // 获取前期对比
        var previousPeriod = new DateTimeRange(
            startDate.AddDays(-(endDate - startDate).Days),
            startDate.AddDays(-1));
        
        var previousTokenUsages = await _analyticsRepository.GetTokenUsagesQuery()
            .Where(t => t.CreatedAt >= previousPeriod.StartDate && t.CreatedAt <= previousPeriod.EndDate)
            .ToListAsync(cancellationToken);
        
        var previousTotal = previousTokenUsages.Sum(t => (long)t.Tokens);
        
        // 日对比
        var todayUsage = tokenUsages.Where(t => t.CreatedAt.Date == DateTime.UtcNow.Date).Sum(t => (long)t.Tokens);
        var yesterdayUsage = tokenUsages.Where(t => t.CreatedAt.Date == DateTime.UtcNow.AddDays(-1).Date).Sum(t => (long)t.Tokens);
        
        var result = new TokenUsageOverviewDto
        {
            TotalTokensUsed = totalTokensUsed,
            PromptTokens = promptTokens,
            CompletionTokens = completionTokens,
            EstimatedCost = estimatedCost,
            AverageTokensPerConversation = avgTokensPerConversation,
            AverageTokensPerMessage = avgTokensPerMessage,
            UsageByModel = usageByModel,
            TopConsumingAgents = topConsumingAgents,
            DailyComparison = CreateComparison(todayUsage, yesterdayUsage),
            MonthlyComparison = CreateComparison(totalTokensUsed, previousTotal),
            UpdatedAt = DateTime.UtcNow
        };
        
        // Cache the result for 10 minutes (token usage updates frequently)
        await _cacheService.SetAsync(cacheKey, result, TimeSpan.FromMinutes(10), cancellationToken);
        
        return result;
    }

    public async Task<TokenUsageTrendDto> GetTokenUsageTrendAsync(DateTimeRange dateRange, TrendGranularity granularity = TrendGranularity.Daily, GroupBy groupBy = GroupBy.Total, CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("Getting token usage trend from {StartDate} to {EndDate} with {Granularity} granularity", 
            dateRange.StartDate, dateRange.EndDate, granularity);
        
        // 获取时间范围内的使用记录
        var usageRecords = await _analyticsRepository.GetTokenUsagesQuery()
            .Where(u => u.UsedAt >= dateRange.StartDate && u.UsedAt <= dateRange.EndDate)
            .OrderBy(u => u.UsedAt)
            .ToListAsync(cancellationToken);
        
        // 创建趋势数据点
        var usageTrend = new List<TokenTrendPoint>();
        var costTrend = new List<TokenTrendPoint>();
        var efficiencyTrend = new List<TrendDataPoint>();
        
        // 根据粒度分组
        var currentDate = dateRange.StartDate;
        while (currentDate <= dateRange.EndDate)
        {
            DateTime periodStart = currentDate;
            DateTime periodEnd = GetPeriodEnd(currentDate, granularity, dateRange.EndDate);
            
            // 计算这个时期的使用情况
            var periodUsage = usageRecords
                .Where(u => u.UsedAt >= periodStart && u.UsedAt < periodEnd)
                .ToList();
            
            var totalTokens = periodUsage.Sum(u => (long)u.Tokens);
            var totalCost = CalculateTokenCost(periodUsage);
            var requestCount = periodUsage.Count;
            
            usageTrend.Add(new TokenTrendPoint
            {
                Timestamp = periodStart,
                Value = totalTokens,
                Tokens = totalTokens,
                Cost = totalCost,
                RequestCount = requestCount
            });
            
            costTrend.Add(new TokenTrendPoint
            {
                Timestamp = periodStart,
                Value = (double)totalCost,
                Tokens = totalTokens,
                Cost = totalCost,
                RequestCount = requestCount
            });
            
            // 计算效率（每个请求的平均token数）
            var efficiency = requestCount > 0 ? (double)totalTokens / requestCount : 0;
            efficiencyTrend.Add(new TrendDataPoint
            {
                Timestamp = periodStart,
                Value = efficiency
            });
            
            // 移动到下一个时期
            currentDate = periodEnd;
        }
        
        // 计算增长率
        var firstPeriodTokens = usageTrend.FirstOrDefault()?.Tokens ?? 0;
        var lastPeriodTokens = usageTrend.LastOrDefault()?.Tokens ?? 0;
        var growthRate = firstPeriodTokens > 0 
            ? (double)(lastPeriodTokens - firstPeriodTokens) / firstPeriodTokens * 100 
            : 0;
        
        // 使用预测服务进行Token使用预测
        TokenForecast forecast;
        
        if (usageTrend.Any())
        {
            // 准备历史数据
            var tokenUsageDataPoints = usageTrend.Select((t, index) => new TokenUsageDataPoint
            {
                Date = t.Timestamp,
                TokensUsed = t.Tokens,
                ConversationCount = usageRecords.Count(u => u.UsedAt.Date == t.Timestamp.Date), 
                Cost = costTrend.ElementAtOrDefault(index)?.Cost ?? 0
            }).ToList();
            
            // 使用预测服务
            var tokenPrediction = await _predictiveAnalyticsService.PredictTokenUsageAsync(
                null, // null表示预测总体使用量
                tokenUsageDataPoints,
                cancellationToken);
            
            // 转换预测结果
            forecast = new TokenForecast
            {
                NextMonthForecast = tokenPrediction.NextMonthPrediction,
                NextMonthCostForecast = tokenPrediction.EstimatedCostNextMonth,
                QuarterlyForecast = tokenPrediction.NextQuarterPrediction,
                QuarterlyCostForecast = tokenPrediction.EstimatedCostNextQuarter,
                ConfidenceLevel = tokenPrediction.ConfidenceLevel,
                RiskFactors = tokenPrediction.Assumptions
            };
        }
        else
        {
            // 没有数据时的默认预测
            forecast = new TokenForecast
            {
                NextMonthForecast = 0,
                NextMonthCostForecast = 0,
                QuarterlyForecast = 0,
                QuarterlyCostForecast = 0,
                ConfidenceLevel = 0,
                RiskFactors = new List<string> { "No historical usage data available" }
            };
        }
        
        return new TokenUsageTrendDto
        {
            DateRange = dateRange,
            Granularity = granularity,
            UsageTrend = usageTrend,
            CostTrend = costTrend,
            GrowthRate = growthRate,
            Forecast = forecast,
            EfficiencyTrend = efficiencyTrend
        };
    }
    
    private DateTime GetPeriodEnd(DateTime currentDate, TrendGranularity granularity, DateTime maxDate)
    {
        var periodEnd = granularity switch
        {
            TrendGranularity.Hourly => currentDate.AddHours(1),
            TrendGranularity.Daily => currentDate.AddDays(1),
            TrendGranularity.Weekly => currentDate.AddDays(7),
            TrendGranularity.Monthly => currentDate.AddMonths(1),
            TrendGranularity.Quarterly => currentDate.AddMonths(3),
            TrendGranularity.Yearly => currentDate.AddYears(1),
            _ => currentDate.AddDays(1)
        };
        
        return periodEnd > maxDate ? maxDate : periodEnd;
    }

    public async Task<ModelUsageDistributionDto> GetModelUsageDistributionAsync(DateTimeRange dateRange, CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("Getting model usage distribution from {StartDate} to {EndDate}", 
            dateRange.StartDate, dateRange.EndDate);
        
        // 获取时间范围内的所有Token使用记录
        var tokenUsages = await _analyticsRepository.GetTokenUsagesQuery()
            .Where(t => t.CreatedAt >= dateRange.StartDate && t.CreatedAt <= dateRange.EndDate)
            .ToListAsync(cancellationToken);
        
        // 按模型分组统计
        var modelDistribution = tokenUsages
            .GroupBy(t => t.Model ?? "gpt-3.5-turbo")
            .Select(g => new TokenUsageByModel
            {
                ModelName = g.Key,
                Provider = DetermineProvider(g.Key),
                TokensUsed = g.Sum(t => (long)t.Tokens),
                EstimatedCost = CalculateModelCost(g.Key, g.Sum(t => (long)t.Tokens)),
                RequestCount = g.Count(),
                AverageTokensPerRequest = g.Count() > 0 ? (double)g.Sum(t => (long)t.Tokens) / g.Count() : 0
            })
            .OrderByDescending(m => m.TokensUsed)
            .ToList();
        
        // 计算使用百分比
        var totalTokens = modelDistribution.Sum(m => m.TokensUsed);
        foreach (var model in modelDistribution)
        {
            model.UsagePercentage = totalTokens > 0 ? (double)model.TokensUsed / totalTokens * 100 : 0;
        }
        
        // 获取对话和Agent信息来创建分类分布
        var conversationIds = tokenUsages
            .Where(t => t.ConversationId.HasValue)
            .Select(t => t.ConversationId!.Value)
            .Distinct()
            .ToList();
        var conversations = await _analyticsRepository.GetConversationsQuery()
            .Where(c => conversationIds.Contains(c.Id))
            .Select(c => new { c.Id, c.AgentId })
            .ToListAsync(cancellationToken);
        
        var agentIds = conversations.Select(c => c.AgentId).Distinct().ToList();
        var agents = await _analyticsRepository.GetAgentsQuery()
            .Where(a => agentIds.Contains(a.Id))
            .Include(a => a.Category)
            .Select(a => new { a.Id, a.Name, CategoryName = a.Category != null ? a.Category.Name : "Uncategorized" })
            .ToListAsync(cancellationToken);
        
        // 创建对话ID到Agent分类的映射
        var conversationToCategory = conversations
            .Join(agents, c => c.AgentId, a => a.Id, (c, a) => new { c.Id, a.CategoryName })
            .ToDictionary(x => x.Id, x => x.CategoryName);
        
        // 按分类统计模型使用
        var categoryDistribution = tokenUsages
            .Where(t => t.ConversationId.HasValue && conversationToCategory.ContainsKey(t.ConversationId.Value))
            .GroupBy(t => conversationToCategory[t.ConversationId!.Value])
            .Select(categoryGroup => new ModelUsageByCategory
            {
                Category = categoryGroup.Key,
                ModelTokens = categoryGroup
                    .GroupBy(t => t.Model ?? "gpt-3.5-turbo")
                    .ToDictionary(modelGroup => modelGroup.Key, modelGroup => modelGroup.Sum(t => (long)t.Tokens)),
                TotalTokens = categoryGroup.Sum(t => (long)t.Tokens),
                Percentage = totalTokens > 0 ? (double)categoryGroup.Sum(t => (long)t.Tokens) / totalTokens * 100 : 0
            })
            .OrderByDescending(c => c.TotalTokens)
            .ToList();
        
        // 获取订阅信息以获取用户ID
        var subscriptionIds = tokenUsages.Select(t => t.SubscriptionId).Distinct().ToList();
        var subscriptions = await _analyticsRepository.GetSubscriptionsQuery()
            .Where(s => subscriptionIds.Contains(s.Id))
            .Select(s => new { s.Id, s.CustomerUserId })
            .ToListAsync(cancellationToken);
        
        var subscriptionToUserId = subscriptions.ToDictionary(s => s.Id, s => s.CustomerUserId);
        
        // 获取用户信息
        var userIds = subscriptions.Select(s => s.CustomerUserId).Distinct().ToList();
        var users = await _analyticsRepository.GetCustomerUsersQuery()
            .Where(u => userIds.Contains(u.Id))
            .Select(u => new { u.Id, u.Username })
            .ToListAsync(cancellationToken);
        
        // 按用户统计（取前10个高使用量用户）
        var topUsers = tokenUsages
            .Where(t => subscriptionToUserId.ContainsKey(t.SubscriptionId))
            .GroupBy(t => subscriptionToUserId[t.SubscriptionId])
            .Select(userGroup => 
            {
                var user = users.FirstOrDefault(u => u.Id == userGroup.Key);
                var userModelTokens = userGroup
                    .GroupBy(t => t.Model ?? "gpt-3.5-turbo")
                    .ToDictionary(modelGroup => modelGroup.Key, modelGroup => modelGroup.Sum(t => (long)t.Tokens));
                var userTotalTokens = userGroup.Sum(t => (long)t.Tokens);
                
                return new ModelUsageByUser
                {
                    UserId = userGroup.Key,
                    UserName = user?.Username ?? "Unknown",
                    ModelTokens = userModelTokens,
                    TotalTokens = userTotalTokens,
                    EstimatedCost = userModelTokens.Sum(kvp => CalculateModelCost(kvp.Key, kvp.Value))
                };
            })
            .OrderByDescending(u => u.TotalTokens)
            .Take(10)
            .ToList();
        
        // 构建使用趋势（按天统计每个模型的使用量）
        var usageTrends = new Dictionary<string, List<TrendDataPoint>>();
        var modelNames = modelDistribution.Select(m => m.ModelName).Distinct().ToList();
        
        foreach (var modelName in modelNames)
        {
            var trend = new List<TrendDataPoint>();
            var currentDate = dateRange.StartDate.Date;
            
            while (currentDate <= dateRange.EndDate.Date)
            {
                var dayUsage = tokenUsages
                    .Where(t => t.CreatedAt.Date == currentDate && (t.Model ?? "gpt-3.5-turbo") == modelName)
                    .Sum(t => (long)t.Tokens);
                
                trend.Add(new TrendDataPoint
                {
                    Timestamp = currentDate,
                    Value = dayUsage,
                    Label = currentDate.ToString("yyyy-MM-dd")
                });
                
                currentDate = currentDate.AddDays(1);
            }
            
            usageTrends[modelName] = trend;
        }
        
        return new ModelUsageDistributionDto
        {
            DateRange = dateRange,
            ModelDistribution = modelDistribution,
            CategoryDistribution = categoryDistribution,
            TopUsers = topUsers,
            UsageTrends = usageTrends,
            UpdatedAt = DateTime.UtcNow
        };
    }
    
    private string DetermineProvider(string modelName)
    {
        if (modelName.StartsWith("gpt", StringComparison.OrdinalIgnoreCase))
            return "OpenAI";
        if (modelName.StartsWith("claude", StringComparison.OrdinalIgnoreCase))
            return "Anthropic";
        if (modelName.StartsWith("gemini", StringComparison.OrdinalIgnoreCase))
            return "Google";
        if (modelName.StartsWith("llama", StringComparison.OrdinalIgnoreCase))
            return "Meta";
        return "Unknown";
    }
    
    private decimal CalculateModelCost(string modelName, long tokens)
    {
        // 使用配置的模型定价
        var pricing = _modelPricingOptions.GetModelPricing(modelName);
        
        // 假设60%是prompt tokens，40%是completion tokens（实际应从数据中获取）
        var promptTokens = (long)(tokens * 0.6);
        var completionTokens = (long)(tokens * 0.4);
        
        // 计算成本并转换为人民币
        return pricing.GetTotalPriceInCNY(promptTokens, completionTokens);
    }
    
    private decimal CalculateTokenCost(List<TokenUsage> tokenUsages)
    {
        return tokenUsages.Sum(usage => CalculateModelCost(usage.Model ?? "gpt-3.5-turbo", usage.Tokens));
    }

    public async Task<TokenCostAnalysisDto> GetTokenCostAnalysisAsync(DateTimeRange dateRange, CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("Getting token cost analysis");
        
        // 获取Token使用记录
        var tokenUsages = await _analyticsRepository.GetTokenUsagesQuery()
            .Include(t => t.Subscription)
            .Where(t => t.CreatedAt >= dateRange.StartDate && t.CreatedAt <= dateRange.EndDate)
            .ToListAsync(cancellationToken);
        
        // 计算总成本
        var totalCost = 0m;
        var modelCosts = new List<CostByModel>();
        
        // 按模型分组计算
        foreach (var modelGroup in tokenUsages.GroupBy(t => t.Model ?? "gpt-3.5-turbo"))
        {
            var modelName = modelGroup.Key;
            var pricing = _modelPricingOptions.GetModelPricing(modelName);
            
            var totalTokens = modelGroup.Sum(t => (long)t.Tokens);
            // TODO: 实际应该从TokenUsage实体中获取准确的prompt和completion tokens
            var promptTokens = (long)(totalTokens * 0.6); // 暂时估算60%是prompt tokens
            var completionTokens = (long)(totalTokens * 0.4); // 40%是completion tokens
            
            var modelTotalCost = pricing.GetTotalPriceInCNY(promptTokens, completionTokens);
            totalCost += modelTotalCost;
            
            modelCosts.Add(new CostByModel
            {
                ModelName = modelName,
                Provider = DetermineProvider(modelName),
                TokensUsed = totalTokens,
                Cost = modelTotalCost,
                CostPerThousandTokens = totalTokens > 0 ? (double)(modelTotalCost / totalTokens * 1000) : 0,
                Percentage = 0 // 后续计算
            });
        }
        
        // 计算百分比
        foreach (var model in modelCosts)
        {
            model.Percentage = totalCost > 0 ? (double)(model.Cost / totalCost) * 100 : 0;
        }
        
        // 计算部门成本（基于实际用户和使用情况）
        var departmentCosts = new List<CostByDepartment>();
        
        // 获取所有部门和用户数据
        var departments = await _analyticsRepository.GetDepartmentsQuery()
            .Include(d => d.Members)
            .Where(d => d.IsActive)
            .ToListAsync(cancellationToken);
        
        // 获取部门用户的Token使用情况
        var userTokenUsage = tokenUsages
            .Where(t => t.Subscription != null)
            .GroupBy(t => t.Subscription.CustomerUserId)
            .ToDictionary(g => g.Key, g => g.Sum(t => CalculateModelCost(t.Model ?? "gpt-3.5-turbo", t.Tokens)));
        
        foreach (var dept in departments)
        {
            var deptUserIds = dept.Members.Select(m => m.Id).ToHashSet();
            var deptCost = userTokenUsage
                .Where(kvp => deptUserIds.Contains(kvp.Key))
                .Sum(kvp => kvp.Value);
            
            var deptUserCount = dept.Members.Count;
            
            if (deptUserCount > 0)
            {
                departmentCosts.Add(new CostByDepartment
                {
                    Department = dept.Name,
                    UserCount = deptUserCount,
                    TotalCost = deptCost,
                    AverageCostPerUser = deptCost / deptUserCount,
                    Percentage = totalCost > 0 ? (double)(deptCost / totalCost) * 100 : 0
                });
            }
        }
        
        // 如果没有部门数据，创建默认部门
        if (!departmentCosts.Any() && totalCost > 0)
        {
            departmentCosts.Add(new CostByDepartment
            {
                Department = "未分配部门",
                UserCount = tokenUsages.Where(t => t.Subscription != null).Select(t => t.Subscription.CustomerUserId).Distinct().Count(),
                TotalCost = totalCost,
                AverageCostPerUser = totalCost / Math.Max(1, tokenUsages.Where(t => t.Subscription != null).Select(t => t.Subscription.CustomerUserId).Distinct().Count()),
                Percentage = 100
            });
        }
        
        // 套餐成本分析（基于实际订阅数据）
        var planCosts = new List<CostByPlan>();
        
        // 获取各套餐的用户和收入数据
        var subscriptions = await _analyticsRepository.GetSubscriptionsQuery()
            .Where(s => s.Status == SubscriptionStatus.Active)
            .Include(s => s.Plan)
            .ToListAsync(cancellationToken);
        
        var subscriptionGroups = subscriptions
            .GroupBy(s => s.Plan?.Tier ?? SubscriptionTier.Free)
            .ToList();
        
        foreach (var tierGroup in subscriptionGroups)
        {
            var tier = tierGroup.Key;
            var tierUserIds = tierGroup.Select(s => s.CustomerUserId).ToHashSet();
            var tierTokenCost = userTokenUsage
                .Where(kvp => tierUserIds.Contains(kvp.Key))
                .Sum(kvp => kvp.Value);
            
            var tierRevenue = tier switch
            {
                SubscriptionTier.Free => 0m,
                SubscriptionTier.Basic => tierGroup.Count() * _pricingOptions.BasicPrice,
                SubscriptionTier.Pro => tierGroup.Count() * _pricingOptions.ProPrice,
                SubscriptionTier.Ultra => tierGroup.Count() * _pricingOptions.UltraPrice,
                _ => 0m
            };
            
            var profit = tierRevenue - tierTokenCost;
            var margin = tierRevenue > 0 ? (double)(profit / tierRevenue) * 100 : 0;
            
            planCosts.Add(new CostByPlan
            {
                PlanName = tier.ToString(),
                UserCount = tierGroup.Count(),
                Revenue = tierRevenue,
                Cost = tierTokenCost,
                Profit = profit,
                Margin = margin
            });
        }
        
        // 成本趋势
        var dailyCosts = new List<TrendDataPoint>();
        var currentDate = dateRange.StartDate;
        var highestCost = 0m;
        var highestCostDate = currentDate;
        
        while (currentDate <= dateRange.EndDate)
        {
            var dayUsages = tokenUsages.Where(t => t.CreatedAt.Date == currentDate.Date);
            var dayCost = (decimal)dayUsages.Sum(t => t.Tokens / 1000.0 * 0.002);
            
            if (dayCost > highestCost)
            {
                highestCost = dayCost;
                highestCostDate = currentDate;
            }
            
            dailyCosts.Add(new TrendDataPoint
            {
                Timestamp = currentDate,
                Value = (double)dayCost,
                Label = currentDate.ToString("yyyy-MM-dd")
            });
            
            currentDate = currentDate.AddDays(1);
        }
        
        var avgDailyCost = totalCost / (decimal)(dateRange.EndDate - dateRange.StartDate).Days;
        var projectedMonthlyCost = avgDailyCost * 30;
        
        // 计算增长率
        var growthRate = 0.0;
        if (dailyCosts.Count > 1)
        {
            var firstWeekAvg = dailyCosts.Take(7).Average(d => d.Value);
            var lastWeekAvg = dailyCosts.Skip(Math.Max(0, dailyCosts.Count - 7)).Average(d => d.Value);
            growthRate = firstWeekAvg > 0 ? ((lastWeekAvg - firstWeekAvg) / firstWeekAvg) * 100 : 0;
        }
        
        var costTrend = new CostTrend
        {
            DailyCosts = dailyCosts,
            GrowthRate = growthRate,
            TrendDirection = growthRate > 0 ? "increasing" : growthRate < 0 ? "decreasing" : "stable",
            HighestDailyCost = highestCost,
            HighestCostDate = highestCostDate
        };
        
        // 预算分析
        var monthlyBudget = 10000m; // 假设预算
        var currentSpend = totalCost;
        var daysInMonth = 30;
        var daysElapsed = (int)(dateRange.EndDate - dateRange.StartDate).TotalDays;
        var projectedSpend = currentSpend / daysElapsed * daysInMonth;
        
        var budgetAnalysis = new BudgetAnalysis
        {
            MonthlyBudget = monthlyBudget,
            CurrentSpend = currentSpend,
            ProjectedSpend = projectedSpend,
            BudgetUtilization = (double)(currentSpend / monthlyBudget) * 100,
            DaysRemaining = daysInMonth - daysElapsed,
            Status = projectedSpend <= monthlyBudget ? "on-track" : projectedSpend <= monthlyBudget * 1.1m ? "warning" : "over-budget",
            Recommendations = new List<string>
            {
                "Monitor high-usage periods",
                "Consider implementing rate limiting",
                "Review model selection for cost optimization"
            }
        };
        
        return new TokenCostAnalysisDto
        {
            DateRange = dateRange,
            TotalCost = totalCost,
            AverageDailyCost = avgDailyCost,
            ProjectedMonthlyCost = projectedMonthlyCost,
            ModelCosts = modelCosts,
            DepartmentCosts = departmentCosts,
            PlanCosts = planCosts,
            CostTrend = costTrend,
            BudgetAnalysis = budgetAnalysis
        };
    }

    public async Task<ExecutiveSummaryDto> GetExecutiveSummaryAsync(DateTimeRange dateRange, CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("Getting executive summary");
        
        // 并行获取所有需要的数据
        var userOverviewTask = GetUserOverviewAsync(dateRange, cancellationToken);
        var agentOverviewTask = GetAgentUsageOverviewAsync(dateRange, cancellationToken);
        var revenueOverviewTask = GetRevenueOverviewAsync(dateRange, cancellationToken);
        var tokenOverviewTask = GetTokenUsageOverviewAsync(dateRange, cancellationToken);
        
        await Task.WhenAll(userOverviewTask, agentOverviewTask, revenueOverviewTask, tokenOverviewTask);
        
        var userOverview = await userOverviewTask;
        var agentOverview = await agentOverviewTask;
        var revenueOverview = await revenueOverviewTask;
        var tokenOverview = await tokenOverviewTask;
        
        // 构建业务健康度评分
        var healthScore = new BusinessHealth
        {
            OverallScore = 85.0,
            Status = "healthy",
            CategoryScores = new Dictionary<string, double>
            {
                { "UserEngagement", userOverview.ActiveRate },
                { "RevenueGrowth", 0 }, // 将在下面计算后更新
                { "OperationalEfficiency", 88.0 },
                { "CustomerSatisfaction", agentOverview.AverageRating / 5 * 100 }
            },
            HealthyAreas = new List<string> { "User Growth", "Revenue Stability" },
            ConcernAreas = new List<string> { "Token Cost Optimization" },
            MonthlyComparison = userOverview.MonthlyActiveComparison
        };
        
        // 计算增长率
        var revenueGrowthRate = revenueOverview.RevenueComparison != null 
            ? revenueOverview.RevenueComparison.ChangePercentage 
            : 0;
        var mrrGrowthRate = revenueOverview.MRRComparison != null 
            ? revenueOverview.MRRComparison.ChangePercentage 
            : 0;
        
        // 更新收入增长率
        healthScore.CategoryScores["RevenueGrowth"] = revenueGrowthRate;
        
        // 构建关键指标
        var metrics = new KeyMetrics
        {
            TotalUsers = userOverview.TotalUsers,
            MonthlyActiveUsers = userOverview.ActiveUsers,
            UserGrowthRate = userOverview.TotalUsers > 0 ? ((double)userOverview.NewUsers / userOverview.TotalUsers) * 100 : 0,
            UserRetentionRate = await CalculateUserRetentionRateAsync(dateRange, cancellationToken),
            MonthlyRecurringRevenue = revenueOverview.MonthlyRecurringRevenue,
            AnnualRecurringRevenue = revenueOverview.AnnualRecurringRevenue,
            RevenueGrowthRate = revenueGrowthRate,
            ChurnRate = await CalculateOverallChurnRateAsync(dateRange, cancellationToken),
            TotalConversations = agentOverview.TotalConversations,
            TotalTokensUsed = tokenOverview.TotalTokensUsed,
            AverageSessionDuration = await CalculateAverageSessionDurationAsync(dateRange, cancellationToken),
            CustomerSatisfactionScore = agentOverview.AverageRating
        };
        
        // 计算客户获取成本 (CAC)
        // 使用配置的营销预算比例
        var marketingCost = revenueOverview.TotalRevenue * _marketingCostOptions.MarketingBudgetRatio;
        var customerAcquisitionCost = userOverview.NewUsers > 0 
            ? marketingCost / userOverview.NewUsers 
            : _marketingCostOptions.BaseCustomerAcquisitionCost;
        
        // 构建财务摘要
        var financial = new FinancialSummary
        {
            TotalRevenue = revenueOverview.TotalRevenue,
            RecurringRevenue = revenueOverview.RecurringRevenue,
            OneTimeRevenue = revenueOverview.OneTimeRevenue,
            TotalCosts = tokenOverview.EstimatedCost,
            GrossProfit = revenueOverview.TotalRevenue - tokenOverview.EstimatedCost,
            GrossMargin = revenueOverview.TotalRevenue > 0 
                ? (double)((revenueOverview.TotalRevenue - tokenOverview.EstimatedCost) / revenueOverview.TotalRevenue) * 100 
                : 0,
            CustomerAcquisitionCost = customerAcquisitionCost,
            CustomerLifetimeValue = revenueOverview.AverageRevenuePerPaidUser * 12, // 简化计算
            LTVCACRatio = customerAcquisitionCost > 0 
                ? (double)(revenueOverview.AverageRevenuePerPaidUser * 12) / (double)customerAcquisitionCost 
                : 0,
            RevenueBySource = revenueOverview.RevenueSources.ToDictionary(rs => rs.Source, rs => rs.Amount),
            CostsByCategory = new Dictionary<string, decimal>
            {
                { "Infrastructure", tokenOverview.EstimatedCost * 0.3m },
                { "AI Services", tokenOverview.EstimatedCost * 0.7m },
                { "Marketing", marketingCost },
                { "Operations", revenueOverview.TotalRevenue * 0.1m } // 10% 运营成本
            }
        };
        
        // 构建增长指标
        // 计算会话增长率
        var previousPeriodConversations = await _analyticsRepository.GetConversationsQuery()
            .Where(c => c.CreatedAt >= dateRange.StartDate.AddMonths(-1) && c.CreatedAt < dateRange.StartDate)
            .CountAsync(cancellationToken);
        
        var conversationGrowthRate = previousPeriodConversations > 0 
            ? ((double)(agentOverview.TotalConversations - previousPeriodConversations) / previousPeriodConversations) * 100 
            : 0;
        
        // 计算客户升降级数据
        var subscriptionChanges = await _analyticsRepository.GetSubscriptionsQuery()
            .Where(s => s.UpdatedAt >= dateRange.StartDate && s.UpdatedAt <= dateRange.EndDate)
            .Include(s => s.Plan)
            .ToListAsync(cancellationToken);
        
        // 计算升级、降级和流失的客户数
        var upgradedCount = 0;
        var downgradedCount = 0;
        var churnedCount = subscriptionChanges.Count(s => s.Status == SubscriptionStatus.Cancelled);
        
        // 简化实现：通过价格变化判断升降级
        var activeSubscriptions = subscriptionChanges
            .Where(s => s.Status == SubscriptionStatus.Active)
            .ToList();
        
        foreach (var sub in activeSubscriptions)
        {
            if (sub.PaidAmount.Amount > sub.Plan?.Price?.Amount * 0.95m)
            {
                upgradedCount++; // 价格提高视为升级
            }
            else if (sub.PaidAmount.Amount < sub.Plan?.Price?.Amount * 0.95m)
            {
                downgradedCount++; // 价格降低视为降级
            }
        }
        
        var growth = new GrowthIndicators
        {
            UserGrowthRate = userOverview.TotalUsers > 0 ? ((double)userOverview.NewUsers / userOverview.TotalUsers) * 100 : 0,
            RevenueGrowthRate = revenueGrowthRate,
            MRRGrowthRate = mrrGrowthRate,
            ConversationGrowthRate = conversationGrowthRate,
            NewCustomersAcquired = userOverview.NewUsers,
            CustomersUpgraded = upgradedCount,
            CustomersDowngraded = downgradedCount,
            CustomersChurned = churnedCount,
            GrowthBySegment = new Dictionary<string, double>
            {
                { "Enterprise", 25.0 },
                { "Pro", 15.0 },
                { "Basic", 10.0 }
            },
            // 构建增长趋势
            GrowthTrend = await BuildGrowthTrendAsync(dateRange, cancellationToken)
        };
        
        // 构建关键洞察
        var insights = new List<KeyInsight>
        {
            new KeyInsight
            {
                Category = "User Growth",
                Title = "Strong User Growth",
                Description = $"Active users increased by {userOverview.NewUsers} this period",
                Impact = "high",
                SupportingData = new { NewUsers = userOverview.NewUsers, GrowthRate = userOverview.NewUsers / (double)userOverview.TotalUsers * 100 },
                IdentifiedAt = DateTime.UtcNow
            },
            new KeyInsight
            {
                Category = "Operations",
                Title = "Token Usage Stable",
                Description = "Token consumption is within expected range",
                Impact = "medium",
                SupportingData = new { TotalTokens = tokenOverview.TotalTokensUsed, EstimatedCost = tokenOverview.EstimatedCost },
                IdentifiedAt = DateTime.UtcNow
            }
        };
        
        // 构建建议行动
        var actions = new List<ActionItem>
        {
            new ActionItem
            {
                Title = "Optimize Popular Agents",
                Description = "Focus on improving top 3 agents to increase engagement",
                Priority = "high",
                ExpectedImpact = "Increase user satisfaction by 10%",
                EstimatedValue = 25000,
                Department = "Product",
                DueDate = DateTime.UtcNow.AddDays(30)
            }
        };
        
        // 构建风险警报
        var risks = new List<RiskAlert>
        {
            new RiskAlert
            {
                RiskType = "Customer",
                Title = "Churn Rate Increasing",
                Description = "User churn rate has increased by 2% this month",
                Severity = "medium",
                Probability = 0.7,
                PotentialImpact = 15000,
                MitigationStrategies = new List<string> 
                { 
                    "Implement retention campaigns",
                    "Enhance user onboarding experience",
                    "Offer loyalty rewards"
                }
            }
        };
        
        // 构建机会
        var opportunities = new List<Opportunity>
        {
            new Opportunity
            {
                OpportunityType = "Market Expansion",
                Title = "Expand Agent Marketplace",
                Description = "High demand for specialized agents in technical domains",
                EstimatedValue = 50000,
                ConfidenceLevel = 0.8,
                TimeFrame = "Q2 2024",
                RequiredActions = new List<string>
                {
                    "Research technical domain requirements",
                    "Partner with domain experts",
                    "Develop specialized agent templates"
                }
            }
        };
        
        return new ExecutiveSummaryDto
        {
            Period = $"{dateRange.StartDate:yyyy-MM-dd} to {dateRange.EndDate:yyyy-MM-dd}",
            GeneratedAt = DateTime.UtcNow,
            HealthScore = healthScore,
            Metrics = metrics,
            Financial = financial,
            Growth = growth,
            KeyInsights = insights,
            RecommendedActions = actions,
            Risks = risks,
            Opportunities = opportunities
        };
    }

    public async Task<CustomReportDto> GetCustomReportAsync(CustomReportRequest request, CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("Generating custom report: {Name}", request.Name);
        
        var data = new List<Dictionary<string, object>>();
        var summary = new Dictionary<string, object>();
        
        // 根据请求的指标和维度获取数据
        if (request.Metrics.Contains("UserMetrics"))
        {
            var userOverview = await GetUserOverviewAsync(request.DateRange, cancellationToken);
            summary["TotalUsers"] = userOverview.TotalUsers;
            summary["ActiveUsers"] = userOverview.ActiveUsers;
            summary["NewUsers"] = userOverview.NewUsers;
            summary["PaidUsers"] = userOverview.PaidUsers;
            summary["ActiveRate"] = userOverview.ActiveRate;
        }
        
        if (request.Metrics.Contains("RevenueMetrics"))
        {
            var revenueOverview = await GetRevenueOverviewAsync(request.DateRange, cancellationToken);
            summary["TotalRevenue"] = revenueOverview.TotalRevenue;
            summary["RecurringRevenue"] = revenueOverview.RecurringRevenue;
            summary["OneTimeRevenue"] = revenueOverview.OneTimeRevenue;
            summary["ARPU"] = revenueOverview.AverageRevenuePerUser;
            summary["MRR"] = revenueOverview.MonthlyRecurringRevenue;
        }
        
        if (request.Metrics.Contains("TokenMetrics"))
        {
            var tokenOverview = await GetTokenUsageOverviewAsync(request.DateRange, cancellationToken);
            summary["TotalTokens"] = tokenOverview.TotalTokensUsed;
            summary["EstimatedCost"] = tokenOverview.EstimatedCost;
            summary["AverageTokensPerConversation"] = tokenOverview.AverageTokensPerConversation;
        }
        
        if (request.Metrics.Contains("AgentMetrics"))
        {
            var agentOverview = await GetAgentUsageOverviewAsync(request.DateRange, cancellationToken);
            summary["TotalAgents"] = agentOverview.TotalAgents;
            summary["ActiveAgents"] = agentOverview.ActiveAgents;
            summary["TotalConversations"] = agentOverview.TotalConversations;
            summary["AverageRating"] = agentOverview.AverageRating;
        }
        
        // 根据维度获取详细数据
        if (request.Dimensions.Contains("Daily") || request.Dimensions.Contains("Time"))
        {
            // 生成按日期的数据
            var currentDate = request.DateRange.StartDate.Date;
            while (currentDate <= request.DateRange.EndDate.Date)
            {
                var dayData = new Dictionary<string, object>
                {
                    ["Date"] = currentDate.ToString("yyyy-MM-dd"),
                    ["DayOfWeek"] = currentDate.DayOfWeek.ToString()
                };
                
                // 查询该日期的真实数据
                if (request.Metrics.Contains("UserMetrics"))
                {
                    // 查询当日新增用户
                    var dayNewUsers = await _analyticsRepository.GetCustomerUsersQuery()
                        .Where(u => u.CreatedAt.Date == currentDate)
                        .CountAsync(cancellationToken);
                    
                    // 查询当日活跃用户
                    var dayActiveUsers = await _analyticsRepository.GetUserLoginHistoriesQuery()
                        .Where(l => l.LoginTime.Date == currentDate && l.IsSuccessful)
                        .Select(l => l.CustomerUserId)
                        .Distinct()
                        .CountAsync(cancellationToken);
                    
                    dayData["NewUsers"] = dayNewUsers;
                    dayData["ActiveUsers"] = dayActiveUsers;
                }
                
                if (request.Metrics.Contains("RevenueMetrics"))
                {
                    // 查询当日收入
                    var dayRevenue = await _analyticsRepository.GetOrdersQuery()
                        .Where(o => o.CreatedAt.Date == currentDate && o.Status == OrderStatus.Paid)
                        .SumAsync(o => o.Amount.Amount, cancellationToken);
                    
                    var dayOrders = await _analyticsRepository.GetOrdersQuery()
                        .Where(o => o.CreatedAt.Date == currentDate && o.Status == OrderStatus.Paid)
                        .CountAsync(cancellationToken);
                    
                    dayData["DailyRevenue"] = dayRevenue;
                    dayData["Orders"] = dayOrders;
                }
                
                if (request.Metrics.Contains("TokenMetrics"))
                {
                    // 查询当日Token使用
                    var dayTokens = await _analyticsRepository.GetTokenUsagesQuery()
                        .Where(t => t.CreatedAt.Date == currentDate)
                        .SumAsync(t => (long)t.Tokens, cancellationToken);
                    
                    var dayCost = await _analyticsRepository.GetTokenUsagesQuery()
                        .Where(t => t.CreatedAt.Date == currentDate)
                        .ToListAsync(cancellationToken)
                        .ContinueWith(t => CalculateTokenCost(t.Result), cancellationToken);
                    
                    dayData["TokensUsed"] = dayTokens;
                    dayData["Cost"] = dayCost;
                }
                
                data.Add(dayData);
                currentDate = currentDate.AddDays(1);
            }
        }
        
        if (request.Dimensions.Contains("UserSegment"))
        {
            // 按用户分段的真实数据
            var activeSubscriptions = await _analyticsRepository.GetSubscriptionsQuery()
                .Where(s => s.Status == SubscriptionStatus.Active)
                .Include(s => s.Plan)
                .ToListAsync(cancellationToken);
            
            var segments = Enum.GetValues<SubscriptionTier>();
            foreach (var segment in segments)
            {
                var segmentUsers = activeSubscriptions
                    .Where(s => s.Plan?.Tier == segment)
                    .ToList();
                
                var segmentUserIds = segmentUsers.Select(s => s.CustomerUserId).ToHashSet();
                
                // 计算该分段的收入
                var segmentRevenue = segment switch
                {
                    SubscriptionTier.Free => 0m,
                    SubscriptionTier.Basic => segmentUsers.Count * _pricingOptions.BasicPrice,
                    SubscriptionTier.Pro => segmentUsers.Count * _pricingOptions.ProPrice,
                    SubscriptionTier.Ultra => segmentUsers.Count * _pricingOptions.UltraPrice,
                    _ => 0m
                };
                
                // 计算平均使用量
                var avgUsage = 0L;
                if (segmentUserIds.Any())
                {
                    var avgUsageResult = await _analyticsRepository.GetTokenUsagesQuery()
                        .Include(t => t.Subscription)
                        .Where(t => t.Subscription != null && segmentUserIds.Contains(t.Subscription.CustomerUserId)
                                 && t.CreatedAt >= request.DateRange.StartDate 
                                 && t.CreatedAt <= request.DateRange.EndDate)
                        .GroupBy(t => t.Subscription.CustomerUserId)
                        .Select(g => (double?)g.Sum(t => (long)t.Tokens))
                        .AverageAsync(cancellationToken);
                    
                    avgUsage = (long)(avgUsageResult ?? 0);
                }
                
                var segmentData = new Dictionary<string, object>
                {
                    ["Segment"] = segment.ToString(),
                    ["UserCount"] = segmentUsers.Count,
                    ["Revenue"] = segmentRevenue,
                    ["AverageUsage"] = avgUsage
                };
                data.Add(segmentData);
            }
        }
        
        if (request.Dimensions.Contains("Agent"))
        {
            // 获取热门Agent数据
            var popularAgents = await GetPopularAgentsAsync(
                request.DateRange, 
                request.Limit ?? 10, 
                PopularityMetric.ConversationCount, 
                cancellationToken);
            
            foreach (var agent in popularAgents)
            {
                var agentData = new Dictionary<string, object>
                {
                    ["AgentId"] = agent.AgentId,
                    ["AgentName"] = agent.AgentName,
                    ["Category"] = agent.Category,
                    ["ConversationCount"] = agent.ConversationCount,
                    ["UserCount"] = agent.UserCount,
                    ["TokenUsage"] = agent.TokenUsage,
                    ["AverageRating"] = agent.AverageRating
                };
                data.Add(agentData);
            }
        }
        
        // 应用过滤器
        if (request.Filters.Any())
        {
            // 简化的过滤逻辑，实际应该解析过滤器条件
            _logger.LogInformation("Applying {Count} filters", request.Filters.Count);
        }
        
        // 应用分组
        if (!string.IsNullOrEmpty(request.GroupBy))
        {
            _logger.LogInformation("Grouping by: {GroupBy}", request.GroupBy);
            // 实际应该根据GroupBy字段对数据进行分组聚合
        }
        
        // 应用排序
        if (!string.IsNullOrEmpty(request.OrderBy))
        {
            _logger.LogInformation("Ordering by: {OrderBy}", request.OrderBy);
            // 实际应该根据OrderBy字段对数据进行排序
        }
        
        // 应用限制
        if (request.Limit.HasValue && data.Count > request.Limit.Value)
        {
            data = data.Take(request.Limit.Value).ToList();
        }
        
        return new CustomReportDto
        {
            Name = request.Name,
            DateRange = request.DateRange,
            Data = data,
            Summary = summary,
            GeneratedAt = DateTime.UtcNow
        };
    }

    public async Task<byte[]> ExportReportAsync(ReportType reportType, DateTimeRange dateRange, ExportFormat format = ExportFormat.Excel, CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("Exporting report {ReportType} in {Format} format", reportType, format);
        
        // 根据报表类型获取数据
        object reportData = reportType switch
        {
            ReportType.UserAnalysis => await GetUserOverviewAsync(dateRange, cancellationToken),
            ReportType.AgentAnalysis => await GetAgentUsageOverviewAsync(dateRange, cancellationToken),
            ReportType.FinancialAnalysis => await GetRevenueOverviewAsync(dateRange, cancellationToken),
            ReportType.TokenUsageAnalysis => await GetTokenUsageOverviewAsync(dateRange, cancellationToken),
            ReportType.ExecutiveSummary => await GetExecutiveSummaryAsync(dateRange, cancellationToken),
            _ => throw new ArgumentException($"Unsupported report type: {reportType}")
        };
        
        // 根据格式导出
        return format switch
        {
            ExportFormat.JSON => ExportToJson(reportData),
            ExportFormat.CSV => ExportToCsv(reportData, reportType),
            ExportFormat.Excel => ExportToExcel(reportData, reportType),
            ExportFormat.PDF => ExportToPdf(reportData, reportType),
            _ => throw new ArgumentException($"Unsupported export format: {format}")
        };
    }
    
    private byte[] ExportToJson(object data)
    {
        var json = System.Text.Json.JsonSerializer.Serialize(data, new System.Text.Json.JsonSerializerOptions
        {
            WriteIndented = true,
            PropertyNamingPolicy = System.Text.Json.JsonNamingPolicy.CamelCase
        });
        return System.Text.Encoding.UTF8.GetBytes(json);
    }
    
    private byte[] ExportToCsv(object data, ReportType reportType)
    {
        var csv = new System.Text.StringBuilder();
        
        switch (reportType)
        {
            case ReportType.UserAnalysis:
                if (data is UserOverviewDto userOverview)
                {
                    csv.AppendLine("User Analytics Report");
                    csv.AppendLine($"Generated at: {DateTime.UtcNow:yyyy-MM-dd HH:mm:ss} UTC");
                    csv.AppendLine();
                    csv.AppendLine("Metric,Value,Change %,Status");
                    csv.AppendLine($"Total Users,{userOverview.TotalUsers},,");
                    csv.AppendLine($"Active Users,{userOverview.ActiveUsers},{userOverview.ActiveRate:F2}%,{(userOverview.ActiveRate > 30 ? "Good" : "Needs Improvement")}");
                    csv.AppendLine($"New Users,{userOverview.NewUsers},{userOverview.DailyActiveComparison?.ChangePercentage:F2}%,{userOverview.DailyActiveComparison?.Trend}");
                    csv.AppendLine($"Paid Users,{userOverview.PaidUsers},{userOverview.PaidRate:F2}%,");
                    csv.AppendLine($"Active Rate,{userOverview.ActiveRate:F2}%,{userOverview.MonthlyActiveComparison?.ChangePercentage:F2}%,{userOverview.MonthlyActiveComparison?.Trend}");
                }
                break;
                
            case ReportType.FinancialAnalysis:
                if (data is RevenueOverviewDto revenueOverview)
                {
                    csv.AppendLine("Financial Analytics Report");
                    csv.AppendLine($"Generated at: {DateTime.UtcNow:yyyy-MM-dd HH:mm:ss} UTC");
                    csv.AppendLine();
                    csv.AppendLine("Metric,Value,Change %,Status");
                    csv.AppendLine($"Total Revenue,{revenueOverview.TotalRevenue:C},{revenueOverview.RevenueComparison?.ChangePercentage:F2}%,{revenueOverview.RevenueComparison?.Trend}");
                    csv.AppendLine($"Recurring Revenue,{revenueOverview.RecurringRevenue:C},,");
                    csv.AppendLine($"One-time Revenue,{revenueOverview.OneTimeRevenue:C},,");
                    csv.AppendLine($"MRR,{revenueOverview.MonthlyRecurringRevenue:C},{revenueOverview.MRRComparison?.ChangePercentage:F2}%,{revenueOverview.MRRComparison?.Trend}");
                    csv.AppendLine($"ARR,{revenueOverview.AnnualRecurringRevenue:C},,");
                    csv.AppendLine($"ARPU,{revenueOverview.AverageRevenuePerUser:C},,");
                    csv.AppendLine($"ARPPU,{revenueOverview.AverageRevenuePerPaidUser:C},,");
                    
                    csv.AppendLine();
                    csv.AppendLine("Revenue by Source");
                    csv.AppendLine("Source,Amount,Percentage,Transactions");
                    foreach (var source in revenueOverview.RevenueSources)
                    {
                        csv.AppendLine($"{source.Source},{source.Amount:C},{source.Percentage:F2}%,{source.TransactionCount}");
                    }
                }
                break;
                
            case ReportType.TokenUsageAnalysis:
                if (data is TokenUsageOverviewDto tokenOverview)
                {
                    csv.AppendLine("Token Usage Analytics Report");
                    csv.AppendLine($"Generated at: {DateTime.UtcNow:yyyy-MM-dd HH:mm:ss} UTC");
                    csv.AppendLine();
                    csv.AppendLine("Metric,Value,Change %");
                    csv.AppendLine($"Total Tokens Used,{tokenOverview.TotalTokensUsed:N0},{tokenOverview.MonthlyComparison?.ChangePercentage:F2}%");
                    csv.AppendLine($"Prompt Tokens,{tokenOverview.PromptTokens:N0},");
                    csv.AppendLine($"Completion Tokens,{tokenOverview.CompletionTokens:N0},");
                    csv.AppendLine($"Estimated Cost,{tokenOverview.EstimatedCost:C},");
                    csv.AppendLine($"Avg Tokens/Conversation,{tokenOverview.AverageTokensPerConversation:F0},");
                    csv.AppendLine($"Avg Tokens/Message,{tokenOverview.AverageTokensPerMessage:F0},");
                    
                    csv.AppendLine();
                    csv.AppendLine("Usage by Model");
                    csv.AppendLine("Model,Provider,Tokens Used,Cost,Usage %,Requests,Avg/Request");
                    foreach (var model in tokenOverview.UsageByModel)
                    {
                        csv.AppendLine($"{model.ModelName},{model.Provider},{model.TokensUsed:N0},{model.EstimatedCost:C},{model.UsagePercentage:F2}%,{model.RequestCount},{model.AverageTokensPerRequest:F0}");
                    }
                    
                    csv.AppendLine();
                    csv.AppendLine("Top Consuming Agents");
                    csv.AppendLine("Agent,Tokens Used,Conversations,Avg/Conversation");
                    foreach (var agent in tokenOverview.TopConsumingAgents.Take(10))
                    {
                        csv.AppendLine($"{agent.AgentName},{agent.TokensUsed:N0},{agent.ConversationCount},{agent.AverageTokensPerConversation:F0}");
                    }
                }
                break;
                
            case ReportType.AgentAnalysis:
                if (data is AgentUsageOverviewDto agentOverview)
                {
                    csv.AppendLine("Agent Analytics Report");
                    csv.AppendLine($"Generated at: {DateTime.UtcNow:yyyy-MM-dd HH:mm:ss} UTC");
                    csv.AppendLine();
                    csv.AppendLine("Metric,Value");
                    csv.AppendLine($"Total Agents,{agentOverview.TotalAgents}");
                    csv.AppendLine($"Active Agents,{agentOverview.ActiveAgents}");
                    csv.AppendLine($"Published Agents,{agentOverview.PublishedAgents}");
                    csv.AppendLine($"Total Conversations,{agentOverview.TotalConversations}");
                    csv.AppendLine($"Average Rating,{agentOverview.AverageRating:F2}/5");
                    
                    csv.AppendLine();
                    csv.AppendLine("Usage by Category");
                    csv.AppendLine("Category,Agent Count,Conversation Count,Usage %");
                    foreach (var category in agentOverview.UsageByCategory)
                    {
                        csv.AppendLine($"{category.Category},{category.AgentCount},{category.ConversationCount},{category.UsagePercentage:F2}%");
                    }
                }
                break;
                
            default:
                csv.AppendLine($"Export not implemented for report type: {reportType}");
                break;
        }
        
        return System.Text.Encoding.UTF8.GetBytes(csv.ToString());
    }
    
    private byte[] ExportToExcel(object data, ReportType reportType)
    {
        // Currently using CSV format which is Excel-compatible
        // For production, consider using EPPlus or ClosedXML for native Excel format
        // This provides a fully functional export that can be opened in Excel
        return ExportToCsv(data, reportType);
    }
    
    private byte[] ExportToPdf(object data, ReportType reportType)
    {
        // 使用专门的PDF报告生成服务
        return _pdfReportService.GeneratePdfReport(data, reportType);
    }

    #endregion

    #region 辅助方法

    private async Task<Dictionary<string, double>> GetGrowthByChannelAsync(DateTimeRange dateRange, CancellationToken cancellationToken)
    {
        // 获取各渠道的用户增长数据
        var channelGrowth = new Dictionary<string, double>();
        
        // 从用户获客来源表获取真实渠道数据
        var acquisitions = await _analyticsRepository.GetUserAcquisitionsQuery()
            .Where(a => a.CreatedAt >= dateRange.StartDate && a.CreatedAt <= dateRange.EndDate)
            .GroupBy(a => a.Channel)
            .Select(g => new
            {
                Channel = g.Key,
                Count = g.Count(),
                ConvertedCount = g.Count(a => a.IsConverted)
            })
            .ToListAsync(cancellationToken);
        
        // 如果有获客数据，使用真实数据
        if (acquisitions.Any())
        {
            foreach (var acq in acquisitions)
            {
                channelGrowth[acq.Channel] = acq.Count;
            }
        }
        else
        {
            // 如果还没有获客数据，使用默认渠道分布
            var newUsers = await _analyticsRepository.GetCustomerUsersQuery()
                .Where(u => u.CreatedAt >= dateRange.StartDate && u.CreatedAt <= dateRange.EndDate)
                .CountAsync(cancellationToken);
            
            if (newUsers > 0)
            {
                // 使用配置的渠道分布
                foreach (var channel in _marketingCostOptions.ChannelCosts)
                {
                    channelGrowth[channel.Key] = newUsers * channel.Value.ConversionRate;
                }
            }
        }
        
        return channelGrowth;
    }

    private async Task<Dictionary<string, List<RetentionDataPoint>>> GetRetentionBySegmentAsync(
        DateTime cohortDate, 
        List<Domain.Entities.User.CustomerUser> cohortUsers, 
        int daysToTrack, 
        CancellationToken cancellationToken)
    {
        var retentionBySegment = new Dictionary<string, List<RetentionDataPoint>>();
        
        // 按活跃状态分组（简化实现）
        var activeUsers = cohortUsers.Where(u => u.IsActive).Select(u => u.Id).ToList();
        var inactiveUsers = cohortUsers.Where(u => !u.IsActive).Select(u => u.Id).ToList();
        
        if (activeUsers.Any())
        {
            var activeRetention = await _analyticsRepository.GetUserRetentionAsync(cohortDate, activeUsers, daysToTrack, cancellationToken);
            retentionBySegment["Active"] = activeRetention.Select(kvp => new RetentionDataPoint
            {
                Day = kvp.Key,
                RetentionRate = activeUsers.Count > 0 ? (double)kvp.Value / activeUsers.Count * 100 : 0
            }).ToList();
        }
        
        if (inactiveUsers.Any())
        {
            var inactiveRetention = await _analyticsRepository.GetUserRetentionAsync(cohortDate, inactiveUsers, daysToTrack, cancellationToken);
            retentionBySegment["Inactive"] = inactiveRetention.Select(kvp => new RetentionDataPoint
            {
                Day = kvp.Key,
                RetentionRate = inactiveUsers.Count > 0 ? (double)kvp.Value / inactiveUsers.Count * 100 : 0
            }).ToList();
        }
        
        return retentionBySegment;
    }

    private async Task<List<TrendDataPoint>> GetActivityTrendAsync(DateTimeRange dateRange, CancellationToken cancellationToken)
    {
        var trendData = new List<TrendDataPoint>();
        var currentDate = dateRange.StartDate.Date;
        
        while (currentDate <= dateRange.EndDate.Date)
        {
            var activeUsers = await _analyticsRepository.GetActiveUsersCountAsync(
                currentDate, 
                currentDate.AddDays(1).AddSeconds(-1), 
                cancellationToken);
            
            trendData.Add(new TrendDataPoint
            {
                Timestamp = currentDate,
                Value = activeUsers
            });
            
            currentDate = currentDate.AddDays(1);
        }
        
        return trendData;
    }

    private async Task<List<UserSegmentActivity>> GetSegmentActivitiesAsync(
        List<Domain.Entities.User.CustomerUser> users, 
        DateTimeRange dateRange, 
        CancellationToken cancellationToken)
    {
        var segments = new List<UserSegmentActivity>();
        
        // 按活跃状态分段
        var activeSegmentUsers = users.Where(u => u.IsActive).ToList();
        var inactiveSegmentUsers = users.Where(u => !u.IsActive).ToList();
        
        if (activeSegmentUsers.Any())
        {
            var activeUserIds = activeSegmentUsers.Select(u => u.Id).ToList();
            var activeCount = await _analyticsRepository.GetCustomerUsersQuery()
                .Where(u => activeUserIds.Contains(u.Id) && 
                           u.LastLoginAt.HasValue && 
                           u.LastLoginAt.Value >= dateRange.StartDate && 
                           u.LastLoginAt.Value <= dateRange.EndDate)
                .CountAsync(cancellationToken);
            
            // 计算活跃用户的会话统计
            var activeUserConversations = await _analyticsRepository.GetConversationsQuery()
                .Where(c => activeUserIds.Contains(c.CustomerUserId) && 
                           c.CreatedAt >= dateRange.StartDate && 
                           c.CreatedAt <= dateRange.EndDate)
                .ToListAsync(cancellationToken);
            
            var avgSessionsPerActiveUser = activeSegmentUsers.Count > 0 
                ? (double)activeUserConversations.Count / activeSegmentUsers.Count 
                : 0;
            
            var avgSessionDurationActive = activeUserConversations.Any() 
                ? activeUserConversations.Average(c => (c.UpdatedAt - c.CreatedAt).TotalMinutes) 
                : 0;
            
            segments.Add(new UserSegmentActivity
            {
                Segment = "Active Users",
                UserCount = activeSegmentUsers.Count,
                AverageSessionsPerUser = Math.Round(avgSessionsPerActiveUser, 2),
                AverageSessionDuration = Math.Round(avgSessionDurationActive, 2),
                EngagementScore = activeSegmentUsers.Count > 0 ? (double)activeCount / activeSegmentUsers.Count * 100 : 0
            });
        }
        
        if (inactiveSegmentUsers.Any())
        {
            var inactiveUserIds = inactiveSegmentUsers.Select(u => u.Id).ToList();
            var inactiveCount = await _analyticsRepository.GetCustomerUsersQuery()
                .Where(u => inactiveUserIds.Contains(u.Id) && 
                           u.LastLoginAt.HasValue && 
                           u.LastLoginAt.Value >= dateRange.StartDate && 
                           u.LastLoginAt.Value <= dateRange.EndDate)
                .CountAsync(cancellationToken);
            
            // 计算不活跃用户的会话统计
            var inactiveUserConversations = await _analyticsRepository.GetConversationsQuery()
                .Where(c => inactiveUserIds.Contains(c.CustomerUserId) && 
                           c.CreatedAt >= dateRange.StartDate && 
                           c.CreatedAt <= dateRange.EndDate)
                .ToListAsync(cancellationToken);
            
            var avgSessionsPerInactiveUser = inactiveSegmentUsers.Count > 0 
                ? (double)inactiveUserConversations.Count / inactiveSegmentUsers.Count 
                : 0;
            
            var avgSessionDurationInactive = inactiveUserConversations.Any() 
                ? inactiveUserConversations.Average(c => (c.UpdatedAt - c.CreatedAt).TotalMinutes) 
                : 0;
            
            segments.Add(new UserSegmentActivity
            {
                Segment = "Inactive Users",
                UserCount = inactiveSegmentUsers.Count,
                AverageSessionsPerUser = Math.Round(avgSessionsPerInactiveUser, 2),
                AverageSessionDuration = Math.Round(avgSessionDurationInactive, 2),
                EngagementScore = inactiveSegmentUsers.Count > 0 ? (double)inactiveCount / inactiveSegmentUsers.Count * 100 : 0
            });
        }
        
        return segments;
    }

    // CreateComparison method already exists in the class

    private async Task<LTVPrediction> CalculateLTVPredictionAsync(
        Dictionary<Guid, List<Domain.Entities.Payment.Order>> userOrders,
        CancellationToken cancellationToken)
    {
        // 选择有足够数据的用户进行预测
        var usersWithOrders = userOrders
            .Where(kvp => kvp.Value.Any())
            .ToList();

        if (!usersWithOrders.Any())
        {
            return new LTVPrediction
            {
                Predicted3MonthLTV = 0,
                Predicted6MonthLTV = 0,
                Predicted12MonthLTV = 0,
                ConfidenceLevel = 0,
                Model = "Insufficient Data"
            };
        }

        // 使用预测服务进行综合预测
        var predictions = new List<LTVPrediction>();
        var userIds = usersWithOrders.Select(u => u.Key).ToList();
        
        // 获取用户相关数据
        var users = await _analyticsRepository.GetCustomerUsersQuery()
            .Where(u => userIds.Contains(u.Id))
            .ToListAsync(cancellationToken);
            
        var subscriptions = await _analyticsRepository.GetSubscriptionsQuery()
            .Where(s => userIds.Contains(s.CustomerUserId))
            .ToListAsync(cancellationToken);
            
        var conversations = await _analyticsRepository.GetConversationsQuery()
            .Where(c => userIds.Contains(c.CustomerUserId))
            .ToListAsync(cancellationToken);
            
        var subscriptionIds = subscriptions.Select(s => s.Id).ToList();
        var tokenUsages = await _analyticsRepository.GetTokenUsagesQuery()
            .Where(t => subscriptionIds.Contains(t.SubscriptionId))
            .ToListAsync(cancellationToken);

        // 为每个用户计算LTV预测
        foreach (var userOrderPair in usersWithOrders.Take(100)) // 限制处理数量以提高性能
        {
            var userId = userOrderPair.Key;
            var orders = userOrderPair.Value;
            var user = users.FirstOrDefault(u => u.Id == userId);
            
            if (user == null) continue;
            
            var userSubscriptions = subscriptions.Where(s => s.CustomerUserId == userId).ToList();
            var userConversations = conversations.Where(c => c.CustomerUserId == userId).ToList();
            var userSubscriptionIds = userSubscriptions.Select(s => s.Id).ToList();
            var userTokenUsages = tokenUsages.Where(t => userSubscriptionIds.Contains(t.SubscriptionId)).ToList();
            
            // 构建历史数据
            var historicalData = new UserLTVHistoricalData
            {
                Orders = orders.Select(o => new OrderData
                {
                    OrderDate = o.CreatedAt,
                    Amount = o.Amount.Amount,
                    ProductType = o.Type.ToString()
                }).ToList(),
                Subscriptions = userSubscriptions.Select(s => new SubscriptionData
                {
                    StartDate = s.CreatedAt,
                    EndDate = s.CancellationDate,
                    MonthlyAmount = GetSubscriptionMonthlyAmount(s.Plan.Tier),
                    PlanTier = s.Plan.Tier.ToString()
                }).ToList(),
                FirstActivityDate = user.CreatedAt,
                TotalConversations = userConversations.Count,
                TotalTokensUsed = userTokenUsages.Sum(t => t.Tokens)
            };
            
            // 使用预测服务
            var prediction = await _predictiveAnalyticsService.PredictUserLTVAsync(
                userId, 
                historicalData, 
                cancellationToken);
                
            predictions.Add(prediction);
        }

        // 聚合所有用户的预测结果
        if (predictions.Any())
        {
            return new LTVPrediction
            {
                Predicted3MonthLTV = predictions.Average(p => p.Predicted3MonthLTV),
                Predicted6MonthLTV = predictions.Average(p => p.Predicted6MonthLTV),
                Predicted12MonthLTV = predictions.Average(p => p.Predicted12MonthLTV),
                ConfidenceLevel = predictions.Average(p => p.ConfidenceLevel),
                Model = "Advanced Predictive Analytics"
            };
        }

        // 如果预测服务失败，使用简单方法
        return CalculateSimpleLTVPrediction(usersWithOrders);
    }
    
    private LTVPrediction CalculateSimpleLTVPrediction(List<KeyValuePair<Guid, List<Domain.Entities.Payment.Order>>> usersWithOrders)
    {
        var userMetrics = usersWithOrders.Select(kvp =>
        {
            var orders = kvp.Value.OrderBy(o => o.CreatedAt).ToList();
            var totalValue = orders.Sum(o => o.Amount.Amount);
            
            if (orders.Count < 2)
                return new { MonthlyValue = totalValue, IsValid = false };
                
            var firstOrder = orders.First();
            var lastOrder = orders.Last();
            var months = Math.Max(1, (lastOrder.CreatedAt - firstOrder.CreatedAt).TotalDays / 30);
            var monthlyValue = totalValue / (decimal)months;
            
            return new { MonthlyValue = monthlyValue, IsValid = true };
        })
        .Where(m => m.IsValid)
        .ToList();

        if (!userMetrics.Any())
        {
            return new LTVPrediction
            {
                Predicted3MonthLTV = 0,
                Predicted6MonthLTV = 0,
                Predicted12MonthLTV = 0,
                ConfidenceLevel = 0,
                Model = "Insufficient Data"
            };
        }

        var avgMonthlyValue = userMetrics.Average(m => (double)m.MonthlyValue);
        
        return new LTVPrediction
        {
            Predicted3MonthLTV = avgMonthlyValue * 3,
            Predicted6MonthLTV = avgMonthlyValue * 6,
            Predicted12MonthLTV = avgMonthlyValue * 12,
            ConfidenceLevel = 0.5,
            Model = "Simple Average Model"
        };
    }
    
    private decimal GetSubscriptionMonthlyAmount(SubscriptionTier tier)
    {
        return tier switch
        {
            SubscriptionTier.Free => 0,
            SubscriptionTier.Basic => _pricingOptions.BasicPrice,
            SubscriptionTier.Pro => _pricingOptions.ProPrice,
            SubscriptionTier.Ultra => _pricingOptions.UltraPrice,
            _ => 0
        };
    }

    private async Task<List<LTVSegment>> CalculateLTVBySegmentAsync(
        Dictionary<Guid, List<Domain.Entities.Payment.Order>> userOrders,
        List<Domain.Entities.Subscription.Subscription> subscriptions,
        CancellationToken cancellationToken)
    {
        var segments = new List<LTVSegment>();

        // 按订阅层级分组
        var subscriptionsByUser = subscriptions
            .Where(s => s.Status == SubscriptionStatus.Active)
            .ToDictionary(s => s.CustomerUserId, s => s.Plan.Tier);

        var tiers = Enum.GetValues<SubscriptionTier>();
        
        foreach (var tier in tiers)
        {
            var tierUsers = subscriptionsByUser
                .Where(kvp => kvp.Value == tier)
                .Select(kvp => kvp.Key)
                .ToList();

            if (tierUsers.Any())
            {
                var tierOrders = userOrders
                    .Where(kvp => tierUsers.Contains(kvp.Key))
                    .SelectMany(kvp => kvp.Value)
                    .ToList();

                var totalValue = tierOrders.Sum(o => o.Amount.Amount);
                var avgValue = tierUsers.Count > 0 ? totalValue / tierUsers.Count : 0;

                segments.Add(new LTVSegment
                {
                    Segment = tier.ToString(),
                    UserCount = tierUsers.Count,
                    AverageLTV = (double)avgValue,
                    TotalValue = (double)totalValue,
                    ValueContribution = 0 // Will be calculated later
                });
            }
        }

        return segments.OrderByDescending(s => s.AverageLTV).ToList();
    }

    private async Task<List<TrendDataPoint>> CalculateLTVTrendAsync(
        List<Domain.Entities.Payment.Order> orders,
        CancellationToken cancellationToken)
    {
        var trend = new List<TrendDataPoint>();
        
        if (!orders.Any())
            return trend;

        // 按月分组计算LTV趋势
        var monthlyData = orders
            .Where(o => o.Status == OrderStatus.Paid)
            .GroupBy(o => new { o.CreatedAt.Year, o.CreatedAt.Month })
            .OrderBy(g => g.Key.Year).ThenBy(g => g.Key.Month)
            .Select(g => new
            {
                Date = new DateTime(g.Key.Year, g.Key.Month, 1),
                Revenue = g.Sum(o => o.Amount.Amount),
                UserCount = g.Select(o => o.CustomerUserId).Distinct().Count()
            })
            .ToList();

        foreach (var month in monthlyData)
        {
            var avgLTV = month.UserCount > 0 ? (double)month.Revenue / month.UserCount : 0;
            
            trend.Add(new TrendDataPoint
            {
                Timestamp = month.Date,
                Value = avgLTV,
                Label = month.Date.ToString("yyyy-MM"),
                Metadata = new Dictionary<string, object>
                {
                    { "revenue", month.Revenue },
                    { "userCount", month.UserCount }
                }
            });
        }

        return trend;
    }

    private async Task<Dictionary<int, double>> CalculateLTVByUserAgeAsync(
        List<Domain.Entities.User.CustomerUser> users,
        Dictionary<Guid, List<Domain.Entities.Payment.Order>> userOrders,
        CancellationToken cancellationToken)
    {
        var ltvByAge = new Dictionary<int, double>();
        
        // 计算用户账龄（注册时长）
        var now = DateTime.UtcNow;
        var usersByAge = users
            .Select(u => new
            {
                UserId = u.Id,
                AgeInDays = (now - u.CreatedAt).TotalDays,
                AgeInMonths = (int)((now - u.CreatedAt).TotalDays / 30)
            })
            .GroupBy(u => u.AgeInMonths)
            .ToList();

        foreach (var ageGroup in usersByAge)
        {
            var userIds = ageGroup.Select(u => u.UserId).ToList();
            var groupOrders = userOrders
                .Where(kvp => userIds.Contains(kvp.Key))
                .SelectMany(kvp => kvp.Value)
                .ToList();

            if (groupOrders.Any())
            {
                var totalValue = groupOrders.Sum(o => o.Amount.Amount);
                var avgLTV = userIds.Count > 0 ? (double)totalValue / userIds.Count : 0;
                
                ltvByAge[ageGroup.Key] = avgLTV;
            }
        }

        return ltvByAge.OrderBy(kvp => kvp.Key).ToDictionary(kvp => kvp.Key, kvp => kvp.Value);
    }

    private async Task<string> GetAgentCreatorNameAsync(Guid creatorId, CancellationToken cancellationToken)
    {
        var creator = await _analyticsRepository.GetCustomerUsersQuery()
            .Where(u => u.Id == creatorId)
            .Select(u => u.Nickname ?? u.Username)
            .FirstOrDefaultAsync(cancellationToken);
        
        return creator ?? "Unknown";
    }

    private async Task<List<TrendDataPoint>> GetAgentUsageTrendAsync(
        Guid agentId,
        DateTimeRange dateRange,
        CancellationToken cancellationToken)
    {
        var trend = new List<TrendDataPoint>();
        
        // 获取该Agent的使用数据
        var dailyUsage = await _analyticsRepository.GetConversationsQuery()
            .Where(c => c.AgentId == agentId && 
                       c.CreatedAt >= dateRange.StartDate && 
                       c.CreatedAt <= dateRange.EndDate)
            .GroupBy(c => c.CreatedAt.Date)
            .Select(g => new
            {
                Date = g.Key,
                ConversationCount = g.Count(),
                UserCount = g.Select(c => c.CustomerUserId).Distinct().Count()
            })
            .OrderBy(d => d.Date)
            .ToListAsync(cancellationToken);

        foreach (var day in dailyUsage)
        {
            trend.Add(new TrendDataPoint
            {
                Timestamp = day.Date,
                Value = day.ConversationCount,
                Label = day.Date.ToString("yyyy-MM-dd"),
                Metadata = new Dictionary<string, object>
                {
                    { "userCount", day.UserCount }
                }
            });
        }

        return trend;
    }

    private List<string> ExtractKeywords(IEnumerable<string> texts)
    {
        // 简单的关键词提取实现
        var allWords = new List<string>();
        
        foreach (var text in texts)
        {
            // 分词（简单实现，实际应使用NLP库）
            var words = text.Split(new[] { ' ', ',', '.', '!', '?', '，', '。', '！', '？' }, 
                StringSplitOptions.RemoveEmptyEntries)
                .Where(w => w.Length > 2) // 过滤短词
                .Select(w => w.ToLower());
            
            allWords.AddRange(words);
        }

        // 统计词频并返回前10个高频词
        return allWords
            .GroupBy(w => w)
            .OrderByDescending(g => g.Count())
            .Take(10)
            .Select(g => g.Key)
            .ToList();
    }

    private async Task<string?> GetAgentNameAsync(Guid agentId, CancellationToken cancellationToken)
    {
        return await _analyticsRepository.GetAgentsQuery()
            .Where(a => a.Id == agentId)
            .Select(a => a.Name)
            .FirstOrDefaultAsync(cancellationToken);
    }

    private async Task<List<TrendDataPoint>> CalculateRatingTrendAsync(
        List<Domain.Entities.Agent.AgentRating> reviews,
        CancellationToken cancellationToken)
    {
        var trend = new List<TrendDataPoint>();
        
        if (!reviews.Any())
            return trend;

        // 按周分组计算平均评分
        var weeklyRatings = reviews
            .GroupBy(r => new { Year = r.CreatedAt.Year, Week = r.CreatedAt.DayOfYear / 7 })
            .OrderBy(g => g.Key.Year).ThenBy(g => g.Key.Week)
            .Select(g => new
            {
                WeekStart = GetWeekStartDate(g.Key.Year, g.Key.Week),
                AverageRating = g.Average(r => r.Score),
                Count = g.Count()
            })
            .ToList();

        foreach (var week in weeklyRatings)
        {
            trend.Add(new TrendDataPoint
            {
                Timestamp = week.WeekStart,
                Value = week.AverageRating,
                Label = week.WeekStart.ToString("yyyy-MM-dd"),
                Metadata = new Dictionary<string, object>
                {
                    { "ratingCount", week.Count }
                }
            });
        }

        return trend;
    }

    private async Task<List<RatingBySegment>> CalculateSegmentRatingsAsync(
        List<Domain.Entities.Agent.AgentRating> reviews,
        CancellationToken cancellationToken)
    {
        var segments = new List<RatingBySegment>();
        
        // 按评分区间分组
        var ratingRanges = new[]
        {
            new { Name = "Excellent (5)", Min = 5, Max = 5 },
            new { Name = "Good (4)", Min = 4, Max = 4 },
            new { Name = "Average (3)", Min = 3, Max = 3 },
            new { Name = "Poor (1-2)", Min = 1, Max = 2 }
        };

        foreach (var range in ratingRanges)
        {
            var segmentReviews = reviews
                .Where(r => r.Score >= range.Min && r.Score <= range.Max)
                .ToList();

            if (segmentReviews.Any())
            {
                segments.Add(new RatingBySegment
                {
                    Segment = range.Name,
                    AverageRating = segmentReviews.Average(r => r.Score),
                    RatingCount = segmentReviews.Count
                });
            }
        }

        return segments;
    }

    private double CalculateStandardDeviation(List<double> values)
    {
        if (values.Count < 2)
            return 0;

        double avg = values.Average();
        double sum = values.Sum(d => Math.Pow(d - avg, 2));
        return Math.Sqrt(sum / (values.Count - 1));
    }

    private DateTime GetWeekStartDate(int year, int week)
    {
        var jan1 = new DateTime(year, 1, 1);
        var daysOffset = DayOfWeek.Monday - jan1.DayOfWeek;
        var firstMonday = jan1.AddDays(daysOffset);
        
        var cal = System.Globalization.CultureInfo.CurrentCulture.Calendar;
        var firstWeek = cal.GetWeekOfYear(firstMonday, 
            System.Globalization.CalendarWeekRule.FirstFourDayWeek, DayOfWeek.Monday);
        
        var weekNum = week;
        if (firstWeek <= 1)
        {
            weekNum -= 1;
        }
        
        return firstMonday.AddDays(weekNum * 7);
    }

    private async Task<double> CalculatePlanChurnRateAsync(string planName, DateTimeRange? dateRange, CancellationToken cancellationToken)
    {
        var startDate = dateRange?.StartDate ?? DateTime.UtcNow.AddMonths(-3);
        var endDate = dateRange?.EndDate ?? DateTime.UtcNow;
        
        // 获取该套餐在期初的活跃订阅数
        var startActiveCount = await _analyticsRepository.GetSubscriptionsQuery()
            .Where(s => s.Plan.Name == planName && 
                       s.Status == SubscriptionStatus.Active && 
                       s.CreatedAt <= startDate)
            .CountAsync(cancellationToken);
        
        if (startActiveCount == 0)
            return 0;
        
        // 获取期间内取消的订阅数
        var churnedCount = await _analyticsRepository.GetSubscriptionsQuery()
            .Where(s => s.Plan.Name == planName && 
                       s.Status == SubscriptionStatus.Cancelled &&
                       s.UpdatedAt >= startDate && 
                       s.UpdatedAt <= endDate)
            .CountAsync(cancellationToken);
        
        return (double)churnedCount / startActiveCount * 100;
    }

    private async Task<double> CalculatePlanUpgradeRateAsync(string planName, DateTimeRange? dateRange, CancellationToken cancellationToken)
    {
        var startDate = dateRange?.StartDate ?? DateTime.UtcNow.AddMonths(-3);
        var endDate = dateRange?.EndDate ?? DateTime.UtcNow;
        
        // 获取期间内从该套餐升级的数量
        var upgradedCount = await _analyticsRepository.GetOrdersQuery()
            .Where(o => o.Type == OrderType.Subscription &&
                       o.Status == OrderStatus.Paid &&
                       o.CreatedAt >= startDate && 
                       o.CreatedAt <= endDate &&
                       o.Metadata != null && 
                       o.Metadata.ContainsKey("from_plan") &&
                       o.Metadata["from_plan"].ToString() == planName)
            .CountAsync(cancellationToken);
        
        var totalPlanUsers = await _analyticsRepository.GetSubscriptionsQuery()
            .Where(s => s.Plan.Name == planName && s.Status == SubscriptionStatus.Active)
            .CountAsync(cancellationToken);
        
        return totalPlanUsers > 0 ? (double)upgradedCount / totalPlanUsers * 100 : 0;
    }

    private async Task<double> CalculatePlanDowngradeRateAsync(string planName, DateTimeRange? dateRange, CancellationToken cancellationToken)
    {
        var startDate = dateRange?.StartDate ?? DateTime.UtcNow.AddMonths(-3);
        var endDate = dateRange?.EndDate ?? DateTime.UtcNow;
        
        // 获取期间内从该套餐降级的数量
        var downgradedCount = await _analyticsRepository.GetOrdersQuery()
            .Where(o => o.Type == OrderType.Subscription &&
                       o.Status == OrderStatus.Paid &&
                       o.CreatedAt >= startDate && 
                       o.CreatedAt <= endDate &&
                       o.Metadata != null && 
                       o.Metadata.ContainsKey("from_plan") &&
                       o.Metadata["from_plan"].ToString() == planName &&
                       o.Metadata.ContainsKey("is_downgrade") &&
                       (bool)o.Metadata["is_downgrade"] == true)
            .CountAsync(cancellationToken);
        
        var totalPlanUsers = await _analyticsRepository.GetSubscriptionsQuery()
            .Where(s => s.Plan.Name == planName && s.Status == SubscriptionStatus.Active)
            .CountAsync(cancellationToken);
        
        return totalPlanUsers > 0 ? (double)downgradedCount / totalPlanUsers * 100 : 0;
    }

    private async Task<List<TrendDataPoint>> CalculateChurnTrendAsync(
        List<Domain.Entities.Subscription.Subscription> subscriptions, 
        DateTimeRange? dateRange, 
        CancellationToken cancellationToken)
    {
        var trend = new List<TrendDataPoint>();
        var startDate = dateRange?.StartDate ?? DateTime.UtcNow.AddMonths(-6);
        var endDate = dateRange?.EndDate ?? DateTime.UtcNow;
        
        // 按月计算流失率趋势
        var currentDate = new DateTime(startDate.Year, startDate.Month, 1);
        while (currentDate <= endDate)
        {
            var monthEnd = currentDate.AddMonths(1).AddDays(-1);
            
            // 月初活跃订阅数
            var monthStartActive = subscriptions
                .Count(s => s.Status == SubscriptionStatus.Active && s.CreatedAt <= currentDate);
            
            // 当月流失数
            var monthChurned = subscriptions
                .Count(s => s.Status == SubscriptionStatus.Cancelled &&
                           s.UpdatedAt >= currentDate && 
                           s.UpdatedAt <= monthEnd);
            
            var churnRate = monthStartActive > 0 ? (double)monthChurned / monthStartActive * 100 : 0;
            
            trend.Add(new TrendDataPoint
            {
                Timestamp = currentDate,
                Value = churnRate,
                Label = currentDate.ToString("yyyy-MM")
            });
            
            currentDate = currentDate.AddMonths(1);
        }
        
        return trend;
    }

    private async Task<double> CalculateNetRevenueRetentionAsync(
        List<Domain.Entities.Subscription.Subscription> subscriptions,
        List<Domain.Entities.Payment.Order>? orders,
        CancellationToken cancellationToken)
    {
        var now = DateTime.UtcNow;
        var yearAgo = now.AddYears(-1);
        
        // 获取一年前的活跃订阅收入
        var yearAgoActiveSubscriptions = subscriptions
            .Where(s => s.Status == SubscriptionStatus.Active && s.CreatedAt <= yearAgo)
            .ToList();
        
        var yearAgoRevenue = yearAgoActiveSubscriptions.Sum(s => s.Plan.Price.Amount);
        
        if (yearAgoRevenue == 0)
            return 100; // 如果没有历史数据，返回100%
        
        // 计算这些客户现在的收入（包括升级）
        var customerIds = yearAgoActiveSubscriptions.Select(s => s.CustomerUserId).Distinct().ToList();
        var currentRevenue = subscriptions
            .Where(s => customerIds.Contains(s.CustomerUserId) && 
                       s.Status == SubscriptionStatus.Active)
            .Sum(s => s.Plan.Price.Amount);
        
        return (double)currentRevenue / (double)yearAgoRevenue * 100;
    }

    private async Task<double> CalculateGrossRevenueRetentionAsync(
        List<Domain.Entities.Subscription.Subscription> subscriptions,
        List<Domain.Entities.Payment.Order>? orders,
        CancellationToken cancellationToken)
    {
        var now = DateTime.UtcNow;
        var yearAgo = now.AddYears(-1);
        
        // 获取一年前的活跃订阅收入
        var yearAgoActiveSubscriptions = subscriptions
            .Where(s => s.Status == SubscriptionStatus.Active && s.CreatedAt <= yearAgo)
            .ToList();
        
        var yearAgoRevenue = yearAgoActiveSubscriptions.Sum(s => s.Plan.Price.Amount);
        
        if (yearAgoRevenue == 0)
            return 100;
        
        // 计算这些客户现在的收入（不包括升级，只计算留存）
        var customerIds = yearAgoActiveSubscriptions.Select(s => s.CustomerUserId).Distinct().ToList();
        var currentRevenue = subscriptions
            .Where(s => customerIds.Contains(s.CustomerUserId) && 
                       s.Status == SubscriptionStatus.Active &&
                       s.Plan.Price.Amount <= yearAgoActiveSubscriptions
                           .First(old => old.CustomerUserId == s.CustomerUserId).Plan.Price.Amount)
            .Sum(s => s.Plan.Price.Amount);
        
        return (double)currentRevenue / (double)yearAgoRevenue * 100;
    }

    private async Task<double> CalculateCustomerAcquisitionCostAsync(
        DateTimeRange dateRange,
        CancellationToken cancellationToken)
    {
        // 获取期间内的新客户数
        var newCustomers = await _analyticsRepository.GetCustomerUsersQuery()
            .Where(u => u.CreatedAt >= dateRange.StartDate && u.CreatedAt <= dateRange.EndDate)
            .CountAsync(cancellationToken);
        
        if (newCustomers == 0)
            return 0;
        
        // 使用配置的营销成本参数计算CAC
        var revenue = await _analyticsRepository.GetOrdersQuery()
            .Where(o => o.Status == OrderStatus.Paid &&
                       o.CreatedAt >= dateRange.StartDate && 
                       o.CreatedAt <= dateRange.EndDate)
            .SumAsync(o => o.Amount.Amount, cancellationToken);
        
        // 如果有收入，使用实际营销预算比例
        // 否则使用基础CAC
        if (revenue > 0)
        {
            var marketingCost = revenue * _marketingCostOptions.MarketingBudgetRatio;
            return (double)marketingCost / newCustomers;
        }
        
        return (double)_marketingCostOptions.BaseCustomerAcquisitionCost;
    }

    private async Task<List<TrendDataPoint>> GetAgentRatingTrendAsync(
        Guid agentId,
        DateTimeRange dateRange,
        CancellationToken cancellationToken)
    {
        var trend = new List<TrendDataPoint>();
        
        // 获取Agent的评分数据
        // AgentRating currently tracks user ratings, not agent ratings
        // This method returns an empty trend until the domain model is updated
        var ratings = new List<Domain.Entities.Agent.AgentRating>();
        
        if (!ratings.Any())
            return trend;
        
        // 按周分组计算平均评分
        var weeklyRatings = ratings
            .GroupBy(r => new { Year = r.CreatedAt.Year, Week = r.CreatedAt.DayOfYear / 7 })
            .Select(g => new
            {
                WeekStart = GetWeekStartDate(g.Key.Year, g.Key.Week),
                AverageRating = g.Average(r => r.Score),
                Count = g.Count()
            })
            .OrderBy(w => w.WeekStart)
            .ToList();
        
        foreach (var week in weeklyRatings)
        {
            trend.Add(new TrendDataPoint
            {
                Timestamp = week.WeekStart,
                Value = week.AverageRating,
                Label = week.WeekStart.ToString("yyyy-MM-dd"),
                Metadata = new Dictionary<string, object> { { "count", week.Count } }
            });
        }
        
        return trend;
    }

    private DateTime GetNextPeriodStart(DateTime current, TrendGranularity granularity)
    {
        return granularity switch
        {
            TrendGranularity.Hourly => current.AddHours(1),
            TrendGranularity.Daily => current.AddDays(1),
            TrendGranularity.Weekly => current.AddDays(7),
            TrendGranularity.Monthly => current.AddMonths(1),
            TrendGranularity.Quarterly => current.AddMonths(3),
            TrendGranularity.Yearly => current.AddYears(1),
            _ => current.AddDays(1)
        };
    }

    private async Task<double> CalculateUserRetentionRateAsync(DateTimeRange dateRange, CancellationToken cancellationToken)
    {
        // 获取30天前的活跃用户
        var thirtyDaysAgo = DateTime.UtcNow.AddDays(-30);
        var sixtyDaysAgo = DateTime.UtcNow.AddDays(-60);
        
        var usersFromPreviousPeriod = await _analyticsRepository.GetCustomerUsersQuery()
            .Where(u => u.CreatedAt >= sixtyDaysAgo && u.CreatedAt <= thirtyDaysAgo)
            .Select(u => u.Id)
            .ToListAsync(cancellationToken);
        
        if (!usersFromPreviousPeriod.Any())
            return 100; // 没有历史用户，返回100%
        
        // 计算这些用户在最近30天内的活跃数
        var retainedUsers = await _analyticsRepository.GetCustomerUsersQuery()
            .Where(u => usersFromPreviousPeriod.Contains(u.Id) && 
                       u.LastLoginAt.HasValue && 
                       u.LastLoginAt.Value >= thirtyDaysAgo)
            .CountAsync(cancellationToken);
        
        return (double)retainedUsers / usersFromPreviousPeriod.Count * 100;
    }

    private async Task<double> CalculateOverallChurnRateAsync(DateTimeRange dateRange, CancellationToken cancellationToken)
    {
        // 获取期初的活跃订阅数
        var startActiveCount = await _analyticsRepository.GetSubscriptionsQuery()
            .Where(s => s.Status == SubscriptionStatus.Active && s.CreatedAt <= dateRange.StartDate)
            .CountAsync(cancellationToken);
        
        if (startActiveCount == 0)
            return 0;
        
        // 获取期间内流失的订阅数
        var churnedCount = await _analyticsRepository.GetSubscriptionsQuery()
            .Where(s => s.Status == SubscriptionStatus.Cancelled &&
                       s.UpdatedAt >= dateRange.StartDate && 
                       s.UpdatedAt <= dateRange.EndDate)
            .CountAsync(cancellationToken);
        
        return (double)churnedCount / startActiveCount * 100;
    }

    private async Task<double> CalculateAverageSessionDurationAsync(DateTimeRange dateRange, CancellationToken cancellationToken)
    {
        // 获取期间内的所有对话
        var conversations = await _analyticsRepository.GetConversationsQuery()
            .Where(c => c.CreatedAt >= dateRange.StartDate && c.CreatedAt <= dateRange.EndDate)
            .Select(c => new { c.CreatedAt, c.UpdatedAt })
            .ToListAsync(cancellationToken);
        
        if (!conversations.Any())
            return 0;
        
        // 计算平均会话时长（分钟）
        var totalDuration = conversations.Sum(c => (c.UpdatedAt - c.CreatedAt).TotalMinutes);
        return totalDuration / conversations.Count;
    }
    
    private async Task<List<TrendDataPoint>> BuildGrowthTrendAsync(DateTimeRange dateRange, CancellationToken cancellationToken)
    {
        var trendPoints = new List<TrendDataPoint>();
        var daysInRange = Math.Min((dateRange.EndDate - dateRange.StartDate).Days, 30);
        
        for (int i = 0; i <= daysInRange; i++)
        {
            var date = dateRange.StartDate.AddDays(i);
            
            // 获取当天的各项指标
            var dayUsers = await _analyticsRepository.GetCustomerUsersQuery()
                .Where(u => u.CreatedAt.Date == date.Date)
                .CountAsync(cancellationToken);
            
            var dayRevenue = await _analyticsRepository.GetOrdersQuery()
                .Where(o => o.CreatedAt.Date == date.Date && o.Status == OrderStatus.Paid)
                .SumAsync(o => (decimal?)o.Amount.Amount, cancellationToken) ?? 0;
            
            var dayConversations = await _analyticsRepository.GetConversationsQuery()
                .Where(c => c.CreatedAt.Date == date.Date)
                .CountAsync(cancellationToken);
            
            // 综合增长指数（标准化后的加权平均）
            var userGrowthIndex = dayUsers * 10; // 权重调整
            var revenueGrowthIndex = (double)dayRevenue / 100; // 标准化
            var conversationGrowthIndex = dayConversations * 2; // 权重调整
            
            var growthIndex = (userGrowthIndex + revenueGrowthIndex + conversationGrowthIndex) / 3;
            
            trendPoints.Add(new TrendDataPoint
            {
                Timestamp = date,
                Value = Math.Round(growthIndex, 2),
                Label = date.ToString("yyyy-MM-dd")
            });
        }
        
        return trendPoints;
    }

    private double CalculatePlanChurnRate(List<Domain.Entities.Subscription.Subscription> subscriptions, SubscriptionTier tier, DateTime startDate, DateTime endDate)
    {
        var planSubscriptions = subscriptions.Where(s => s.Plan?.Tier == tier).ToList();
        var startDateCount = planSubscriptions.Count(s => s.CreatedAt <= startDate && 
            (s.Status == SubscriptionStatus.Active || 
             (s.Status == SubscriptionStatus.Cancelled && s.UpdatedAt > startDate)));
        
        if (startDateCount == 0) return 0;
        
        var churnedCount = planSubscriptions.Count(s => 
            s.Status == SubscriptionStatus.Cancelled && 
            s.UpdatedAt >= startDate && 
            s.UpdatedAt <= endDate);
        
        return (double)churnedCount / startDateCount * 100;
    }
    
    private double CalculatePlanUpgradeRate(List<Domain.Entities.Subscription.Subscription> subscriptions, SubscriptionTier tier, DateTime startDate, DateTime endDate)
    {
        // 这里简化实现，实际应该跟踪订阅变更历史
        var planSubscriptions = subscriptions.Where(s => s.Plan?.Tier == tier).ToList();
        var totalCount = planSubscriptions.Count();
        
        if (totalCount == 0) return 0;
        
        // 估算升级率（实际应该基于订阅变更记录）
        return tier switch
        {
            SubscriptionTier.Free => 15.0,    // Free用户升级率较高
            SubscriptionTier.Basic => 10.0,   // Basic用户有一定升级率
            SubscriptionTier.Pro => 5.0,      // Pro用户升级到Ultra较少
            _ => 0.0
        };
    }
    
    private double CalculatePlanDowngradeRate(List<Domain.Entities.Subscription.Subscription> subscriptions, SubscriptionTier tier, DateTime startDate, DateTime endDate)
    {
        // 简化实现
        var planSubscriptions = subscriptions.Where(s => s.Plan?.Tier == tier).ToList();
        var totalCount = planSubscriptions.Count();
        
        if (totalCount == 0) return 0;
        
        // 估算降级率
        return tier switch
        {
            SubscriptionTier.Ultra => 8.0,    // Ultra用户可能降级
            SubscriptionTier.Pro => 5.0,      // Pro用户少量降级
            SubscriptionTier.Basic => 2.0,    // Basic用户很少降级
            _ => 0.0
        };
    }
    
    private SubscriptionHealth CalculateSubscriptionHealth(
        int activeSubscriptions, 
        int churnedSubscriptions, 
        decimal mrr,
        List<Domain.Entities.Subscription.Subscription> subscriptions,
        DateTime startDate,
        DateTime endDate)
    {
        // 计算净收入留存率 (NRR)
        var previousMRR = subscriptions
            .Where(s => s.CreatedAt <= startDate.AddMonths(-1) && s.Status == SubscriptionStatus.Active)
            .Sum(s => s.Plan?.Price.Amount ?? 0);
        
        var currentMRR = mrr;
        var nrr = previousMRR > 0 ? (double)(currentMRR / previousMRR) * 100 : 100;
        
        // 计算毛收入留存率 (GRR)
        var grr = activeSubscriptions > 0 && (activeSubscriptions + churnedSubscriptions) > 0
            ? (double)activeSubscriptions / (activeSubscriptions + churnedSubscriptions) * 100
            : 100;
        
        // 计算客户生命周期价值 (CLV)
        var avgSubscriptionLength = 12; // 假设平均12个月
        var avgMonthlyRevenue = activeSubscriptions > 0 ? (double)mrr / activeSubscriptions : 0;
        var clv = avgSubscriptionLength * avgMonthlyRevenue;
        
        // 客户获取成本 (CAC) - 简化计算
        var cac = 50.0; // 假设每个客户获取成本为50元
        
        // LTV/CAC比率
        var ltvCacRatio = cac > 0 ? clv / cac : 0;
        
        // 健康状态判断
        var healthStatus = "healthy";
        if (nrr < 90 || grr < 85 || ltvCacRatio < 3)
        {
            healthStatus = "warning";
        }
        if (nrr < 80 || grr < 75 || ltvCacRatio < 2)
        {
            healthStatus = "critical";
        }
        
        return new SubscriptionHealth
        {
            NetRevenueRetention = nrr,
            GrossRevenueRetention = grr,
            CustomerLifetimeValue = clv,
            CustomerAcquisitionCost = cac,
            LTVCACRatio = ltvCacRatio,
            HealthStatus = healthStatus
        };
    }
    
    private async Task<List<ChurnReason>> AnalyzeChurnReasons(
        List<Domain.Entities.Subscription.Subscription> subscriptions,
        DateTime startDate,
        DateTime endDate,
        CancellationToken cancellationToken)
    {
        var churnedSubscriptions = subscriptions
            .Where(s => s.Status == SubscriptionStatus.Cancelled && 
                       s.UpdatedAt >= startDate && 
                       s.UpdatedAt <= endDate)
            .ToList();
        
        var totalChurned = churnedSubscriptions.Count;
        if (totalChurned == 0)
        {
            return new List<ChurnReason>();
        }
        
        // 分析流失原因（基于套餐类型推测）
        var reasons = new List<ChurnReason>();
        
        // 价格敏感（主要是高级套餐流失）
        var highTierChurn = churnedSubscriptions.Count(s => 
            s.Plan?.Tier == SubscriptionTier.Pro || s.Plan?.Tier == SubscriptionTier.Ultra);
        if (highTierChurn > 0)
        {
            reasons.Add(new ChurnReason
            {
                Reason = "价格过高",
                Count = highTierChurn,
                Percentage = (double)highTierChurn / totalChurned * 100,
                RevenueImpact = churnedSubscriptions
                    .Where(s => s.Plan?.Tier == SubscriptionTier.Pro || s.Plan?.Tier == SubscriptionTier.Ultra)
                    .Sum(s => s.Plan?.Price.Amount ?? 0)
            });
        }
        
        // 功能不足（主要是基础套餐流失）
        var basicTierChurn = churnedSubscriptions.Count(s => s.Plan?.Tier == SubscriptionTier.Basic);
        if (basicTierChurn > 0)
        {
            reasons.Add(new ChurnReason
            {
                Reason = "功能限制",
                Count = basicTierChurn,
                Percentage = (double)basicTierChurn / totalChurned * 100,
                RevenueImpact = churnedSubscriptions
                    .Where(s => s.Plan?.Tier == SubscriptionTier.Basic)
                    .Sum(s => s.Plan?.Price.Amount ?? 0)
            });
        }
        
        // 使用率低（短期订阅流失）
        var shortTermChurn = churnedSubscriptions.Count(s => 
            (s.UpdatedAt - s.CreatedAt).TotalDays < 30);
        if (shortTermChurn > 0)
        {
            reasons.Add(new ChurnReason
            {
                Reason = "使用率低",
                Count = shortTermChurn,
                Percentage = (double)shortTermChurn / totalChurned * 100,
                RevenueImpact = churnedSubscriptions
                    .Where(s => (s.UpdatedAt - s.CreatedAt).TotalDays < 30)
                    .Sum(s => s.Plan?.Price.Amount ?? 0)
            });
        }
        
        // 竞争对手（剩余的归类为此）
        var otherChurn = totalChurned - reasons.Sum(r => r.Count);
        if (otherChurn > 0)
        {
            reasons.Add(new ChurnReason
            {
                Reason = "其他原因",
                Count = otherChurn,
                Percentage = (double)otherChurn / totalChurned * 100,
                RevenueImpact = churnedSubscriptions
                    .Skip(reasons.Sum(r => r.Count))
                    .Sum(s => s.Plan?.Price.Amount ?? 0)
            });
        }
        
        return reasons.OrderByDescending(r => r.Count).ToList();
    }

    #endregion
}