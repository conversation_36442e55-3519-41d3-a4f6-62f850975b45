using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using WhimLabAI.Abstractions.Application;
using WhimLabAI.Abstractions.Infrastructure;
using WhimLabAI.Domain.Repositories;
using WhimLabAI.Shared.Dtos.Finance;
using WhimLabAI.Shared.Enums;
using WhimLabAI.Shared.Results;

namespace WhimLabAI.Application.Services.Finance;

/// <summary>
/// 财务报表服务实现
/// </summary>
public class FinancialReportService : IFinancialReportService
{
    private readonly IUnitOfWork _unitOfWork;
    private readonly IAnalyticsRepository _analyticsRepository;
    private readonly ILogger<FinancialReportService> _logger;
    private readonly ICacheService _cacheService;
    private readonly ITokenMeteringService _tokenMeteringService;
    private static readonly TimeSpan CacheExpiry = TimeSpan.FromMinutes(15);

    public FinancialReportService(
        IUnitOfWork unitOfWork,
        IAnalyticsRepository analyticsRepository,
        ILogger<FinancialReportService> logger,
        ICacheService cacheService,
        ITokenMeteringService tokenMeteringService)
    {
        _unitOfWork = unitOfWork;
        _analyticsRepository = analyticsRepository;
        _logger = logger;
        _cacheService = cacheService;
        _tokenMeteringService = tokenMeteringService;
    }

    public async Task<Result<RevenueReportDto>> GetRevenueReportAsync(
        DateTime startDate, 
        DateTime endDate, 
        TimeGranularity granularity = TimeGranularity.Monthly,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var cacheKey = $"financial:revenue:{startDate:yyyyMMdd}:{endDate:yyyyMMdd}:{granularity}";
            var cached = await _cacheService.GetAsync<RevenueReportDto>(cacheKey, cancellationToken);
            if (cached != null)
            {
                return Result<RevenueReportDto>.Success(cached);
            }

            var report = new RevenueReportDto
            {
                StartDate = startDate,
                EndDate = endDate,
                Granularity = granularity,
                GeneratedAt = DateTime.UtcNow
            };

            // 获取订单数据
            var orders = await _unitOfWork.Orders.GetQueryable()
                .Where(o => o.Status == OrderStatus.Paid && 
                           o.PaidAt >= startDate && 
                           o.PaidAt <= endDate)
                .ToListAsync(cancellationToken);

            // 计算总收入
            report.TotalRevenue = orders.Sum(o => o.FinalAmount.Amount);
            report.SubscriptionRevenue = orders
                .Where(o => o.Type == OrderType.Subscription)
                .Sum(o => o.FinalAmount.Amount);
            report.TokenPackageRevenue = orders
                .Where(o => o.Type == OrderType.TokenPackage)
                .Sum(o => o.FinalAmount.Amount);
            report.OtherRevenue = report.TotalRevenue - report.SubscriptionRevenue - report.TokenPackageRevenue;

            // 获取客户数据
            var customerIds = orders.Select(o => o.CustomerUserId).Distinct().ToList();
            report.TotalCustomers = await _unitOfWork.CustomerUsers.GetQueryable().CountAsync(cancellationToken);
            report.PaidCustomers = customerIds.Count;
            
            // 计算ARPU和ARPPU
            report.AverageRevenuePerUser = report.TotalCustomers > 0 
                ? report.TotalRevenue / report.TotalCustomers 
                : 0;
            report.AverageRevenuePerPaidUser = report.PaidCustomers > 0 
                ? report.TotalRevenue / report.PaidCustomers 
                : 0;

            // 生成收入趋势
            report.RevenueTrend = await GenerateRevenueTrend(orders, startDate, endDate, granularity, cancellationToken);

            // 按套餐分析收入
            report.RevenueByPlan = await GenerateRevenueByPlan(orders, cancellationToken);

            // 按支付渠道分析收入
            report.RevenueByChannel = GenerateRevenueByChannel(orders);

            // 计算同比环比增长
            var (yoy, mom) = await CalculateGrowthRates(startDate, endDate, report.TotalRevenue, cancellationToken);
            report.YearOverYearGrowth = yoy;
            report.MonthOverMonthGrowth = mom;

            await _cacheService.SetAsync(cacheKey, report, CacheExpiry, cancellationToken);
            
            _logger.LogInformation("Generated revenue report for period {StartDate} to {EndDate}", startDate, endDate);
            return Result<RevenueReportDto>.Success(report);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error generating revenue report");
            return Result<RevenueReportDto>.Failure("REVENUE_REPORT_ERROR", "生成收入报表失败");
        }
    }

    public async Task<Result<CostAnalysisReportDto>> GetCostAnalysisReportAsync(
        DateTime startDate, 
        DateTime endDate, 
        TimeGranularity granularity = TimeGranularity.Monthly,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var cacheKey = $"financial:cost:{startDate:yyyyMMdd}:{endDate:yyyyMMdd}:{granularity}";
            var cached = await _cacheService.GetAsync<CostAnalysisReportDto>(cacheKey, cancellationToken);
            if (cached != null)
            {
                return Result<CostAnalysisReportDto>.Success(cached);
            }

            var report = new CostAnalysisReportDto
            {
                StartDate = startDate,
                EndDate = endDate,
                Granularity = granularity,
                GeneratedAt = DateTime.UtcNow
            };

            // 获取Token使用数据
            var tokenUsages = await _analyticsRepository.GetTokenUsagesQuery()
                .Where(t => t.UsedAt >= startDate && t.UsedAt <= endDate)
                .ToListAsync(cancellationToken);

            // 计算Token成本（假设每1000个token成本为0.02元）
            var totalTokens = tokenUsages.Sum(t => t.Tokens);
            report.TokenCost = totalTokens * 0.00002m; // 0.02元/1000 tokens
            
            // Token成本详情
            report.TokenCostDetail = new TokenCostDetail
            {
                TotalTokensUsed = totalTokens,
                TotalTokenCost = report.TokenCost,
                CostByModel = tokenUsages
                    .GroupBy(t => t.Model ?? "Unknown")
                    .Select(g => new TokenCostByModelItem
                    {
                        Model = g.Key,
                        TokensUsed = g.Sum(t => t.Tokens),
                        Cost = g.Sum(t => t.Tokens) * 0.00002m,
                        Percentage = totalTokens > 0 ? (double)g.Sum(t => t.Tokens) / totalTokens * 100 : 0
                    })
                    .OrderByDescending(x => x.TokensUsed)
                    .ToList(),
                CostByUsageType = tokenUsages
                    .GroupBy(t => t.Type.ToString())
                    .Select(g => new TokenCostByUsageTypeItem
                    {
                        UsageType = g.Key,
                        TokensUsed = g.Sum(t => t.Tokens),
                        Cost = g.Sum(t => t.Tokens) * 0.00002m,
                        Percentage = totalTokens > 0 ? (double)g.Sum(t => t.Tokens) / totalTokens * 100 : 0
                    })
                    .OrderByDescending(x => x.TokensUsed)
                    .ToList()
            };

            // 估算其他成本（实际项目中应从财务系统获取）
            report.OperationalCost = report.TokenCost * 0.3m; // 假设运营成本为Token成本的30%
            report.InfrastructureCost = report.TokenCost * 0.2m; // 假设基础设施成本为Token成本的20%
            report.MarketingCost = report.TokenCost * 0.1m; // 假设营销成本为Token成本的10%
            report.OtherCost = 0;

            report.TotalCost = report.TokenCost + report.OperationalCost + 
                              report.InfrastructureCost + report.MarketingCost + report.OtherCost;

            // 计算成本效率指标
            var userCount = await _unitOfWork.CustomerUsers.GetQueryable()
                .Where(u => u.CreatedAt <= endDate)
                .CountAsync(cancellationToken);
            var transactionCount = await _unitOfWork.Orders.GetQueryable()
                .Where(o => o.Status == OrderStatus.Paid && 
                           o.PaidAt >= startDate && 
                           o.PaidAt <= endDate)
                .CountAsync(cancellationToken);
            var conversationCount = await _unitOfWork.Conversations.GetQueryable()
                .Where(c => c.CreatedAt >= startDate && c.CreatedAt <= endDate)
                .CountAsync(cancellationToken);

            report.CostPerUser = userCount > 0 ? report.TotalCost / userCount : 0;
            report.CostPerTransaction = transactionCount > 0 ? report.TotalCost / transactionCount : 0;
            report.TokenCostPerConversation = conversationCount > 0 ? report.TokenCost / conversationCount : 0;

            // 生成成本趋势
            report.CostTrend = await GenerateCostTrend(tokenUsages, startDate, endDate, granularity);

            // 成本结构
            report.CostStructure = new List<CostStructureItem>
            {
                new() { CostType = "Token成本", Amount = report.TokenCost, 
                    Percentage = report.TotalCost > 0 ? (double)(report.TokenCost / report.TotalCost) * 100 : 0,
                    Description = "AI模型调用成本" },
                new() { CostType = "运营成本", Amount = report.OperationalCost, 
                    Percentage = report.TotalCost > 0 ? (double)(report.OperationalCost / report.TotalCost) * 100 : 0,
                    Description = "人员、办公等运营开支" },
                new() { CostType = "基础设施成本", Amount = report.InfrastructureCost, 
                    Percentage = report.TotalCost > 0 ? (double)(report.InfrastructureCost / report.TotalCost) * 100 : 0,
                    Description = "服务器、带宽等基础设施开支" },
                new() { CostType = "营销成本", Amount = report.MarketingCost, 
                    Percentage = report.TotalCost > 0 ? (double)(report.MarketingCost / report.TotalCost) * 100 : 0,
                    Description = "推广、广告等营销开支" }
            };

            await _cacheService.SetAsync(cacheKey, report, CacheExpiry, cancellationToken);
            
            _logger.LogInformation("Generated cost analysis report for period {StartDate} to {EndDate}", startDate, endDate);
            return Result<CostAnalysisReportDto>.Success(report);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error generating cost analysis report");
            return Result<CostAnalysisReportDto>.Failure("COST_REPORT_ERROR", "生成成本分析报表失败");
        }
    }

    public async Task<Result<ProfitAnalysisReportDto>> GetProfitAnalysisReportAsync(
        DateTime startDate, 
        DateTime endDate, 
        TimeGranularity granularity = TimeGranularity.Monthly,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var cacheKey = $"financial:profit:{startDate:yyyyMMdd}:{endDate:yyyyMMdd}:{granularity}";
            var cached = await _cacheService.GetAsync<ProfitAnalysisReportDto>(cacheKey, cancellationToken);
            if (cached != null)
            {
                return Result<ProfitAnalysisReportDto>.Success(cached);
            }

            // 获取收入和成本数据
            var revenueResult = await GetRevenueReportAsync(startDate, endDate, granularity, cancellationToken);
            var costResult = await GetCostAnalysisReportAsync(startDate, endDate, granularity, cancellationToken);

            if (!revenueResult.IsSuccess || !costResult.IsSuccess)
            {
                return Result<ProfitAnalysisReportDto>.Failure("DATA_ERROR", "获取收入或成本数据失败");
            }

            var revenue = revenueResult.Value!;
            var cost = costResult.Value!;

            var report = new ProfitAnalysisReportDto
            {
                StartDate = startDate,
                EndDate = endDate,
                Granularity = granularity,
                TotalRevenue = revenue.TotalRevenue,
                TotalCost = cost.TotalCost,
                GrossProfit = revenue.TotalRevenue - cost.TotalCost,
                NetProfit = revenue.TotalRevenue - cost.TotalCost, // 简化计算，实际应扣除税费等
                GeneratedAt = DateTime.UtcNow
            };

            // 计算利润率
            report.GrossProfitMargin = report.TotalRevenue > 0 
                ? (double)(report.GrossProfit / report.TotalRevenue) * 100 
                : 0;
            report.NetProfitMargin = report.TotalRevenue > 0 
                ? (double)(report.NetProfit / report.TotalRevenue) * 100 
                : 0;

            // 生成利润趋势
            report.ProfitTrend = GenerateProfitTrend(revenue.RevenueTrend, cost.CostTrend);

            // 按产品线分析利润
            report.ProfitByProduct = new List<ProfitByProductItem>
            {
                new()
                {
                    ProductType = "订阅服务",
                    Revenue = revenue.SubscriptionRevenue,
                    Cost = cost.TotalCost * 0.6m, // 假设订阅服务占成本的60%
                    Profit = revenue.SubscriptionRevenue - (cost.TotalCost * 0.6m),
                    ProfitMargin = revenue.SubscriptionRevenue > 0 
                        ? (double)((revenue.SubscriptionRevenue - (cost.TotalCost * 0.6m)) / revenue.SubscriptionRevenue) * 100 
                        : 0,
                    ContributionPercentage = report.GrossProfit > 0 
                        ? (double)((revenue.SubscriptionRevenue - (cost.TotalCost * 0.6m)) / report.GrossProfit) * 100 
                        : 0
                },
                new()
                {
                    ProductType = "Token包",
                    Revenue = revenue.TokenPackageRevenue,
                    Cost = cost.TotalCost * 0.4m, // 假设Token包占成本的40%
                    Profit = revenue.TokenPackageRevenue - (cost.TotalCost * 0.4m),
                    ProfitMargin = revenue.TokenPackageRevenue > 0 
                        ? (double)((revenue.TokenPackageRevenue - (cost.TotalCost * 0.4m)) / revenue.TokenPackageRevenue) * 100 
                        : 0,
                    ContributionPercentage = report.GrossProfit > 0 
                        ? (double)((revenue.TokenPackageRevenue - (cost.TotalCost * 0.4m)) / report.GrossProfit) * 100 
                        : 0
                }
            };

            // 按客户群体分析利润
            report.ProfitByCustomerSegment = await GenerateProfitByCustomerSegment(startDate, endDate, cancellationToken);

            // 计算盈利能力指标
            report.EarningsPerUser = revenue.TotalCustomers > 0 
                ? report.NetProfit / revenue.TotalCustomers 
                : 0;
            report.CustomerAcquisitionCost = 100m; // 假设获客成本为100元
            report.ReturnOnInvestment = cost.TotalCost > 0 
                ? (double)(report.NetProfit / cost.TotalCost) * 100 
                : 0;

            await _cacheService.SetAsync(cacheKey, report, CacheExpiry, cancellationToken);
            
            _logger.LogInformation("Generated profit analysis report for period {StartDate} to {EndDate}", startDate, endDate);
            return Result<ProfitAnalysisReportDto>.Success(report);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error generating profit analysis report");
            return Result<ProfitAnalysisReportDto>.Failure("PROFIT_REPORT_ERROR", "生成利润分析报表失败");
        }
    }

    public async Task<Result<CustomerValueReportDto>> GetCustomerValueReportAsync(
        DateTime startDate, 
        DateTime endDate, 
        int topN = 100,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var cacheKey = $"financial:customer-value:{startDate:yyyyMMdd}:{endDate:yyyyMMdd}:{topN}";
            var cached = await _cacheService.GetAsync<CustomerValueReportDto>(cacheKey, cancellationToken);
            if (cached != null)
            {
                return Result<CustomerValueReportDto>.Success(cached);
            }

            var report = new CustomerValueReportDto
            {
                StartDate = startDate,
                EndDate = endDate,
                GeneratedAt = DateTime.UtcNow
            };

            // 获取客户订单数据
            var customerOrdersQuery = await _unitOfWork.Orders.GetQueryable()
                .Where(o => o.Status == OrderStatus.Paid && 
                           o.PaidAt >= startDate && 
                           o.PaidAt <= endDate)
                .GroupBy(o => o.CustomerUserId)
                .Select(g => new
                {
                    CustomerId = g.Key,
                    TotalSpent = g.Sum(o => o.FinalAmount.Amount),
                    TransactionCount = g.Count(),
                    FirstPurchase = g.Min(o => o.PaidAt!.Value),
                    LastPurchase = g.Max(o => o.PaidAt!.Value)
                })
                .ToListAsync(cancellationToken);
                
            // Convert to dynamic list for compatibility
            var customerOrders = customerOrdersQuery.Cast<dynamic>().ToList();

            // 计算平均和中位数CLV
            var clvValues = customerOrders.Select(c => (decimal)c.TotalSpent).OrderBy(v => v).ToList();
            report.AverageCustomerLifetimeValue = clvValues.Any() ? clvValues.Average() : 0;
            report.MedianCustomerLifetimeValue = clvValues.Any() 
                ? clvValues.Count % 2 == 0 
                    ? (clvValues[clvValues.Count / 2 - 1] + clvValues[clvValues.Count / 2]) / 2 
                    : clvValues[clvValues.Count / 2]
                : 0;

            // 客户价值分布
            report.ValueDistribution = GenerateValueDistribution(customerOrders);

            // 获取高价值客户详情
            var topCustomerIds = customerOrders
                .OrderByDescending(c => c.TotalSpent)
                .Take(topN)
                .Select(c => c.CustomerId)
                .ToList();

            var topCustomers = await _unitOfWork.CustomerUsers.GetQueryable()
                .Where(u => topCustomerIds.Contains(u.Id))
                .ToListAsync(cancellationToken);

            var subscriptions = await _unitOfWork.Subscriptions.GetQueryable()
                .Where(s => topCustomerIds.Contains(s.CustomerUserId) && s.Status == SubscriptionStatus.Active)
                .ToListAsync(cancellationToken);

            report.TopCustomers = customerOrders
                .Where(c => topCustomerIds.Contains(c.CustomerId))
                .OrderByDescending(c => c.TotalSpent)
                .Select(c =>
                {
                    var customer = topCustomers.FirstOrDefault(u => u.Id == c.CustomerId);
                    var subscription = subscriptions.FirstOrDefault(s => s.CustomerUserId == c.CustomerId);
                    
                    return new HighValueCustomerItem
                    {
                        CustomerId = c.CustomerId,
                        CustomerName = customer?.Username ?? "Unknown",
                        Email = customer?.Email ?? "Unknown",
                        TotalSpent = c.TotalSpent,
                        LifetimeValue = c.TotalSpent * 1.5m, // 简化CLV计算
                        TransactionCount = c.TransactionCount,
                        FirstPurchaseDate = c.FirstPurchase,
                        LastPurchaseDate = c.LastPurchase,
                        CurrentPlan = subscription?.Plan.Name ?? "None",
                        ChurnProbability = CalculateChurnProbability(c.LastPurchase)
                    };
                })
                .ToList();

            // 客户价值趋势
            report.ValueTrend = await GenerateCustomerValueTrend(startDate, endDate, cancellationToken);

            // 客户群体分析
            report.SegmentAnalysis = await GenerateSegmentAnalysis(startDate, endDate, cancellationToken);

            // 预测指标
            report.PredictedNextMonthValue = report.AverageCustomerLifetimeValue * 1.05m; // 简化预测
            report.ChurnRiskValue = report.TopCustomers
                .Where(c => c.ChurnProbability > 0.5)
                .Sum(c => c.LifetimeValue);

            await _cacheService.SetAsync(cacheKey, report, CacheExpiry, cancellationToken);
            
            _logger.LogInformation("Generated customer value report for period {StartDate} to {EndDate}", startDate, endDate);
            return Result<CustomerValueReportDto>.Success(report);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error generating customer value report");
            return Result<CustomerValueReportDto>.Failure("CUSTOMER_VALUE_REPORT_ERROR", "生成客户价值分析报表失败");
        }
    }

    public async Task<Result<RefundAnalysisReportDto>> GetRefundAnalysisReportAsync(
        DateTime startDate, 
        DateTime endDate, 
        TimeGranularity granularity = TimeGranularity.Monthly,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var cacheKey = $"financial:refund:{startDate:yyyyMMdd}:{endDate:yyyyMMdd}:{granularity}";
            var cached = await _cacheService.GetAsync<RefundAnalysisReportDto>(cacheKey, cancellationToken);
            if (cached != null)
            {
                return Result<RefundAnalysisReportDto>.Success(cached);
            }

            var report = new RefundAnalysisReportDto
            {
                StartDate = startDate,
                EndDate = endDate,
                Granularity = granularity,
                GeneratedAt = DateTime.UtcNow
            };

            // 获取退款数据
            var refunds = await _unitOfWork.RefundRecords.GetQueryable()
                .Where(r => r.CompletedAt != null && r.CompletedAt >= startDate && r.CompletedAt <= endDate)
                .Include(r => r.Order)
                .ToListAsync(cancellationToken);

            // 计算退款总览
            report.TotalRefundCount = refunds.Count;
            report.TotalRefundAmount = refunds.Sum(r => r.RefundAmount.Amount);
            report.AverageRefundAmount = refunds.Any() 
                ? refunds.Average(r => r.RefundAmount.Amount) 
                : 0;

            // 计算退款率
            var totalOrders = await _unitOfWork.Orders.GetQueryable()
                .Where(o => o.Status == OrderStatus.Paid && 
                           o.PaidAt >= startDate && 
                           o.PaidAt <= endDate)
                .CountAsync(cancellationToken);
            report.RefundRate = totalOrders > 0 
                ? (double)report.TotalRefundCount / totalOrders * 100 
                : 0;

            // 生成退款趋势
            report.RefundTrend = await GenerateRefundTrend(refunds, startDate, endDate, granularity, cancellationToken);

            // 退款原因分析
            report.RefundReasons = refunds
                .GroupBy(r => r.Reason)
                .Select(g => new RefundReasonItem
                {
                    Reason = g.Key,
                    Count = g.Count(),
                    Amount = g.Sum(r => r.RefundAmount.Amount),
                    Percentage = refunds.Any() ? (double)g.Count() / refunds.Count * 100 : 0,
                    AverageAmount = g.Average(r => r.RefundAmount.Amount)
                })
                .OrderByDescending(r => r.Count)
                .ToList();

            // 退款时间分析
            report.TimeAnalysis = GenerateRefundTimeAnalysis(refunds);

            // 退款业务影响
            report.BusinessImpact = new RefundBusinessImpact
            {
                RevenueImpact = report.TotalRefundAmount,
                CustomerRetentionImpact = report.RefundRate * 0.3, // 假设退款对留存率的影响系数为0.3
                RiskFactors = GenerateRefundRiskFactors(report),
                PreventionRecommendations = GenerateRefundPreventionRecommendations(report)
            };

            await _cacheService.SetAsync(cacheKey, report, CacheExpiry, cancellationToken);
            
            _logger.LogInformation("Generated refund analysis report for period {StartDate} to {EndDate}", startDate, endDate);
            return Result<RefundAnalysisReportDto>.Success(report);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error generating refund analysis report");
            return Result<RefundAnalysisReportDto>.Failure("REFUND_REPORT_ERROR", "生成退款分析报表失败");
        }
    }

    public async Task<Result<CashFlowReportDto>> GetCashFlowReportAsync(
        DateTime startDate, 
        DateTime endDate, 
        TimeGranularity granularity = TimeGranularity.Monthly,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var cacheKey = $"financial:cashflow:{startDate:yyyyMMdd}:{endDate:yyyyMMdd}:{granularity}";
            var cached = await _cacheService.GetAsync<CashFlowReportDto>(cacheKey, cancellationToken);
            if (cached != null)
            {
                return Result<CashFlowReportDto>.Success(cached);
            }

            var report = new CashFlowReportDto
            {
                StartDate = startDate,
                EndDate = endDate,
                Granularity = granularity,
                GeneratedAt = DateTime.UtcNow
            };

            // 获取期初余额（简化处理，实际应从财务系统获取）
            report.OpeningBalance = 1000000m; // 假设期初余额为100万

            // 计算现金流入
            var paidOrders = await _unitOfWork.Orders.GetQueryable()
                .Where(o => o.Status == OrderStatus.Paid && 
                           o.PaidAt >= startDate && 
                           o.PaidAt <= endDate)
                .ToListAsync(cancellationToken);

            report.CashInflow = new CashInflowDetail
            {
                SubscriptionInflow = paidOrders
                    .Where(o => o.Type == OrderType.Subscription)
                    .Sum(o => o.FinalAmount.Amount),
                TokenPackageInflow = paidOrders
                    .Where(o => o.Type == OrderType.TokenPackage)
                    .Sum(o => o.FinalAmount.Amount),
                OtherInflow = 0,
                Details = new List<CashFlowItem>
                {
                    new()
                    {
                        Category = "订阅服务",
                        Amount = paidOrders.Where(o => o.Type == OrderType.Subscription).Sum(o => o.FinalAmount.Amount),
                        TransactionCount = paidOrders.Count(o => o.Type == OrderType.Subscription),
                        Description = "月度/年度订阅收入"
                    },
                    new()
                    {
                        Category = "Token包",
                        Amount = paidOrders.Where(o => o.Type == OrderType.TokenPackage).Sum(o => o.FinalAmount.Amount),
                        TransactionCount = paidOrders.Count(o => o.Type == OrderType.TokenPackage),
                        Description = "一次性Token包购买收入"
                    }
                }
            };
            report.CashInflow.TotalInflow = report.CashInflow.SubscriptionInflow + 
                                           report.CashInflow.TokenPackageInflow + 
                                           report.CashInflow.OtherInflow;

            // 计算现金流出
            var refunds = await _unitOfWork.RefundRecords.GetQueryable()
                .Where(r => r.CompletedAt != null && r.CompletedAt >= startDate && r.CompletedAt <= endDate)
                .ToListAsync(cancellationToken);

            report.CashOutflow = new CashOutflowDetail
            {
                RefundOutflow = refunds.Sum(r => r.RefundAmount.Amount),
                OperationalOutflow = report.CashInflow.TotalInflow * 0.3m, // 假设运营成本为收入的30%
                OtherOutflow = 0,
                Details = new List<CashFlowItem>
                {
                    new()
                    {
                        Category = "退款",
                        Amount = refunds.Sum(r => r.RefundAmount.Amount),
                        TransactionCount = refunds.Count,
                        Description = "客户退款支出"
                    },
                    new()
                    {
                        Category = "运营成本",
                        Amount = report.CashInflow.TotalInflow * 0.3m,
                        TransactionCount = 0,
                        Description = "人员工资、办公等运营支出"
                    }
                }
            };
            report.CashOutflow.TotalOutflow = report.CashOutflow.RefundOutflow + 
                                             report.CashOutflow.OperationalOutflow + 
                                             report.CashOutflow.OtherOutflow;

            // 计算净现金流
            report.NetCashFlow = report.CashInflow.TotalInflow - report.CashOutflow.TotalOutflow;
            report.ClosingBalance = report.OpeningBalance + report.NetCashFlow;

            // 生成现金流趋势
            report.CashFlowTrend = await GenerateCashFlowTrend(paidOrders, refunds, startDate, endDate, granularity, report.OpeningBalance);

            // 现金流预测
            report.Forecast = new CashFlowForecast
            {
                NextMonthForecast = report.NetCashFlow * 1.1m, // 简化预测，增长10%
                NextQuarterForecast = report.NetCashFlow * 3.3m, // 简化预测
                ConfidenceLevel = 0.75,
                Assumptions = new List<string>
                {
                    "基于历史增长率的线性预测",
                    "假设客户留存率保持稳定",
                    "不考虑季节性因素影响"
                }
            };

            await _cacheService.SetAsync(cacheKey, report, CacheExpiry, cancellationToken);
            
            _logger.LogInformation("Generated cash flow report for period {StartDate} to {EndDate}", startDate, endDate);
            return Result<CashFlowReportDto>.Success(report);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error generating cash flow report");
            return Result<CashFlowReportDto>.Failure("CASHFLOW_REPORT_ERROR", "生成现金流报表失败");
        }
    }

    public async Task<Result<ComprehensiveFinancialReportDto>> GetComprehensiveReportAsync(
        DateTime startDate, 
        DateTime endDate,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var cacheKey = $"financial:comprehensive:{startDate:yyyyMMdd}:{endDate:yyyyMMdd}";
            var cached = await _cacheService.GetAsync<ComprehensiveFinancialReportDto>(cacheKey, cancellationToken);
            if (cached != null)
            {
                return Result<ComprehensiveFinancialReportDto>.Success(cached);
            }

            // 获取各项报表数据
            var revenueResult = await GetRevenueReportAsync(startDate, endDate, TimeGranularity.Monthly, cancellationToken);
            var costResult = await GetCostAnalysisReportAsync(startDate, endDate, TimeGranularity.Monthly, cancellationToken);
            var profitResult = await GetProfitAnalysisReportAsync(startDate, endDate, TimeGranularity.Monthly, cancellationToken);
            var customerValueResult = await GetCustomerValueReportAsync(startDate, endDate, 10, cancellationToken);

            if (!revenueResult.IsSuccess || !costResult.IsSuccess || 
                !profitResult.IsSuccess || !customerValueResult.IsSuccess)
            {
                return Result<ComprehensiveFinancialReportDto>.Failure("DATA_ERROR", "获取财务数据失败");
            }

            var revenue = revenueResult.Value!;
            var cost = costResult.Value!;
            var profit = profitResult.Value!;
            var customerValue = customerValueResult.Value!;

            var report = new ComprehensiveFinancialReportDto
            {
                StartDate = startDate,
                EndDate = endDate,
                GeneratedAt = DateTime.UtcNow
            };

            // 收入概览
            report.Revenue = new RevenueOverview
            {
                Total = revenue.TotalRevenue,
                Recurring = revenue.SubscriptionRevenue,
                OneTime = revenue.TokenPackageRevenue,
                GrowthRate = (double)revenue.MonthOverMonthGrowth
            };

            // 成本概览
            report.Cost = new CostOverview
            {
                Total = cost.TotalCost,
                Variable = cost.TokenCost,
                Fixed = cost.OperationalCost + cost.InfrastructureCost,
                EfficiencyRatio = revenue.TotalRevenue > 0 
                    ? (double)(cost.TotalCost / revenue.TotalRevenue) 
                    : 0
            };

            // 利润概览
            report.Profit = new ProfitOverview
            {
                GrossProfit = profit.GrossProfit,
                NetProfit = profit.NetProfit,
                GrossMargin = profit.GrossProfitMargin,
                NetMargin = profit.NetProfitMargin
            };

            // 关键财务指标
            var activeSubscriptions = await _unitOfWork.Subscriptions.GetQueryable()
                .Where(s => s.Status == SubscriptionStatus.Active)
                .ToListAsync(cancellationToken);

            var monthlyRecurringRevenue = activeSubscriptions.Sum(s => s.PaidAmount.Amount);
            var churnedCount = await _unitOfWork.Subscriptions.GetQueryable()
                .Where(s => s.Status == SubscriptionStatus.Cancelled && 
                           s.UpdatedAt >= startDate && 
                           s.UpdatedAt <= endDate)
                .CountAsync(cancellationToken);

            report.KeyMetrics = new FinancialKeyMetrics
            {
                MonthlyRecurringRevenue = monthlyRecurringRevenue,
                AnnualRecurringRevenue = monthlyRecurringRevenue * 12,
                CustomerLifetimeValue = customerValue.AverageCustomerLifetimeValue,
                CustomerAcquisitionCost = 100m, // 假设值
                CLVCACRatio = 100m > 0 ? (double)(customerValue.AverageCustomerLifetimeValue / 100m) : 0,
                ChurnRate = activeSubscriptions.Count > 0 
                    ? (double)churnedCount / activeSubscriptions.Count * 100 
                    : 0,
                RetentionRate = activeSubscriptions.Count > 0 
                    ? (double)(activeSubscriptions.Count - churnedCount) / activeSubscriptions.Count * 100 
                    : 0,
                BurnRate = profit.NetProfit < 0 ? Math.Abs(profit.NetProfit) : 0,
                RunwayMonths = profit.NetProfit < 0 && report.Cost.Total > 0 
                    ? (int)(1000000m / report.Cost.Total) // 假设现金储备100万
                    : 999 // 盈利状态
            };

            // 财务健康度评估
            report.HealthAssessment = AssessFinancialHealth(report);

            await _cacheService.SetAsync(cacheKey, report, CacheExpiry, cancellationToken);
            
            _logger.LogInformation("Generated comprehensive financial report for period {StartDate} to {EndDate}", startDate, endDate);
            return Result<ComprehensiveFinancialReportDto>.Success(report);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error generating comprehensive financial report");
            return Result<ComprehensiveFinancialReportDto>.Failure("COMPREHENSIVE_REPORT_ERROR", "生成综合财务报表失败");
        }
    }

    #region Private Helper Methods

    private async Task<List<RevenueTrendItem>> GenerateRevenueTrend(
        List<Domain.Entities.Payment.Order> orders, 
        DateTime startDate, 
        DateTime endDate, 
        TimeGranularity granularity,
        CancellationToken cancellationToken)
    {
        var trend = new List<RevenueTrendItem>();
        var currentDate = startDate;

        while (currentDate <= endDate)
        {
            DateTime periodEnd;
            string periodLabel;

            switch (granularity)
            {
                case TimeGranularity.Daily:
                    periodEnd = currentDate.Date.AddDays(1).AddSeconds(-1);
                    periodLabel = currentDate.ToString("yyyy-MM-dd");
                    break;
                case TimeGranularity.Weekly:
                    periodEnd = currentDate.Date.AddDays(7).AddSeconds(-1);
                    periodLabel = $"{currentDate:yyyy-MM-dd} ~ {periodEnd:yyyy-MM-dd}";
                    break;
                case TimeGranularity.Monthly:
                    periodEnd = new DateTime(currentDate.Year, currentDate.Month, 1).AddMonths(1).AddSeconds(-1);
                    periodLabel = currentDate.ToString("yyyy-MM");
                    break;
                case TimeGranularity.Quarterly:
                    var quarter = (currentDate.Month - 1) / 3 + 1;
                    periodEnd = new DateTime(currentDate.Year, quarter * 3, 1).AddMonths(1).AddSeconds(-1);
                    periodLabel = $"{currentDate.Year} Q{quarter}";
                    break;
                case TimeGranularity.Yearly:
                    periodEnd = new DateTime(currentDate.Year + 1, 1, 1).AddSeconds(-1);
                    periodLabel = currentDate.Year.ToString();
                    break;
                default:
                    periodEnd = currentDate.Date.AddMonths(1).AddSeconds(-1);
                    periodLabel = currentDate.ToString("yyyy-MM");
                    break;
            }

            if (periodEnd > endDate) periodEnd = endDate;

            var periodOrders = orders.Where(o => o.PaidAt >= currentDate && o.PaidAt <= periodEnd).ToList();

            // 获取新客户数
            var customerIds = periodOrders.Select(o => o.CustomerUserId).Distinct().ToList();
            var newCustomers = 0;
            if (customerIds.Any())
            {
                newCustomers = await _unitOfWork.CustomerUsers.GetQueryable()
                    .Where(u => customerIds.Contains(u.Id) && 
                               u.CreatedAt >= currentDate && 
                               u.CreatedAt <= periodEnd)
                    .CountAsync(cancellationToken);
            }

            trend.Add(new RevenueTrendItem
            {
                Date = currentDate,
                Period = periodLabel,
                TotalRevenue = periodOrders.Sum(o => o.FinalAmount.Amount),
                SubscriptionRevenue = periodOrders.Where(o => o.Type == OrderType.Subscription).Sum(o => o.FinalAmount.Amount),
                TokenPackageRevenue = periodOrders.Where(o => o.Type == OrderType.TokenPackage).Sum(o => o.FinalAmount.Amount),
                TransactionCount = periodOrders.Count,
                NewCustomers = newCustomers
            });

            // 移动到下一个周期
            currentDate = granularity switch
            {
                TimeGranularity.Daily => currentDate.AddDays(1),
                TimeGranularity.Weekly => currentDate.AddDays(7),
                TimeGranularity.Monthly => currentDate.AddMonths(1),
                TimeGranularity.Quarterly => currentDate.AddMonths(3),
                TimeGranularity.Yearly => currentDate.AddYears(1),
                _ => currentDate.AddMonths(1)
            };
        }

        return trend;
    }

    private async Task<List<RevenueByPlanItem>> GenerateRevenueByPlan(
        List<Domain.Entities.Payment.Order> orders,
        CancellationToken cancellationToken)
    {
        var subscriptionOrders = orders.Where(o => o.Type == OrderType.Subscription && o.ProductId.HasValue).ToList();
        var planIds = subscriptionOrders.Select(o => o.ProductId!.Value).Distinct().ToList();
        
        var plans = await _unitOfWork.SubscriptionPlans.GetQueryable()
            .Where(p => planIds.Contains(p.Id))
            .ToListAsync(cancellationToken);

        var totalRevenue = subscriptionOrders.Sum(o => o.FinalAmount.Amount);

        return plans.Select(plan =>
        {
            var planOrders = subscriptionOrders.Where(o => o.ProductId == plan.Id).ToList();
            var revenue = planOrders.Sum(o => o.FinalAmount.Amount);
            
            return new RevenueByPlanItem
            {
                PlanId = plan.Id,
                PlanName = plan.Name,
                Revenue = revenue,
                Percentage = totalRevenue > 0 ? (double)(revenue / totalRevenue) * 100 : 0,
                SubscriberCount = planOrders.Count,
                NewSubscribers = planOrders.Count, // 简化处理
                ChurnedSubscribers = 0 // 需要更复杂的查询来获取
            };
        }).OrderByDescending(x => x.Revenue).ToList();
    }

    private List<RevenueByChannelItem> GenerateRevenueByChannel(List<Domain.Entities.Payment.Order> orders)
    {
        var totalRevenue = orders.Sum(o => o.FinalAmount.Amount);
        var channels = orders.GroupBy(o => o.PaymentMethod);

        return channels.Select(g =>
        {
            var revenue = g.Sum(o => o.FinalAmount.Amount);
            return new RevenueByChannelItem
            {
                Channel = g.Key,
                ChannelName = g.Key.ToString(),
                Revenue = revenue,
                Percentage = totalRevenue > 0 ? (double)(revenue / totalRevenue) * 100 : 0,
                TransactionCount = g.Count(),
                SuccessRate = 100 // 因为这里只统计已支付订单
            };
        }).OrderByDescending(x => x.Revenue).ToList();
    }

    private async Task<(decimal yearOverYear, decimal monthOverMonth)> CalculateGrowthRates(
        DateTime startDate, 
        DateTime endDate, 
        decimal currentRevenue,
        CancellationToken cancellationToken)
    {
        // 计算同比（去年同期）
        var lastYearStart = startDate.AddYears(-1);
        var lastYearEnd = endDate.AddYears(-1);
        var lastYearRevenue = await _unitOfWork.Orders.GetQueryable()
            .Where(o => o.Status == OrderStatus.Paid && 
                       o.PaidAt >= lastYearStart && 
                       o.PaidAt <= lastYearEnd)
            .SumAsync(o => o.FinalAmount.Amount, cancellationToken);

        var yoy = lastYearRevenue > 0 
            ? ((currentRevenue - lastYearRevenue) / lastYearRevenue) * 100 
            : 0;

        // 计算环比（上个月）
        var lastMonthStart = startDate.AddMonths(-1);
        var lastMonthEnd = endDate.AddMonths(-1);
        var lastMonthRevenue = await _unitOfWork.Orders.GetQueryable()
            .Where(o => o.Status == OrderStatus.Paid && 
                       o.PaidAt >= lastMonthStart && 
                       o.PaidAt <= lastMonthEnd)
            .SumAsync(o => o.FinalAmount.Amount, cancellationToken);

        var mom = lastMonthRevenue > 0 
            ? ((currentRevenue - lastMonthRevenue) / lastMonthRevenue) * 100 
            : 0;

        return (yoy, mom);
    }

    private async Task<List<CostTrendItem>> GenerateCostTrend(
        List<Domain.Entities.Subscription.TokenUsage> tokenUsages,
        DateTime startDate,
        DateTime endDate,
        TimeGranularity granularity)
    {
        var trend = new List<CostTrendItem>();
        var currentDate = startDate;

        while (currentDate <= endDate)
        {
            DateTime periodEnd;
            string periodLabel;

            switch (granularity)
            {
                case TimeGranularity.Daily:
                    periodEnd = currentDate.Date.AddDays(1).AddSeconds(-1);
                    periodLabel = currentDate.ToString("yyyy-MM-dd");
                    break;
                case TimeGranularity.Weekly:
                    periodEnd = currentDate.Date.AddDays(7).AddSeconds(-1);
                    periodLabel = $"{currentDate:yyyy-MM-dd} ~ {periodEnd:yyyy-MM-dd}";
                    break;
                case TimeGranularity.Monthly:
                    periodEnd = new DateTime(currentDate.Year, currentDate.Month, 1).AddMonths(1).AddSeconds(-1);
                    periodLabel = currentDate.ToString("yyyy-MM");
                    break;
                case TimeGranularity.Quarterly:
                    var quarter = (currentDate.Month - 1) / 3 + 1;
                    periodEnd = new DateTime(currentDate.Year, quarter * 3, 1).AddMonths(1).AddSeconds(-1);
                    periodLabel = $"{currentDate.Year} Q{quarter}";
                    break;
                case TimeGranularity.Yearly:
                    periodEnd = new DateTime(currentDate.Year + 1, 1, 1).AddSeconds(-1);
                    periodLabel = currentDate.Year.ToString();
                    break;
                default:
                    periodEnd = currentDate.Date.AddMonths(1).AddSeconds(-1);
                    periodLabel = currentDate.ToString("yyyy-MM");
                    break;
            }

            if (periodEnd > endDate) periodEnd = endDate;

            var periodTokens = tokenUsages.Where(t => t.UsedAt >= currentDate && t.UsedAt <= periodEnd).ToList();
            var tokensUsed = periodTokens.Sum(t => t.Tokens);
            var tokenCost = tokensUsed * 0.00002m;

            trend.Add(new CostTrendItem
            {
                Date = currentDate,
                Period = periodLabel,
                TokenCost = tokenCost,
                OperationalCost = tokenCost * 0.3m,
                TotalCost = tokenCost * 1.6m, // Token成本 + 运营成本 + 基础设施成本 + 营销成本
                TokensUsed = tokensUsed
            });

            // 移动到下一个周期
            currentDate = granularity switch
            {
                TimeGranularity.Daily => currentDate.AddDays(1),
                TimeGranularity.Weekly => currentDate.AddDays(7),
                TimeGranularity.Monthly => currentDate.AddMonths(1),
                TimeGranularity.Quarterly => currentDate.AddMonths(3),
                TimeGranularity.Yearly => currentDate.AddYears(1),
                _ => currentDate.AddMonths(1)
            };
        }

        return trend;
    }

    private List<ProfitTrendItem> GenerateProfitTrend(
        List<RevenueTrendItem> revenueTrend,
        List<CostTrendItem> costTrend)
    {
        return revenueTrend.Select(r =>
        {
            var cost = costTrend.FirstOrDefault(c => c.Date == r.Date);
            var totalCost = cost?.TotalCost ?? 0;
            var grossProfit = r.TotalRevenue - totalCost;
            
            return new ProfitTrendItem
            {
                Date = r.Date,
                Period = r.Period,
                Revenue = r.TotalRevenue,
                Cost = totalCost,
                GrossProfit = grossProfit,
                NetProfit = grossProfit, // 简化处理
                ProfitMargin = r.TotalRevenue > 0 ? (double)(grossProfit / r.TotalRevenue) * 100 : 0
            };
        }).ToList();
    }

    private async Task<List<ProfitByCustomerSegmentItem>> GenerateProfitByCustomerSegment(
        DateTime startDate,
        DateTime endDate,
        CancellationToken cancellationToken)
    {
        var plans = await _unitOfWork.SubscriptionPlans.GetQueryable().ToListAsync(cancellationToken);
        var segments = new List<ProfitByCustomerSegmentItem>();

        foreach (var plan in plans)
        {
            var planOrders = await _unitOfWork.Orders.GetQueryable()
                .Where(o => o.Status == OrderStatus.Paid && 
                           o.Type == OrderType.Subscription &&
                           o.ProductId == plan.Id &&
                           o.PaidAt >= startDate && 
                           o.PaidAt <= endDate)
                .ToListAsync(cancellationToken);

            var customerCount = planOrders.Select(o => o.CustomerUserId).Distinct().Count();
            var revenue = planOrders.Sum(o => o.FinalAmount.Amount);
            var cost = revenue * 0.4m; // 假设成本为收入的40%
            var profit = revenue - cost;

            segments.Add(new ProfitByCustomerSegmentItem
            {
                Segment = plan.Name,
                CustomerCount = customerCount,
                Revenue = revenue,
                Cost = cost,
                Profit = profit,
                ProfitMargin = revenue > 0 ? (double)(profit / revenue) * 100 : 0,
                AverageProfit = customerCount > 0 ? profit / customerCount : 0
            });
        }

        return segments.OrderByDescending(s => s.Profit).ToList();
    }

    private List<CustomerValueDistributionItem> GenerateValueDistribution(
        List<dynamic> customerOrders)
    {
        var ranges = new[]
        {
            (min: 0m, max: 100m, label: "¥0-100"),
            (min: 100m, max: 500m, label: "¥100-500"),
            (min: 500m, max: 1000m, label: "¥500-1000"),
            (min: 1000m, max: 5000m, label: "¥1000-5000"),
            (min: 5000m, max: decimal.MaxValue, label: "¥5000+")
        };

        var totalCustomers = customerOrders.Count;
        var distribution = new List<CustomerValueDistributionItem>();

        foreach (var range in ranges)
        {
            var customersInRange = customerOrders
                .Where(c => c.TotalSpent >= range.min && c.TotalSpent < range.max)
                .ToList();

            distribution.Add(new CustomerValueDistributionItem
            {
                ValueRange = range.label,
                CustomerCount = customersInRange.Count,
                Percentage = totalCustomers > 0 ? (double)customersInRange.Count / totalCustomers * 100 : 0,
                TotalValue = customersInRange.Sum(c => (decimal)c.TotalSpent)
            });
        }

        return distribution;
    }

    private double CalculateChurnProbability(DateTime lastPurchaseDate)
    {
        var daysSinceLastPurchase = (DateTime.UtcNow - lastPurchaseDate).TotalDays;
        
        // 简化的流失概率计算
        if (daysSinceLastPurchase < 30) return 0.1;
        if (daysSinceLastPurchase < 60) return 0.3;
        if (daysSinceLastPurchase < 90) return 0.5;
        if (daysSinceLastPurchase < 180) return 0.7;
        return 0.9;
    }

    private async Task<List<CustomerValueTrendItem>> GenerateCustomerValueTrend(
        DateTime startDate,
        DateTime endDate,
        CancellationToken cancellationToken)
    {
        var trend = new List<CustomerValueTrendItem>();
        var currentDate = new DateTime(startDate.Year, startDate.Month, 1);

        while (currentDate <= endDate)
        {
            var monthEnd = currentDate.AddMonths(1).AddSeconds(-1);
            if (monthEnd > endDate) monthEnd = endDate;

            var monthOrders = await _unitOfWork.Orders.GetQueryable()
                .Where(o => o.Status == OrderStatus.Paid && 
                           o.PaidAt >= currentDate && 
                           o.PaidAt <= monthEnd)
                .GroupBy(o => o.CustomerUserId)
                .Select(g => new
                {
                    CustomerId = g.Key,
                    TotalSpent = g.Sum(o => o.FinalAmount.Amount)
                })
                .ToListAsync(cancellationToken);

            var activeCustomers = monthOrders.Count;
            var totalValue = monthOrders.Sum(o => o.TotalSpent);
            var averageValue = activeCustomers > 0 ? totalValue / activeCustomers : 0;

            trend.Add(new CustomerValueTrendItem
            {
                Date = currentDate,
                Period = currentDate.ToString("yyyy-MM"),
                AverageValue = averageValue,
                TotalValue = totalValue,
                ActiveCustomers = activeCustomers
            });

            currentDate = currentDate.AddMonths(1);
        }

        return trend;
    }

    private async Task<CustomerSegmentAnalysis> GenerateSegmentAnalysis(
        DateTime startDate,
        DateTime endDate,
        CancellationToken cancellationToken)
    {
        var plans = await _unitOfWork.SubscriptionPlans.GetQueryable().ToListAsync(cancellationToken);
        var analysis = new CustomerSegmentAnalysis
        {
            SegmentValues = new List<SegmentValueItem>()
        };

        foreach (var plan in plans)
        {
            var subscriptions = await _unitOfWork.Subscriptions.GetQueryable()
                .Where(s => s.PlanId == plan.Id && s.Status == SubscriptionStatus.Active)
                .ToListAsync(cancellationToken);

            var customerIds = subscriptions.Select(s => s.CustomerUserId).Distinct().ToList();
            
            var segmentOrders = await _unitOfWork.Orders.GetQueryable()
                .Where(o => customerIds.Contains(o.CustomerUserId) &&
                           o.Status == OrderStatus.Paid &&
                           o.PaidAt >= startDate && 
                           o.PaidAt <= endDate)
                .ToListAsync(cancellationToken);

            var totalValue = segmentOrders.Sum(o => o.FinalAmount.Amount);
            
            analysis.SegmentValues.Add(new SegmentValueItem
            {
                Segment = plan.Name,
                CustomerCount = customerIds.Count,
                TotalValue = totalValue,
                AverageValue = customerIds.Count > 0 ? totalValue / customerIds.Count : 0,
                ValuePercentage = 0, // 将在后面计算
                RetentionRate = 95 // 简化处理，实际应计算真实留存率
            });
        }

        analysis.TotalValue = analysis.SegmentValues.Sum(s => s.TotalValue);
        
        // 计算百分比
        foreach (var segment in analysis.SegmentValues)
        {
            segment.ValuePercentage = analysis.TotalValue > 0 
                ? (double)(segment.TotalValue / analysis.TotalValue) * 100 
                : 0;
        }

        return analysis;
    }

    private async Task<List<RefundTrendItem>> GenerateRefundTrend(
        List<Domain.Entities.Payment.RefundRecord> refunds,
        DateTime startDate,
        DateTime endDate,
        TimeGranularity granularity,
        CancellationToken cancellationToken)
    {
        var trend = new List<RefundTrendItem>();
        var currentDate = startDate;

        while (currentDate <= endDate)
        {
            DateTime periodEnd;
            string periodLabel;

            switch (granularity)
            {
                case TimeGranularity.Daily:
                    periodEnd = currentDate.Date.AddDays(1).AddSeconds(-1);
                    periodLabel = currentDate.ToString("yyyy-MM-dd");
                    break;
                case TimeGranularity.Weekly:
                    periodEnd = currentDate.Date.AddDays(7).AddSeconds(-1);
                    periodLabel = $"{currentDate:yyyy-MM-dd} ~ {periodEnd:yyyy-MM-dd}";
                    break;
                case TimeGranularity.Monthly:
                    periodEnd = new DateTime(currentDate.Year, currentDate.Month, 1).AddMonths(1).AddSeconds(-1);
                    periodLabel = currentDate.ToString("yyyy-MM");
                    break;
                case TimeGranularity.Quarterly:
                    var quarter = (currentDate.Month - 1) / 3 + 1;
                    periodEnd = new DateTime(currentDate.Year, quarter * 3, 1).AddMonths(1).AddSeconds(-1);
                    periodLabel = $"{currentDate.Year} Q{quarter}";
                    break;
                case TimeGranularity.Yearly:
                    periodEnd = new DateTime(currentDate.Year + 1, 1, 1).AddSeconds(-1);
                    periodLabel = currentDate.Year.ToString();
                    break;
                default:
                    periodEnd = currentDate.Date.AddMonths(1).AddSeconds(-1);
                    periodLabel = currentDate.ToString("yyyy-MM");
                    break;
            }

            if (periodEnd > endDate) periodEnd = endDate;

            var periodRefunds = refunds
                .Where(r => r.CompletedAt != null && r.CompletedAt >= currentDate && r.CompletedAt <= periodEnd)
                .ToList();

            var periodOrders = await _unitOfWork.Orders.GetQueryable()
                .Where(o => o.Status == OrderStatus.Paid && 
                           o.PaidAt >= currentDate && 
                           o.PaidAt <= periodEnd)
                .CountAsync(cancellationToken);

            trend.Add(new RefundTrendItem
            {
                Date = currentDate,
                Period = periodLabel,
                RefundCount = periodRefunds.Count,
                RefundAmount = periodRefunds.Sum(r => r.RefundAmount.Amount),
                RefundRate = periodOrders > 0 ? (double)periodRefunds.Count / periodOrders * 100 : 0
            });

            // 移动到下一个周期
            currentDate = granularity switch
            {
                TimeGranularity.Daily => currentDate.AddDays(1),
                TimeGranularity.Weekly => currentDate.AddDays(7),
                TimeGranularity.Monthly => currentDate.AddMonths(1),
                TimeGranularity.Quarterly => currentDate.AddMonths(3),
                TimeGranularity.Yearly => currentDate.AddYears(1),
                _ => currentDate.AddMonths(1)
            };
        }

        return trend;
    }

    private RefundTimeAnalysis GenerateRefundTimeAnalysis(List<Domain.Entities.Payment.RefundRecord> refunds)
    {
        var analysis = new RefundTimeAnalysis
        {
            TimeDistribution = new List<RefundTimeDistributionItem>()
        };

        // 计算从购买到退款的时间
        var refundTimes = new List<double>();
        foreach (var refund in refunds)
        {
            if (refund.Order?.PaidAt != null)
            {
                var days = (refund.CompletedAt!.Value - refund.Order.PaidAt.Value).TotalDays;
                refundTimes.Add(days);
            }
        }

        analysis.AverageRefundTime = refundTimes.Any() ? refundTimes.Average() : 0;

        // 时间分布
        var timeRanges = new[]
        {
            (min: 0, max: 7, label: "0-7天"),
            (min: 7, max: 30, label: "8-30天"),
            (min: 30, max: 90, label: "31-90天"),
            (min: 90, max: int.MaxValue, label: "90天以上")
        };

        foreach (var range in timeRanges)
        {
            var count = refundTimes.Count(t => t >= range.min && t < range.max);
            analysis.TimeDistribution.Add(new RefundTimeDistributionItem
            {
                TimeRange = range.label,
                Count = count,
                Percentage = refundTimes.Any() ? (double)count / refundTimes.Count * 100 : 0
            });
        }

        return analysis;
    }

    private List<string> GenerateRefundRiskFactors(RefundAnalysisReportDto report)
    {
        var riskFactors = new List<string>();

        if (report.RefundRate > 10)
            riskFactors.Add("退款率超过10%，高于行业平均水平");

        if (report.TotalRefundAmount > report.TotalRefundCount * 200)
            riskFactors.Add("平均退款金额较高，可能存在大额订单退款");

        var techIssues = report.RefundReasons.FirstOrDefault(r => r.Reason.Contains("技术") || r.Reason.Contains("故障"));
        if (techIssues != null && techIssues.Percentage > 30)
            riskFactors.Add("技术问题导致的退款占比较高");

        if (report.TimeAnalysis.AverageRefundTime < 7)
            riskFactors.Add("退款时间过快，可能存在产品体验问题");

        return riskFactors;
    }

    private List<string> GenerateRefundPreventionRecommendations(RefundAnalysisReportDto report)
    {
        var recommendations = new List<string>();

        if (report.RefundRate > 10)
            recommendations.Add("优化产品描述和演示，确保客户预期与实际体验一致");

        var techIssues = report.RefundReasons.FirstOrDefault(r => r.Reason.Contains("技术") || r.Reason.Contains("故障"));
        if (techIssues != null && techIssues.Percentage > 30)
            recommendations.Add("加强技术支持和系统稳定性，减少技术故障");

        if (report.TimeAnalysis.AverageRefundTime < 7)
            recommendations.Add("实施新用户引导计划，帮助客户更好地理解和使用产品");

        recommendations.Add("建立客户满意度调查机制，及时发现和解决问题");
        recommendations.Add("考虑提供试用期或免费体验，让客户充分了解产品");

        return recommendations;
    }

    private async Task<List<CashFlowTrendItem>> GenerateCashFlowTrend(
        List<Domain.Entities.Payment.Order> orders,
        List<Domain.Entities.Payment.RefundRecord> refunds,
        DateTime startDate,
        DateTime endDate,
        TimeGranularity granularity,
        decimal openingBalance)
    {
        var trend = new List<CashFlowTrendItem>();
        var currentDate = startDate;
        var runningBalance = openingBalance;

        while (currentDate <= endDate)
        {
            DateTime periodEnd;
            string periodLabel;

            switch (granularity)
            {
                case TimeGranularity.Daily:
                    periodEnd = currentDate.Date.AddDays(1).AddSeconds(-1);
                    periodLabel = currentDate.ToString("yyyy-MM-dd");
                    break;
                case TimeGranularity.Weekly:
                    periodEnd = currentDate.Date.AddDays(7).AddSeconds(-1);
                    periodLabel = $"{currentDate:yyyy-MM-dd} ~ {periodEnd:yyyy-MM-dd}";
                    break;
                case TimeGranularity.Monthly:
                    periodEnd = new DateTime(currentDate.Year, currentDate.Month, 1).AddMonths(1).AddSeconds(-1);
                    periodLabel = currentDate.ToString("yyyy-MM");
                    break;
                case TimeGranularity.Quarterly:
                    var quarter = (currentDate.Month - 1) / 3 + 1;
                    periodEnd = new DateTime(currentDate.Year, quarter * 3, 1).AddMonths(1).AddSeconds(-1);
                    periodLabel = $"{currentDate.Year} Q{quarter}";
                    break;
                case TimeGranularity.Yearly:
                    periodEnd = new DateTime(currentDate.Year + 1, 1, 1).AddSeconds(-1);
                    periodLabel = currentDate.Year.ToString();
                    break;
                default:
                    periodEnd = currentDate.Date.AddMonths(1).AddSeconds(-1);
                    periodLabel = currentDate.ToString("yyyy-MM");
                    break;
            }

            if (periodEnd > endDate) periodEnd = endDate;

            var periodInflow = orders
                .Where(o => o.PaidAt >= currentDate && o.PaidAt <= periodEnd)
                .Sum(o => o.FinalAmount.Amount);

            var periodRefunds = refunds
                .Where(r => r.CompletedAt != null && r.CompletedAt >= currentDate && r.CompletedAt <= periodEnd)
                .Sum(r => r.RefundAmount.Amount);

            var periodOperationalCost = periodInflow * 0.3m; // 假设运营成本为收入的30%
            var periodOutflow = periodRefunds + periodOperationalCost;

            var netFlow = periodInflow - periodOutflow;
            runningBalance += netFlow;

            trend.Add(new CashFlowTrendItem
            {
                Date = currentDate,
                Period = periodLabel,
                Inflow = periodInflow,
                Outflow = periodOutflow,
                NetFlow = netFlow,
                Balance = runningBalance
            });

            // 移动到下一个周期
            currentDate = granularity switch
            {
                TimeGranularity.Daily => currentDate.AddDays(1),
                TimeGranularity.Weekly => currentDate.AddDays(7),
                TimeGranularity.Monthly => currentDate.AddMonths(1),
                TimeGranularity.Quarterly => currentDate.AddMonths(3),
                TimeGranularity.Yearly => currentDate.AddYears(1),
                _ => currentDate.AddMonths(1)
            };
        }

        return trend;
    }

    private FinancialHealthAssessment AssessFinancialHealth(ComprehensiveFinancialReportDto report)
    {
        var assessment = new FinancialHealthAssessment
        {
            Strengths = new List<string>(),
            Weaknesses = new List<string>(),
            Recommendations = new List<string>()
        };

        var score = 0.0;

        // 评估利润率
        if (report.Profit.NetMargin > 20)
        {
            score += 20;
            assessment.Strengths.Add("净利润率健康，超过20%");
        }
        else if (report.Profit.NetMargin > 10)
        {
            score += 10;
            assessment.Strengths.Add("净利润率良好，超过10%");
        }
        else if (report.Profit.NetMargin < 0)
        {
            assessment.Weaknesses.Add("净利润率为负，业务处于亏损状态");
        }

        // 评估CLV/CAC比率
        if (report.KeyMetrics.CLVCACRatio > 3)
        {
            score += 20;
            assessment.Strengths.Add("客户生命周期价值远高于获客成本");
        }
        else if (report.KeyMetrics.CLVCACRatio > 1)
        {
            score += 10;
        }
        else
        {
            assessment.Weaknesses.Add("获客成本过高，CLV/CAC比率低于1");
        }

        // 评估流失率
        if (report.KeyMetrics.ChurnRate < 5)
        {
            score += 20;
            assessment.Strengths.Add("客户流失率低，留存良好");
        }
        else if (report.KeyMetrics.ChurnRate < 10)
        {
            score += 10;
        }
        else
        {
            assessment.Weaknesses.Add("客户流失率偏高，需要改善产品和服务");
        }

        // 评估收入增长
        if (report.Revenue.GrowthRate > 20)
        {
            score += 20;
            assessment.Strengths.Add("收入增长强劲，超过20%");
        }
        else if (report.Revenue.GrowthRate > 0)
        {
            score += 10;
        }
        else
        {
            assessment.Weaknesses.Add("收入增长停滞或下降");
        }

        // 评估成本效率
        if (report.Cost.EfficiencyRatio < 0.6)
        {
            score += 20;
            assessment.Strengths.Add("成本控制良好，成本收入比低于60%");
        }
        else if (report.Cost.EfficiencyRatio < 0.8)
        {
            score += 10;
        }
        else
        {
            assessment.Weaknesses.Add("成本偏高，需要优化运营效率");
        }

        // 综合评分
        assessment.HealthScore = score;
        
        if (score >= 80)
        {
            assessment.OverallStatus = "Excellent";
        }
        else if (score >= 60)
        {
            assessment.OverallStatus = "Good";
        }
        else if (score >= 40)
        {
            assessment.OverallStatus = "Fair";
        }
        else
        {
            assessment.OverallStatus = "Poor";
        }

        // 生成建议
        if (report.KeyMetrics.ChurnRate > 10)
        {
            assessment.Recommendations.Add("实施客户成功计划，降低流失率");
        }

        if (report.Cost.EfficiencyRatio > 0.8)
        {
            assessment.Recommendations.Add("审查运营成本，寻找效率提升机会");
        }

        if (report.Revenue.GrowthRate < 10)
        {
            assessment.Recommendations.Add("加强市场推广，开发新的收入来源");
        }

        if (report.KeyMetrics.CLVCACRatio < 3)
        {
            assessment.Recommendations.Add("优化获客渠道，提高客户价值");
        }

        return assessment;
    }

    #endregion
}