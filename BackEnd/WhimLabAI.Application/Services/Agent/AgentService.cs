using Microsoft.Extensions.Logging;
using Microsoft.EntityFrameworkCore;
using System.Linq.Expressions;
using WhimLabAI.Abstractions.Application;
using WhimLabAI.Domain.Repositories;
using WhimLabAI.Abstractions.Infrastructure;
using WhimLabAI.Domain.Entities.Agent;
using WhimLabAI.Domain.ValueObjects;
using WhimLabAI.Shared.Dtos;
using WhimLabAI.Shared.Enums;
using WhimLabAI.Shared.Extensions;
using WhimLabAI.Shared.Results;

namespace WhimLabAI.Application.Services.Agent;

public class AgentService : IAgentService
{
    private readonly IUnitOfWork _unitOfWork;
    private readonly ICacheService _cacheService;
    private readonly IAIService _aiService;
    private readonly IDataEncryptionService _encryptionService;
    private readonly ILogger<AgentService> _logger;

    public AgentService(
        IUnitOfWork unitOfWork,
        ICacheService cacheService,
        IAIService aiService,
        IDataEncryptionService encryptionService,
        ILogger<AgentService> logger)
    {
        _unitOfWork = unitOfWork;
        _cacheService = cacheService;
        _aiService = aiService;
        _encryptionService = encryptionService;
        _logger = logger;
    }

    public async Task<Result<Guid>> CreateAgentAsync(Shared.Dtos.CreateAgentDto request, Guid creatorId, CancellationToken cancellationToken = default)
    {
        try
        {
            // Generate unique key if not provided
            var uniqueKey = request.UniqueKey;
            if (string.IsNullOrWhiteSpace(uniqueKey))
            {
                uniqueKey = GenerateUniqueKey(request.Name);
            }

            // Validate unique key availability
            if (!await _unitOfWork.Agents.IsUniqueKeyAvailableAsync(uniqueKey, null, cancellationToken))
            {
                return Result<Guid>.Failure("AGENT_KEY_EXISTS", "该唯一标识已被使用");
            }

            // Get category
            Domain.Entities.Agent.AgentCategory? category = null;
            if (!string.IsNullOrWhiteSpace(request.Category))
            {
                category = await GetCategoryByNameAsync(request.Category, cancellationToken);
            }

            // Create agent
            var agent = new Domain.Entities.Agent.Agent(
                name: request.Name,
                creatorId: creatorId,
                uniqueKey: uniqueKey,
                description: request.Description,
                categoryId: category?.Id
            );

            // Update agent with icon and cover if provided
            if (!string.IsNullOrEmpty(request.Icon) || !string.IsNullOrEmpty(request.Cover))
            {
                agent.Update(icon: request.Icon, cover: request.Cover);
            }

            // Create model configuration
            var modelProvider = request.Config.ModelType;
            var modelName = request.Config.ModelConfig.GetValueOrDefault("model", "gpt-3.5-turbo")?.ToString() ?? "gpt-3.5-turbo";
            var temperature = Convert.ToDouble(request.Config.Temperature);
            var maxTokens = request.Config.MaxTokens;
            
            var modelConfig = Domain.ValueObjects.ModelConfiguration.Create(
                modelType: modelProvider,
                modelName: modelName,
                temperature: temperature,
                maxTokens: maxTokens
            );

            // Get current version and update configuration
            var currentVersion = agent.Versions.FirstOrDefault(v => v.Id == agent.CurrentVersionId);
            if (currentVersion != null)
            {
                currentVersion.UpdateConfiguration(
                    modelConfig: modelConfig,
                    systemPrompt: request.Config.SystemPrompt,
                    userPrompt: request.Config.UserPrompt,
                    plugins: request.Config.Plugins?.ToList() ?? new List<string>(),
                    knowledgeBases: request.Config.KnowledgeBases?.Select(kb => kb.ToString()).ToList()
                );
            }

            // Handle Dify-specific configuration
            if (modelProvider.Equals("Dify", StringComparison.OrdinalIgnoreCase))
            {
                var difyApiKey = request.Config.ModelConfig.GetValueOrDefault("apiKey", string.Empty)?.ToString();
                var difyAppId = request.Config.ModelConfig.GetValueOrDefault("appId", string.Empty)?.ToString();
                var difyAppType = request.Config.ModelConfig.GetValueOrDefault("appType", "Chat")?.ToString(); // Default to Chat
                
                // Add Dify app type to model configuration
                if (!string.IsNullOrEmpty(difyAppType))
                {
                    modelConfig = modelConfig.WithAdditionalSetting("DifyAppType", difyAppType);
                    
                    // Update the version with new model config
                    currentVersion?.UpdateConfiguration(
                        modelConfig: modelConfig,
                        systemPrompt: currentVersion.SystemPrompt,
                        userPrompt: currentVersion.UserPrompt,
                        plugins: currentVersion.Plugins.ToList(),
                        knowledgeBases: currentVersion.KnowledgeBases.ToList()
                    );
                }
                
                if (!string.IsNullOrEmpty(difyApiKey) && !string.IsNullOrEmpty(difyAppId))
                {
                    // Encrypt the API key before storing
                    var encryptedApiKey = _encryptionService.EncryptString(difyApiKey);
                    agent.AddDifyApiKey(encryptedApiKey, difyAppId);
                }
            }

            // Add tags
            foreach (var tagName in request.Tags)
            {
                if (!string.IsNullOrWhiteSpace(tagName))
                {
                    agent.AddTag(tagName);
                }
            }

            // Version is already created in the Agent constructor
            // Set the current version ID to avoid circular dependency
            agent.SetInitialVersion();

            await _unitOfWork.Agents.AddAsync(agent, cancellationToken);
            await _unitOfWork.SaveChangesAsync(cancellationToken);

            _logger.LogInformation("Agent created successfully: {AgentId}", agent.Id);
            return Result<Guid>.Success(agent.Id);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating agent");
            return Result<Guid>.Failure("CREATE_AGENT_ERROR", "创建Agent失败");
        }
    }

    public async Task<Result> UpdateAgentAsync(Guid agentId, Shared.Dtos.UpdateAgentDto request, Guid userId, CancellationToken cancellationToken = default)
    {
        try
        {
            var agent = await GetAgentWithDetailsAsync(agentId, cancellationToken);
            if (agent == null)
            {
                return Result.Failure("AGENT_NOT_FOUND", "Agent不存在");
            }

            // Check ownership
            if (agent.CreatorId != userId)
            {
                return Result.Failure("UNAUTHORIZED", "无权修改此Agent");
            }

            // Get category ID if category name is provided
            Guid? categoryId = null;
            if (request.Category != null)
            {
                var category = await GetCategoryByNameAsync(request.Category, cancellationToken);
                categoryId = category?.Id;
            }

            // Update basic info
            agent.Update(
                name: request.Name,
                description: request.Description,
                icon: request.Icon,
                categoryId: categoryId
            );

            // Update tags
            if (request.Tags != null)
            {
                agent.ClearTags();
                foreach (var tagName in request.Tags)
                {
                    if (!string.IsNullOrWhiteSpace(tagName))
                    {
                        agent.AddTag(tagName);
                    }
                }
            }

            // Update configuration if provided
            if (request.Config != null && agent.CurrentVersion != null)
            {
                var modelConfig = ModelConfiguration.Create(
                    request.Config.ModelType ?? agent.CurrentVersion.ModelConfig?.ModelType ?? "OpenAI",
                    (request.Config.ModelConfig.GetValueOrDefault("model", "gpt-3.5-turbo") as string) ?? agent.CurrentVersion.ModelConfig?.ModelName ?? "gpt-3.5-turbo",
                    request.Config.Temperature,
                    request.Config.MaxTokens
                );
                
                agent.CurrentVersion.UpdateConfiguration(
                    modelConfig,
                    request.Config.SystemPrompt,
                    request.Config.UserPrompt
                );
            }

            // UpdatedAt is handled by domain methods

            await _unitOfWork.SaveChangesAsync(cancellationToken);

            // Clear cache
            await _cacheService.RemoveAsync($"agent:{agentId}", cancellationToken);

            _logger.LogInformation("Agent updated successfully: {AgentId}", agentId);
            return Result.Success();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating agent: {AgentId}", agentId);
            return Result.Failure("UPDATE_AGENT_ERROR", "更新Agent失败");
        }
    }

    public async Task<Result> PublishAgentAsync(Guid agentId, Guid versionId, Guid userId, CancellationToken cancellationToken = default)
    {
        try
        {
            var agent = await GetAgentWithDetailsAsync(agentId, cancellationToken);
            if (agent == null)
            {
                return Result.Failure("AGENT_NOT_FOUND", "Agent不存在");
            }

            // Check ownership
            if (agent.CreatorId != userId)
            {
                return Result.Failure("UNAUTHORIZED", "无权发布此Agent");
            }

            // Publish agent
            agent.Publish();

            await _unitOfWork.SaveChangesAsync(cancellationToken);

            // Clear cache
            await _cacheService.RemoveAsync($"agent:{agentId}", cancellationToken);

            _logger.LogInformation("Agent published successfully: {AgentId}", agentId);
            return Result.Success();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error publishing agent: {AgentId}", agentId);
            return Result.Failure("PUBLISH_AGENT_ERROR", "发布Agent失败");
        }
    }

    public async Task<Result> ArchiveAgentAsync(Guid agentId, Guid userId, CancellationToken cancellationToken = default)
    {
        try
        {
            var agent = await _unitOfWork.Agents.GetByIdAsync(agentId, cancellationToken);
            if (agent == null)
            {
                return Result.Failure("AGENT_NOT_FOUND", "Agent不存在");
            }

            // Check ownership
            if (agent.CreatorId != userId)
            {
                return Result.Failure("UNAUTHORIZED", "无权归档此Agent");
            }

            // Archive agent
            agent.Archive();

            await _unitOfWork.SaveChangesAsync(cancellationToken);

            // Clear cache
            await _cacheService.RemoveAsync($"agent:{agentId}", cancellationToken);

            _logger.LogInformation("Agent archived successfully: {AgentId}", agentId);
            return Result.Success();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error archiving agent: {AgentId}", agentId);
            return Result.Failure("ARCHIVE_AGENT_ERROR", "归档Agent失败");
        }
    }

    public async Task<Result<PagedResult<AgentListDto>>> GetAgentListAsync(AgentQueryDto query, CancellationToken cancellationToken = default)
    {
        try
        {
            var agents = await GetAgentsWithPagingAsync(query, cancellationToken);
            var totalCount = await GetAgentCountAsync(query, cancellationToken);

            var agentDtos = await MapToAgentListDtosAsync(agents, cancellationToken);

            var result = new PagedResult<AgentListDto>(
                agentDtos,
                totalCount,
                query.PageNumber,
                query.PageSize);

            return Result<PagedResult<AgentListDto>>.Success(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting agent list");
            return Result<PagedResult<AgentListDto>>.Failure("GET_AGENT_LIST_ERROR", "获取Agent列表失败");
        }
    }

    public async Task<Result<AgentDetailDto>> GetAgentDetailAsync(Guid agentId, CancellationToken cancellationToken = default)
    {
        try
        {
            // Try to get from cache first
            var cachedAgent = await _cacheService.GetAsync<AgentDetailDto>($"agent:{agentId}", cancellationToken);
            if (cachedAgent != null)
            {
                return Result<AgentDetailDto>.Success(cachedAgent);
            }

            var agent = await GetAgentWithFullDetailsAsync(agentId, cancellationToken);
            if (agent == null)
            {
                return Result<AgentDetailDto>.Failure("AGENT_NOT_FOUND", "Agent不存在");
            }

            var agentDto = await MapToAgentDetailDtoAsync(agent, cancellationToken);

            // Cache for 5 minutes
            await _cacheService.SetAsync($"agent:{agentId}", agentDto, TimeSpan.FromMinutes(5), cancellationToken);

            return Result<AgentDetailDto>.Success(agentDto);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting agent detail: {AgentId}", agentId);
            return Result<AgentDetailDto>.Failure("GET_AGENT_DETAIL_ERROR", "获取Agent详情失败");
        }
    }

    public async Task<Result<List<AgentVersionDto>>> GetAgentVersionsAsync(Guid agentId, CancellationToken cancellationToken = default)
    {
        try
        {
            var versions = await _unitOfWork.Agents.GetAgentVersionsAsync(agentId, cancellationToken);
            
            var versionDtos = versions.Cast<AgentVersion>().Select(v => new AgentVersionDto
            {
                Id = v.Id,
                VersionNumber = v.VersionNumber.ToString(),
                Status = v.Status.ToString(),
                Config = new AgentConfigDto
                {
                    ModelType = v.ModelConfig?.ModelType ?? "Unknown",
                    ModelConfig = new Dictionary<string, object>
                    {
                        ["model"] = v.ModelConfig?.ModelName ?? "Unknown",
                        ["maxContextTokens"] = v.ModelConfig?.MaxTokens ?? 4096
                    },
                    SystemPrompt = v.SystemPrompt,
                    MaxTokens = v.ModelConfig?.MaxTokens ?? 2048,
                    Temperature = v.ModelConfig?.Temperature ?? 0.7
                },
                ChangeLog = v.ChangeLog,
                CreatedAt = v.CreatedAt
            }).ToList();

            return Result<List<AgentVersionDto>>.Success(versionDtos);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting agent versions: {AgentId}", agentId);
            return Result<List<AgentVersionDto>>.Failure("GET_AGENT_VERSIONS_ERROR", "获取Agent版本列表失败");
        }
    }

    public async Task<Result<AgentTestResponseDto>> TestAgentAsync(Guid agentId, AgentTestRequestDto request, CancellationToken cancellationToken = default)
    {
        try
        {
            var agent = await GetAgentWithDetailsAsync(agentId, cancellationToken);
            if (agent == null)
            {
                return Result<AgentTestResponseDto>.Failure("AGENT_NOT_FOUND", "Agent不存在");
            }

            // Validate agent has proper configuration
            if (agent.CurrentVersion?.ModelConfig == null)
            {
                return Result<AgentTestResponseDto>.Failure("AGENT_NOT_CONFIGURED", "Agent未配置模型");
            }

            // Prepare test request for AI service
            var testRequest = new ProcessMessageDto
            {
                ConversationId = Guid.NewGuid(), // Create a temporary conversation ID for testing
                Message = request.Message,
                Attachments = null,
                Parameters = new Dictionary<string, object>(),
                EnableStreaming = false // Always use non-streaming for testing
            };

            // Add agent-specific parameters
            if (agent.CurrentVersion.ModelConfig.ModelType == "Dify")
            {
                var difyApiKey = agent.GetDifyApiKey();
                if (difyApiKey == null || !difyApiKey.IsValid())
                {
                    return Result<AgentTestResponseDto>.Failure("DIFY_API_KEY_MISSING", "Dify API密钥未配置或已失效");
                }

                testRequest.Parameters["ApiKey"] = _encryptionService.DecryptString(difyApiKey.GetEncryptedApiKey());
                testRequest.Parameters["AppType"] = agent.CurrentVersion.ModelConfig.AdditionalSettings.GetValueOrDefault("DifyAppType", "Chat");
                testRequest.Parameters["DifyAppId"] = difyApiKey.DifyAppId;
            }

            // Set model configuration
            testRequest.Parameters["ModelType"] = agent.CurrentVersion.ModelConfig.ModelType;
            testRequest.Parameters["ModelName"] = agent.CurrentVersion.ModelConfig.ModelName;
            testRequest.Parameters["Temperature"] = agent.CurrentVersion.ModelConfig.Temperature;
            testRequest.Parameters["MaxTokens"] = agent.CurrentVersion.ModelConfig.MaxTokens;
            testRequest.Parameters["SystemPrompt"] = agent.CurrentVersion.SystemPrompt;

            // Execute test
            var startTime = DateTime.UtcNow;
            var result = await _aiService.ProcessMessageAsync(testRequest, Guid.Empty, cancellationToken);
            var responseTime = (DateTime.UtcNow - startTime).TotalMilliseconds;

            if (!result.IsSuccess)
            {
                return Result<AgentTestResponseDto>.Failure("TEST_FAILED", $"测试失败: {result.Error}");
            }

            var response = new AgentTestResponseDto
            {
                Response = result.Value!.Content,
                TokensUsed = result.Value.TokenUsage.TotalTokens,
                ResponseTime = (long)responseTime,
                Metadata = new Dictionary<string, object>
                {
                    ["model"] = agent.CurrentVersion.ModelConfig.ModelName,
                    ["temperature"] = agent.CurrentVersion.ModelConfig.Temperature,
                    ["provider"] = agent.CurrentVersion.ModelConfig.ModelType,
                    ["promptTokens"] = result.Value.TokenUsage.PromptTokens,
                    ["completionTokens"] = result.Value.TokenUsage.CompletionTokens
                }
            };

            // Add Dify-specific metadata if available
            if (result.Value.Metadata != null)
            {
                foreach (var kvp in result.Value.Metadata)
                {
                    response.Metadata[kvp.Key] = kvp.Value;
                }
            }

            return Result<AgentTestResponseDto>.Success(response);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error testing agent: {AgentId}", agentId);
            return Result<AgentTestResponseDto>.Failure("TEST_AGENT_ERROR", "测试Agent失败");
        }
    }

    // Helper methods
    private string GenerateUniqueKey(string name)
    {
        var baseKey = name.ToLower()
            .Replace(" ", "-")
            .Replace("_", "-");
        
        // Remove invalid characters
        baseKey = System.Text.RegularExpressions.Regex.Replace(baseKey, @"[^a-z0-9\-]", "");
        
        // Add timestamp to ensure uniqueness
        return $"{baseKey}-{DateTimeOffset.UtcNow.ToUnixTimeSeconds()}";
    }

    private async Task<Domain.Entities.Agent.AgentCategory?> GetCategoryByNameAsync(string name, CancellationToken cancellationToken)
    {
        // 查找分类
        var categories = await _unitOfWork.Repository<Domain.Entities.Agent.AgentCategory>()
            .GetAsync(c => c.Name == name && c.IsActive, cancellationToken);
        return categories.FirstOrDefault();
    }

    private async Task<Domain.Entities.Agent.Agent?> GetAgentWithDetailsAsync(Guid agentId, CancellationToken cancellationToken)
    {
        return await _unitOfWork.Agents.GetByIdAsync(agentId, cancellationToken);
    }

    private async Task<Domain.Entities.Agent.Agent?> GetAgentWithFullDetailsAsync(Guid agentId, CancellationToken cancellationToken)
    {
        // 获取代理基本信息
        var agent = await GetAgentWithDetailsAsync(agentId, cancellationToken);
        if (agent == null) return null;
        
        // 加载统计信息
        var conversationCount = await _unitOfWork.Repository<Domain.Entities.Conversation.Conversation>()
            .CountAsync(c => c.AgentId == agentId, cancellationToken);
        
        var messages = await _unitOfWork.Repository<Domain.Entities.Conversation.ConversationMessage>()
            .GetAsync(m => m.Conversation.AgentId == agentId, cancellationToken);
        
        var totalTokenUsage = messages.Sum(m => m.TokenCount);
        
        // 加载评分信息
        var messagesWithRatings = messages.Where(m => m.Rating != null).ToList();
        var averageRating = messagesWithRatings.Any() ? messagesWithRatings.Average(m => m.Rating!.Score) : 0;
        
        // 这些统计信息通常会存储在代理的元数据或单独的统计表中
        // 由于Agent实体可能没有直接的统计属性，这里仅演示如何获取数据
        
        return agent;
    }

    private async Task<List<Domain.Entities.Agent.Agent>> GetAgentsWithPagingAsync(AgentQueryDto query, CancellationToken cancellationToken)
    {
        var status = string.IsNullOrWhiteSpace(query.Status) ? "Published" : query.Status;
        var agentsObj = await _unitOfWork.Agents.GetAgentsByStatusAsync(status, cancellationToken);
        
        var agents = agentsObj.Cast<Domain.Entities.Agent.Agent>().ToList();
        
        // Apply filtering
        if (!string.IsNullOrWhiteSpace(query.Keyword))
        {
            agents = agents.Where(a => 
                a.Name.Contains(query.Keyword, StringComparison.OrdinalIgnoreCase) ||
                a.Description.Contains(query.Keyword, StringComparison.OrdinalIgnoreCase)).ToList();
        }

        if (!string.IsNullOrWhiteSpace(query.Category))
        {
            agents = agents.Where(a => a.Category?.Name == query.Category).ToList();
        }

        if (query.CreatorId.HasValue)
        {
            agents = agents.Where(a => a.CreatorId == query.CreatorId.Value).ToList();
        }

        // Apply sorting
        agents = query.SortBy?.ToLower() switch
        {
            "name" => query.Descending ? agents.OrderByDescending(a => a.Name).ToList() : agents.OrderBy(a => a.Name).ToList(),
            "usagecount" => query.Descending ? agents.OrderByDescending(a => a.UsageCount).ToList() : agents.OrderBy(a => a.UsageCount).ToList(),
            _ => query.Descending ? agents.OrderByDescending(a => a.CreatedAt).ToList() : agents.OrderBy(a => a.CreatedAt).ToList()
        };

        // Apply paging
        return agents
            .Skip((query.PageNumber - 1) * query.PageSize)
            .Take(query.PageSize)
            .ToList();
    }

    private async Task<int> GetAgentCountAsync(AgentQueryDto query, CancellationToken cancellationToken)
    {
        // 构建查询表达式
        var repository = _unitOfWork.Repository<Domain.Entities.Agent.Agent>();
        var predicate = BuildAgentSearchPredicate(query);
        
        // 直接执行计数查询，避免加载所有数据
        return await repository.CountAsync(predicate, cancellationToken);
    }
    
    private Expression<Func<Domain.Entities.Agent.Agent, bool>> BuildAgentSearchPredicate(AgentQueryDto query)
    {
        // 使用IQueryable来构建查询条件更简单
        // 这里直接返回一个组合的lambda表达式
        return a => 
            // 关键词搜索
            (string.IsNullOrWhiteSpace(query.Keyword) || 
             a.Name.Contains(query.Keyword) || 
             a.Description.Contains(query.Keyword)) &&
            // 分类过滤
            (string.IsNullOrWhiteSpace(query.Category) || 
             (a.Category != null && a.Category.Name == query.Category)) &&
            // 标签过滤
            (query.Tags == null || !query.Tags.Any() || 
             a.Tags.Any(t => query.Tags.Contains(t.Name))) &&
            // 状态过滤
            (string.IsNullOrWhiteSpace(query.Status) || 
             a.Status.ToString() == query.Status) &&
            // 创建者过滤
            (!query.CreatorId.HasValue || 
             a.CreatorId == query.CreatorId.Value);
    }

    private async Task<List<AgentListDto>> MapToAgentListDtosAsync(List<Domain.Entities.Agent.Agent> agents, CancellationToken cancellationToken)
    {
        var agentDtos = new List<AgentListDto>();

        foreach (var agent in agents)
        {
            var dto = new AgentListDto
            {
                Id = agent.Id,
                Name = agent.Name,
                UniqueKey = agent.UniqueKey,
                Description = agent.Description,
                Category = agent.Category?.Name,
                Tags = agent.Tags.Select(t => t.Name).ToList(),
                Icon = agent.Icon,
                Status = agent.Status.ToString(),
                UsageCount = agent.UsageCount,
                Rating = await CalculateAverageRatingAsync(agent.Id, cancellationToken),
                CreatedAt = agent.CreatedAt,
                UpdatedAt = agent.UpdatedAt
            };

            // 加载创建者信息
            var creator = await _unitOfWork.Repository<Domain.Entities.User.CustomerUser>()
                .GetByIdAsync(agent.CreatorId, cancellationToken);
            
            dto.Creator = new CreatorInfoDto
            {
                Id = agent.CreatorId,
                Username = creator?.Username ?? "Unknown",
                Nickname = creator?.Nickname ?? creator?.Username ?? "Unknown"
            };

            agentDtos.Add(dto);
        }

        return agentDtos;
    }

    private async Task<AgentDetailDto> MapToAgentDetailDtoAsync(Domain.Entities.Agent.Agent agent, CancellationToken cancellationToken)
    {
        var dto = new AgentDetailDto
        {
            Id = agent.Id,
            Name = agent.Name,
            UniqueKey = agent.UniqueKey,
            Description = agent.Description,
            Category = agent.Category?.Name,
            Tags = agent.Tags.Select(t => t.Name).ToList(),
            Icon = agent.Icon,
            Status = agent.Status.ToString(),
            UsageCount = agent.UsageCount,
            Rating = await CalculateAverageRatingAsync(agent.Id, cancellationToken),
            CreatedAt = agent.CreatedAt,
            UpdatedAt = agent.UpdatedAt,
            Config = agent.CurrentVersion != null ? new AgentConfigDto
            {
                ModelType = agent.CurrentVersion.ModelConfig?.ModelType ?? "Unknown",
                ModelConfig = new Dictionary<string, object>
                {
                    ["model"] = agent.CurrentVersion.ModelConfig?.ModelName ?? "Unknown",
                    ["maxContextTokens"] = agent.CurrentVersion.ModelConfig?.MaxTokens ?? 4096
                },
                SystemPrompt = agent.CurrentVersion.SystemPrompt,
                MaxTokens = agent.CurrentVersion.ModelConfig?.MaxTokens ?? 2048,
                Temperature = agent.CurrentVersion.ModelConfig?.Temperature ?? 0.7
            } : new AgentConfigDto(),
            CurrentVersionNumber = agent.CurrentVersion?.VersionNumber.ToString(),
            PublishedAt = agent.PublishedAt,
            Stats = await CalculateAgentStatsAsync(agent, cancellationToken)
        };

        // 加载创建者信息
        var creator = await _unitOfWork.Repository<Domain.Entities.User.CustomerUser>()
            .GetByIdAsync(agent.CreatorId, cancellationToken);
        
        dto.Creator = new CreatorInfoDto
        {
            Id = agent.CreatorId,
            Username = creator?.Username ?? "Unknown",
            Nickname = creator?.Nickname ?? creator?.Username ?? "Unknown"
        };

        return dto;
    }

    private async Task<double> CalculateAverageRatingAsync(Guid agentId, CancellationToken cancellationToken)
    {
        // Try to get from cache first
        var cacheKey = $"agent:rating:{agentId}";
        var cachedRating = await _cacheService.GetAsync<double?>(cacheKey, cancellationToken);
        if (cachedRating.HasValue)
            return cachedRating.Value;
        
        // Get the agent with ratings
        var agents = await _unitOfWork.Repository<Domain.Entities.Agent.Agent>()
            .GetAsync(a => a.Id == agentId, cancellationToken);
        
        var agentEntity = agents.FirstOrDefault();
        if (agentEntity == null || !agentEntity.Ratings.Any())
            return 0;
        
        double rating;
        // Use the agent's calculated average rating property if available
        if (agentEntity.AverageRating > 0)
        {
            rating = agentEntity.AverageRating;
        }
        else
        {
            // Calculate from ratings collection
            rating = Math.Round(agentEntity.Ratings.Average(r => r.Score), 1);
        }
        
        // Cache for 30 minutes
        await _cacheService.SetAsync(cacheKey, rating, TimeSpan.FromMinutes(30), cancellationToken);
        
        return rating;
    }
    
    private async Task<AgentStatsDto> CalculateAgentStatsAsync(Domain.Entities.Agent.Agent agent, CancellationToken cancellationToken)
    {
        // Try to get from cache first
        var cacheKey = $"agent:stats:{agent.Id}";
        var cachedStats = await _cacheService.GetAsync<AgentStatsDto>(cacheKey, cancellationToken);
        if (cachedStats != null)
            return cachedStats;
        
        // 获取会话数据
        var conversations = await _unitOfWork.Repository<Domain.Entities.Conversation.Conversation>()
            .GetAsync(c => c.AgentId == agent.Id, cancellationToken);
        
        var conversationIds = conversations.Select(c => c.Id).ToList();
        
        // 获取消息数据
        var messages = await _unitOfWork.Repository<Domain.Entities.Conversation.ConversationMessage>()
            .GetAsync(m => conversationIds.Contains(m.ConversationId), cancellationToken);
        
        // 获取唯一用户数
        var uniqueUserIds = conversations.Select(c => c.CustomerUserId).Distinct().Count();
        
        // Get the agent with ratings if not already loaded
        if (!agent.Ratings.Any() && agent.RatingCount > 0)
        {
            var agentWithRatings = await _unitOfWork.Repository<Domain.Entities.Agent.Agent>()
                .GetAsync(a => a.Id == agent.Id, cancellationToken);
            agent = agentWithRatings.FirstOrDefault() ?? agent;
        }
        
        var ratings = agent.Ratings.ToList();
        
        var stats = new AgentStatsDto
        {
            TotalConversations = conversations.Count(),
            TotalMessages = messages.Count(),
            TotalTokens = messages.Sum(m => m.TokenCount),
            TotalUsers = uniqueUserIds,
            AverageRating = ratings.Any() ? Math.Round(ratings.Average(r => r.Score), 1) : 0,
            RatingCount = ratings.Count()
        };
        
        // Cache for 15 minutes
        await _cacheService.SetAsync(cacheKey, stats, TimeSpan.FromMinutes(15), cancellationToken);
        
        return stats;
    }

    public async Task<Result<List<AgentCategoryDto>>> GetCategoriesAsync(CancellationToken cancellationToken = default)
    {
        try
        {
            var cacheKey = "agent:categories:all";
            var cached = await _cacheService.GetAsync<List<AgentCategoryDto>>(cacheKey, cancellationToken);
            if (cached != null)
            {
                return Result<List<AgentCategoryDto>>.Success(cached);
            }

            var categories = await _unitOfWork.Repository<AgentCategory>()
                .GetAsync(c => c.IsActive, cancellationToken);

            var result = categories.Select(c => new AgentCategoryDto
            {
                Id = c.Id,
                Name = c.Name,
                DisplayName = c.DisplayName,
                Description = c.Description,
                Icon = c.Icon,
                SortOrder = c.SortOrder,
                AgentCount = c.Agents.Count(a => a.Status == AgentStatus.Published)
            })
            .OrderBy(c => c.SortOrder)
            .ToList();

            await _cacheService.SetAsync(cacheKey, result, TimeSpan.FromHours(1), cancellationToken);

            return Result<List<AgentCategoryDto>>.Success(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get agent categories");
            return Result<List<AgentCategoryDto>>.Failure("GET_CATEGORIES_FAILED", "获取代理分类失败");
        }
    }

    public async Task<Result<List<AgentListDto>>> GetTopRatedAsync(int count, CancellationToken cancellationToken = default)
    {
        try
        {
            var cacheKey = $"agent:toprated:{count}";
            var cached = await _cacheService.GetAsync<List<AgentListDto>>(cacheKey, cancellationToken);
            if (cached != null)
            {
                return Result<List<AgentListDto>>.Success(cached);
            }

            // Get all published agents with ratings
            var agents = await _unitOfWork.Agents.GetAsync(
                a => a.Status == AgentStatus.Published,
                cancellationToken: cancellationToken);

            var agentDtos = new List<AgentListDto>();
            foreach (var agent in agents)
            {
                var stats = await CalculateAgentStatsAsync(agent, cancellationToken);
                if (stats.AverageRating > 0) // Only include agents with ratings
                {
                    // Load related data
                    var category = agent.CategoryId.HasValue 
                        ? await _unitOfWork.Repository<AgentCategory>().GetByIdAsync(agent.CategoryId.Value, cancellationToken)
                        : null;
                    var creator = await _unitOfWork.Repository<Domain.Entities.User.CustomerUser>().GetByIdAsync(agent.CreatorId, cancellationToken);
                    var tags = agent.Tags?.Select(t => t.Name).ToList() ?? new List<string>();
                    
                    agentDtos.Add(new AgentListDto
                    {
                        Id = agent.Id,
                        Name = agent.Name,
                        Description = agent.Description,
                        Icon = agent.Icon,
                        Status = agent.Status.ToString(),
                        Category = category?.DisplayName ?? "未分类",
                        Tags = tags,
                        Creator = creator != null ? new CreatorInfoDto
                        {
                            Id = creator.Id,
                            Username = creator.Username,
                            Avatar = creator.Avatar
                        } : null,
                        CreatedAt = agent.CreatedAt,
                        UpdatedAt = agent.UpdatedAt,
                        Rating = stats.AverageRating,
                        UsageCount = (int)stats.TotalConversations
                    });
                }
            }

            var result = agentDtos
                .OrderByDescending(a => a.Rating)
                .ThenByDescending(a => a.UsageCount)
                .Take(count)
                .ToList();

            await _cacheService.SetAsync(cacheKey, result, TimeSpan.FromMinutes(30), cancellationToken);

            return Result<List<AgentListDto>>.Success(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get top rated agents");
            return Result<List<AgentListDto>>.Failure("GET_TOP_RATED_FAILED", "获取高评分代理失败");
        }
    }

    public async Task<Result<Dictionary<string, AgentCategoryStatisticsDto>>> GetAgentCategoryStatisticsAsync(CancellationToken cancellationToken = default)
    {
        try
        {
            var cacheKey = "agent:category:statistics";
            var cachedStats = await _cacheService.GetAsync<Dictionary<string, AgentCategoryStatisticsDto>>(cacheKey, cancellationToken);
            if (cachedStats != null)
                return Result<Dictionary<string, AgentCategoryStatisticsDto>>.Success(cachedStats);
            
            // Get all categories
            var categories = await _unitOfWork.Repository<AgentCategory>()
                .GetAsync(c => c.IsActive, cancellationToken);
            
            var statistics = new Dictionary<string, AgentCategoryStatisticsDto>();
            
            foreach (var category in categories)
            {
                // Get agents in this category
                var agents = await _unitOfWork.Repository<Domain.Entities.Agent.Agent>()
                    .GetAsync(a => a.CategoryId == category.Id && a.Status == AgentStatus.Published, cancellationToken);
                
                var agentList = agents.ToList();
                
                // Calculate statistics
                var totalAgents = agentList.Count;
                var totalUsage = agentList.Sum(a => a.UsageCount);
                var averageRating = agentList.Any() && agentList.Any(a => a.Ratings.Any()) 
                    ? agentList.Where(a => a.Ratings.Any())
                        .Average(a => a.AverageRating > 0 ? a.AverageRating : a.Ratings.Average(r => r.Score))
                    : 0;
                
                statistics[category.Name] = new AgentCategoryStatisticsDto
                {
                    CategoryId = category.Id,
                    CategoryName = category.Name,
                    CategoryDisplayName = category.DisplayName,
                    TotalAgents = totalAgents,
                    TotalUsage = totalUsage,
                    AverageRating = Math.Round(averageRating, 1),
                    TopAgents = agentList
                        .OrderByDescending(a => a.UsageCount)
                        .Take(5)
                        .Select(a => new AgentSummaryDto
                        {
                            Id = a.Id,
                            Name = a.Name,
                            UsageCount = a.UsageCount,
                            Rating = a.AverageRating
                        })
                        .ToList()
                };
            }
            
            // Cache for 1 hour
            await _cacheService.SetAsync(cacheKey, statistics, TimeSpan.FromHours(1), cancellationToken);
            
            return Result<Dictionary<string, AgentCategoryStatisticsDto>>.Success(statistics);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get agent category statistics");
            return Result<Dictionary<string, AgentCategoryStatisticsDto>>.Failure("GET_STATISTICS_FAILED", "获取分类统计失败");
        }
    }
    
    public async Task<Result<PagedResult<AgentListDto>>> GetPublishedAsync(int page, int pageSize, string? category, string? sortBy, CancellationToken cancellationToken = default)
    {
        try
        {
            Expression<Func<Domain.Entities.Agent.Agent, bool>> filter = a => a.Status == AgentStatus.Published;

            if (!string.IsNullOrEmpty(category))
            {
                filter = a => a.Status == AgentStatus.Published && a.Category != null && a.Category.Name == category;
            }

            var agents = await _unitOfWork.Agents.GetAsync(filter, cancellationToken: cancellationToken);
            var query = agents.AsQueryable();

            // Apply sorting
            query = sortBy?.ToLower() switch
            {
                "name" => query.OrderBy(a => a.Name),
                "newest" => query.OrderByDescending(a => a.CreatedAt),
                "updated" => query.OrderByDescending(a => a.UpdatedAt),
                _ => query.OrderByDescending(a => a.CreatedAt)
            };

            var totalCount = query.Count();
            var items = query
                .Skip((page - 1) * pageSize)
                .Take(pageSize)
                .ToList();

            var agentDtos = new List<AgentListDto>();
            foreach (var agent in items)
            {
                var stats = await CalculateAgentStatsAsync(agent, cancellationToken);
                // Load related data
                var agentCategory = agent.CategoryId.HasValue 
                    ? await _unitOfWork.Repository<AgentCategory>().GetByIdAsync(agent.CategoryId.Value, cancellationToken)
                    : null;
                var creator = await _unitOfWork.Repository<Domain.Entities.User.CustomerUser>().GetByIdAsync(agent.CreatorId, cancellationToken);
                var tags = agent.Tags?.Select(t => t.Name).ToList() ?? new List<string>();
                
                agentDtos.Add(new AgentListDto
                {
                    Id = agent.Id,
                    Name = agent.Name,
                    Description = agent.Description,
                    Icon = agent.Icon,
                    Status = agent.Status.ToString(),
                    Category = agentCategory?.DisplayName ?? "未分类",
                    Tags = tags,
                    Creator = creator != null ? new CreatorInfoDto
                    {
                        Id = creator.Id,
                        Username = creator.Username,
                        Avatar = creator.Avatar
                    } : null,
                    CreatedAt = agent.CreatedAt,
                    UpdatedAt = agent.UpdatedAt,
                    Rating = stats.AverageRating,
                    UsageCount = (int)stats.TotalConversations
                });
            }

            var result = new PagedResult<AgentListDto>(agentDtos, totalCount, page, pageSize);
            return Result<PagedResult<AgentListDto>>.Success(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get published agents");
            return Result<PagedResult<AgentListDto>>.Failure("GET_PUBLISHED_FAILED", "获取已发布代理失败");
        }
    }
}