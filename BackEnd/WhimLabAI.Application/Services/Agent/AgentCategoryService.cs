using Microsoft.Extensions.Logging;
using WhimLabAI.Abstractions.Application;
using WhimLabAI.Domain.Repositories;
using WhimLabAI.Domain.Entities.Agent;
using WhimLabAI.Shared.Dtos;
using WhimLabAI.Shared.Results;
using WhimLabAI.Shared.Enums;

namespace WhimLabAI.Application.Services.Agent;

public class AgentCategoryService : IAgentCategoryService
{
    private readonly IUnitOfWork _unitOfWork;
    private readonly ILogger<AgentCategoryService> _logger;

    public AgentCategoryService(
        IUnitOfWork unitOfWork,
        ILogger<AgentCategoryService> logger)
    {
        _unitOfWork = unitOfWork;
        _logger = logger;
    }

    public async Task<Result<List<AgentCategoryDto>>> GetCategoriesAsync(CancellationToken cancellationToken = default)
    {
        try
        {
            var categoryRepository = _unitOfWork.Repository<AgentCategory>();
            var categories = await categoryRepository.GetAllAsync(cancellationToken);
            
            var categoryDtos = new List<AgentCategoryDto>();
            
            foreach (var category in categories.OrderBy(c => c.SortOrder).ThenBy(c => c.Name))
            {
                // Get real agent count for this category
                var agentCount = await _unitOfWork.Repository<Domain.Entities.Agent.Agent>()
                    .CountAsync(a => a.CategoryId == category.Id && a.Status == AgentStatus.Published, cancellationToken);
                
                categoryDtos.Add(new AgentCategoryDto
                {
                    Id = category.Id,
                    Name = category.Name,
                    DisplayName = category.DisplayName,
                    Description = category.Description,
                    Icon = category.Icon,
                    ParentId = category.ParentId,
                    DisplayOrder = category.SortOrder,
                    IsActive = category.IsActive,
                    AgentCount = agentCount
                });
            }

            return Result<List<AgentCategoryDto>>.Success(categoryDtos);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting agent categories");
            return Result<List<AgentCategoryDto>>.Failure("GET_CATEGORIES_ERROR", "获取分类列表失败");
        }
    }

    public async Task<Result<AgentCategoryDto>> GetCategoryAsync(Guid categoryId, CancellationToken cancellationToken = default)
    {
        try
        {
            var categoryRepository = _unitOfWork.Repository<AgentCategory>();
            var category = await categoryRepository.GetByIdAsync(categoryId, cancellationToken);
            
            if (category == null)
            {
                return Result<AgentCategoryDto>.Failure("CATEGORY_NOT_FOUND", "分类不存在");
            }

            // Get real agent count for this category
            var agentCount = await _unitOfWork.Repository<Domain.Entities.Agent.Agent>()
                .CountAsync(a => a.CategoryId == category.Id && a.Status == AgentStatus.Published, cancellationToken);
            
            var categoryDto = new AgentCategoryDto
            {
                Id = category.Id,
                Name = category.Name,
                DisplayName = category.DisplayName,
                Description = category.Description,
                Icon = category.Icon,
                ParentId = category.ParentId,
                DisplayOrder = category.SortOrder,
                IsActive = category.IsActive,
                AgentCount = agentCount
            };

            return Result<AgentCategoryDto>.Success(categoryDto);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting agent category: {CategoryId}", categoryId);
            return Result<AgentCategoryDto>.Failure("GET_CATEGORY_ERROR", "获取分类失败");
        }
    }

    public async Task<Result<AgentCategoryDto>> CreateCategoryAsync(CreateAgentCategoryDto request, CancellationToken cancellationToken = default)
    {
        try
        {
            var category = new AgentCategory(
                request.Name,
                request.DisplayName,
                request.Description,
                request.Icon,
                request.ParentId);
            
            if (request.DisplayOrder.HasValue)
            {
                category.SetSortOrder(request.DisplayOrder.Value);
            }

            var categoryRepository = _unitOfWork.Repository<AgentCategory>();
            await categoryRepository.AddAsync(category, cancellationToken);
            await _unitOfWork.SaveChangesAsync(cancellationToken);

            var categoryDto = new AgentCategoryDto
            {
                Id = category.Id,
                Name = category.Name,
                DisplayName = category.DisplayName,
                Description = category.Description,
                Icon = category.Icon,
                ParentId = category.ParentId,
                DisplayOrder = category.SortOrder,
                IsActive = category.IsActive,
                AgentCount = 0
            };

            _logger.LogInformation("Agent category created: {CategoryId} - {CategoryName}", category.Id, category.Name);
            return Result<AgentCategoryDto>.Success(categoryDto);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating agent category");
            return Result<AgentCategoryDto>.Failure("CREATE_CATEGORY_ERROR", "创建分类失败");
        }
    }

    public async Task<Result<AgentCategoryDto>> UpdateCategoryAsync(Guid categoryId, UpdateAgentCategoryDto request, CancellationToken cancellationToken = default)
    {
        try
        {
            var categoryRepository = _unitOfWork.Repository<AgentCategory>();
            var category = await categoryRepository.GetByIdAsync(categoryId, cancellationToken);
            
            if (category == null)
            {
                return Result<AgentCategoryDto>.Failure("CATEGORY_NOT_FOUND", "分类不存在");
            }

            category.Update(
                request.DisplayName,
                request.Description,
                request.Icon);
            
            if (request.ParentId != category.ParentId)
            {
                category.SetParent(request.ParentId);
            }
            
            if (request.DisplayOrder.HasValue)
            {
                category.SetSortOrder(request.DisplayOrder.Value);
            }

            if (request.IsActive.HasValue)
            {
                if (request.IsActive.Value)
                    category.Activate();
                else
                    category.Deactivate();
            }

            await _unitOfWork.SaveChangesAsync(cancellationToken);

            var categoryDto = new AgentCategoryDto
            {
                Id = category.Id,
                Name = category.Name,
                DisplayName = category.DisplayName,
                Description = category.Description,
                Icon = category.Icon,
                ParentId = category.ParentId,
                DisplayOrder = category.SortOrder,
                IsActive = category.IsActive,
                AgentCount = 0
            };

            _logger.LogInformation("Agent category updated: {CategoryId}", categoryId);
            return Result<AgentCategoryDto>.Success(categoryDto);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating agent category: {CategoryId}", categoryId);
            return Result<AgentCategoryDto>.Failure("UPDATE_CATEGORY_ERROR", "更新分类失败");
        }
    }

    public async Task<Result> DeleteCategoryAsync(Guid categoryId, CancellationToken cancellationToken = default)
    {
        try
        {
            var categoryRepository = _unitOfWork.Repository<AgentCategory>();
            var category = await categoryRepository.GetByIdAsync(categoryId, cancellationToken);
            
            if (category == null)
            {
                return Result.Failure("CATEGORY_NOT_FOUND", "分类不存在");
            }

            // Check if category has agents
            var agentRepository = _unitOfWork.Repository<Domain.Entities.Agent.Agent>();
            var hasAgents = await agentRepository.AnyAsync(a => a.CategoryId == categoryId, cancellationToken);
            
            if (hasAgents)
            {
                return Result.Failure("CATEGORY_HAS_AGENTS", "分类下存在代理，无法删除");
            }

            categoryRepository.Remove(category);
            await _unitOfWork.SaveChangesAsync(cancellationToken);

            _logger.LogInformation("Agent category deleted: {CategoryId}", categoryId);
            return Result.Success();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deleting agent category: {CategoryId}", categoryId);
            return Result.Failure("DELETE_CATEGORY_ERROR", "删除分类失败");
        }
    }
}