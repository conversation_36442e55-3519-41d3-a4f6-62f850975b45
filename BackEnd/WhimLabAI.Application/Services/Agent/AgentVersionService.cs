using Microsoft.Extensions.Logging;
using WhimLabAI.Abstractions.Application;
using WhimLabAI.Domain.Repositories;
using WhimLabAI.Domain.Entities.Agent;
using WhimLabAI.Shared.Dtos;
using WhimLabAI.Shared.Enums;
using WhimLabAI.Shared.Results;

namespace WhimLabAI.Application.Services.Agent;

public class AgentVersionService : IAgentVersionService
{
    private readonly IUnitOfWork _unitOfWork;
    private readonly ILogger<AgentVersionService> _logger;

    public AgentVersionService(
        IUnitOfWork unitOfWork,
        ILogger<AgentVersionService> logger)
    {
        _unitOfWork = unitOfWork;
        _logger = logger;
    }

    public async Task<Result<AgentVersionDto>> CreateVersionAsync(Guid agentId, CreateAgentVersionDto request, Guid userId, CancellationToken cancellationToken = default)
    {
        try
        {
            // Get agent with versions
            var agentRepository = _unitOfWork.Repository<Domain.Entities.Agent.Agent>();
            var agent = await agentRepository.GetByIdAsync(agentId, cancellationToken);
            if (agent == null)
            {
                return Result<AgentVersionDto>.Failure("AGENT_NOT_FOUND", "Agent不存在");
            }

            // Check ownership
            if (agent.CreatorId != userId)
            {
                return Result<AgentVersionDto>.Failure("UNAUTHORIZED", "无权创建此Agent的版本");
            }

            // Create model configuration
            var modelConfig = Domain.ValueObjects.ModelConfiguration.Create(
                request.Config.ModelType ?? "OpenAI",
                request.Config.ModelConfig?.GetValueOrDefault("model", "gpt-3.5-turbo")?.ToString() ?? "gpt-3.5-turbo",
                request.Config.Temperature,
                request.Config.MaxTokens,
                1.0, // TopP default
                0.0, // FrequencyPenalty default  
                0.0  // PresencePenalty default
            );
            
            // Create new version
            var newVersion = agent.CreateNewVersion(modelConfig);
            
            // Set description as change log
            if (!string.IsNullOrEmpty(request.Description))
            {
                newVersion.SetChangeLog(request.Description);
            }
            
            // Update version configuration
            newVersion.UpdateConfiguration(
                modelConfig,
                request.Config.SystemPrompt,
                null, // userPrompt
                request.Config.Plugins?.ToList(),
                request.Config.KnowledgeBases?.ToList()
            );

            await _unitOfWork.SaveChangesAsync(cancellationToken);

            var versionDto = MapToVersionDto(newVersion);

            _logger.LogInformation("Agent version created: {AgentId} - Version {VersionNumber}", agentId, newVersion.VersionNumber);
            return Result<AgentVersionDto>.Success(versionDto);
        }
        catch (Exception ex)
        {
            _logger.LogError("Error creating agent version: {AgentId}", agentId, ex);
            return Result<AgentVersionDto>.Failure("CREATE_VERSION_ERROR", "创建版本失败");
        }
    }

    public async Task<Result<AgentVersionDto>> GetVersionAsync(Guid agentId, Guid versionId, CancellationToken cancellationToken = default)
    {
        try
        {
            var versionRepository = _unitOfWork.Repository<AgentVersion>();
            var versions = await versionRepository.GetAsync(v => v.AgentId == agentId, cancellationToken);
            var versionList = versions.ToList();
            
            var version = versionList.FirstOrDefault(v => v.Id == versionId);
            if (version == null)
            {
                return Result<AgentVersionDto>.Failure("VERSION_NOT_FOUND", "版本不存在");
            }

            var versionDto = MapToVersionDto(version);
            return Result<AgentVersionDto>.Success(versionDto);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting agent version: {AgentId} - {VersionId}", agentId, versionId);
            return Result<AgentVersionDto>.Failure("GET_VERSION_ERROR", "获取版本失败");
        }
    }

    public async Task<Result<List<AgentVersionDto>>> GetVersionsAsync(Guid agentId, CancellationToken cancellationToken = default)
    {
        try
        {
            var versionRepository = _unitOfWork.Repository<AgentVersion>();
            var versions = await versionRepository.GetAsync(v => v.AgentId == agentId, cancellationToken);
            var versionList = versions.ToList();

            var versionDtos = versionList
                .OrderByDescending(v => v.CreatedAt)
                .Select(MapToVersionDto)
                .ToList();

            return Result<List<AgentVersionDto>>.Success(versionDtos);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting agent versions: {AgentId}", agentId);
            return Result<List<AgentVersionDto>>.Failure("GET_VERSIONS_ERROR", "获取版本列表失败");
        }
    }

    public async Task<Result> PublishVersionAsync(Guid agentId, Guid versionId, Guid userId, CancellationToken cancellationToken = default)
    {
        try
        {
            // Get agent with versions
            var agentRepository = _unitOfWork.Repository<Domain.Entities.Agent.Agent>();
            var agent = await agentRepository.GetByIdAsync(agentId, cancellationToken);
            if (agent == null)
            {
                return Result.Failure("AGENT_NOT_FOUND", "Agent不存在");
            }

            // Check ownership
            if (agent.CreatorId != userId)
            {
                return Result.Failure("UNAUTHORIZED", "无权发布此Agent的版本");
            }

            // Find the version
            var version = agent.Versions.FirstOrDefault(v => v.Id == versionId);
            if (version == null)
            {
                return Result.Failure("VERSION_NOT_FOUND", "版本不存在");
            }

            // Archive all other published versions
            foreach (var v in agent.Versions.Where(v => v.Status == AgentStatus.Published))
            {
                v.Archive();
            }

            // Publish the selected version
            version.Publish();

            // Agent configuration is stored in the version, no need to sync

            // Publish the agent if not already published
            if (agent.Status != AgentStatus.Published)
            {
                agent.Publish();
            }

            await _unitOfWork.SaveChangesAsync(cancellationToken);

            _logger.LogInformation("Agent version published: {AgentId} - Version {VersionId}", agentId, versionId);
            return Result.Success();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error publishing agent version: {AgentId} - {VersionId}", agentId, versionId);
            return Result.Failure("PUBLISH_VERSION_ERROR", "发布版本失败");
        }
    }

    public async Task<Result> RollbackToVersionAsync(Guid agentId, Guid versionId, Guid userId, CancellationToken cancellationToken = default)
    {
        try
        {
            // Get agent with versions
            var agentRepository = _unitOfWork.Repository<Domain.Entities.Agent.Agent>();
            var agent = await agentRepository.GetByIdAsync(agentId, cancellationToken);
            if (agent == null)
            {
                return Result.Failure("AGENT_NOT_FOUND", "Agent不存在");
            }

            // Check ownership
            if (agent.CreatorId != userId)
            {
                return Result.Failure("UNAUTHORIZED", "无权回滚此Agent的版本");
            }

            // Find the version
            var version = agent.Versions.FirstOrDefault(v => v.Id == versionId);
            if (version == null)
            {
                return Result.Failure("VERSION_NOT_FOUND", "版本不存在");
            }

            // Create a new version based on the selected version
            var rollbackVersion = agent.CreateNewVersion();
            
            // Copy configuration from the selected version
            rollbackVersion.UpdateConfiguration(
                version.ModelConfig,
                version.SystemPrompt,
                version.UserPrompt,
                version.Plugins.ToList(),
                version.KnowledgeBases.ToList());
            
            rollbackVersion.SetChangeLog($"回滚到版本 {version.VersionNumber}");

            await _unitOfWork.SaveChangesAsync(cancellationToken);

            _logger.LogInformation("Agent rolled back to version: {AgentId} - Version {VersionId}", agentId, versionId);
            return Result.Success();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error rolling back agent version: {AgentId} - {VersionId}", agentId, versionId);
            return Result.Failure("ROLLBACK_VERSION_ERROR", "回滚版本失败");
        }
    }

    public async Task<Result<AgentVersionComparisonDto>> CompareVersionsAsync(Guid agentId, Guid versionId1, Guid versionId2, CancellationToken cancellationToken = default)
    {
        try
        {
            var versionRepository = _unitOfWork.Repository<AgentVersion>();
            var versions = await versionRepository.GetAsync(v => v.AgentId == agentId, cancellationToken);
            var versionList = versions.ToList();
            
            var version1 = versionList.FirstOrDefault(v => v.Id == versionId1);
            var version2 = versionList.FirstOrDefault(v => v.Id == versionId2);

            if (version1 == null || version2 == null)
            {
                return Result<AgentVersionComparisonDto>.Failure("VERSION_NOT_FOUND", "版本不存在");
            }

            var differences = new List<VersionDifference>();

            // Compare prompt
            if (version1.SystemPrompt != version2.SystemPrompt)
            {
                differences.Add(new VersionDifference
                {
                    Field = "系统提示词",
                    OldValue = version1.SystemPrompt,
                    NewValue = version2.SystemPrompt
                });
            }

            // Compare model configuration
            if (version1.ModelConfig.ModelType != version2.ModelConfig.ModelType ||
                version1.ModelConfig.ModelName != version2.ModelConfig.ModelName)
            {
                differences.Add(new VersionDifference
                {
                    Field = "模型配置",
                    OldValue = $"{version1.ModelConfig.ModelType} - {version1.ModelConfig.ModelName}",
                    NewValue = $"{version2.ModelConfig.ModelType} - {version2.ModelConfig.ModelName}"
                });
            }

            // Compare temperature
            if (version1.ModelConfig.Temperature != version2.ModelConfig.Temperature)
            {
                differences.Add(new VersionDifference
                {
                    Field = "Temperature",
                    OldValue = version1.ModelConfig.Temperature,
                    NewValue = version2.ModelConfig.Temperature
                });
            }

            // Compare max tokens
            if (version1.ModelConfig.MaxTokens != version2.ModelConfig.MaxTokens)
            {
                differences.Add(new VersionDifference
                {
                    Field = "最大令牌数",
                    OldValue = version1.ModelConfig.MaxTokens,
                    NewValue = version2.ModelConfig.MaxTokens
                });
            }

            var comparison = new AgentVersionComparisonDto
            {
                Version1 = MapToVersionDto(version1),
                Version2 = MapToVersionDto(version2),
                Differences = differences
            };

            return Result<AgentVersionComparisonDto>.Success(comparison);
        }
        catch (Exception ex)
        {
            _logger.LogError("Error comparing agent versions: {AgentId} - {VersionId1} vs {VersionId2}", agentId, versionId1, versionId2, ex);
            return Result<AgentVersionComparisonDto>.Failure("COMPARE_VERSIONS_ERROR", "比较版本失败");
        }
    }

    // New approval workflow methods
    public async Task<Result> SubmitForReviewAsync(Guid agentId, Guid versionId, Guid userId, CancellationToken cancellationToken = default)
    {
        try
        {
            var agentRepository = _unitOfWork.Repository<Domain.Entities.Agent.Agent>();
            var agent = await agentRepository.GetByIdAsync(agentId, cancellationToken);
            if (agent == null)
            {
                return Result.Failure("AGENT_NOT_FOUND", "Agent不存在");
            }

            if (agent.CreatorId != userId)
            {
                return Result.Failure("UNAUTHORIZED", "无权操作此Agent的版本");
            }

            var version = agent.Versions.FirstOrDefault(v => v.Id == versionId);
            if (version == null)
            {
                return Result.Failure("VERSION_NOT_FOUND", "版本不存在");
            }

            version.SubmitForReview();
            await _unitOfWork.SaveChangesAsync(cancellationToken);

            _logger.LogInformation("Agent version submitted for review: {AgentId} - Version {VersionId}", agentId, versionId);
            return Result.Success();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error submitting agent version for review: {AgentId} - {VersionId}", agentId, versionId);
            return Result.Failure("SUBMIT_REVIEW_ERROR", ex.Message);
        }
    }

    public async Task<Result> ReviewVersionAsync(Guid agentId, Guid versionId, ReviewVersionDto request, Guid reviewerId, CancellationToken cancellationToken = default)
    {
        try
        {
            var agentRepository = _unitOfWork.Repository<Domain.Entities.Agent.Agent>();
            var agent = await agentRepository.GetByIdAsync(agentId, cancellationToken);
            if (agent == null)
            {
                return Result.Failure("AGENT_NOT_FOUND", "Agent不存在");
            }

            var version = agent.Versions.FirstOrDefault(v => v.Id == versionId);
            if (version == null)
            {
                return Result.Failure("VERSION_NOT_FOUND", "版本不存在");
            }

            // Parse review status
            if (!Enum.TryParse<Shared.Enums.ReviewStatus>(request.Status, out var reviewStatus))
            {
                return Result.Failure("INVALID_STATUS", "无效的审核状态");
            }

            version.AddReview(reviewerId, reviewStatus, request.Comments);
            await _unitOfWork.SaveChangesAsync(cancellationToken);

            _logger.LogInformation("Agent version reviewed: {AgentId} - Version {VersionId} - Status {Status}", agentId, versionId, reviewStatus);
            return Result.Success();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error reviewing agent version: {AgentId} - {VersionId}", agentId, versionId);
            return Result.Failure("REVIEW_VERSION_ERROR", "审核版本失败");
        }
    }

    public async Task<Result<List<AgentVersionReviewDto>>> GetVersionReviewsAsync(Guid agentId, Guid versionId, CancellationToken cancellationToken = default)
    {
        try
        {
            var versionRepository = _unitOfWork.Repository<AgentVersion>();
            var versions = await versionRepository.GetAsync(v => v.AgentId == agentId, cancellationToken);
            var version = versions.FirstOrDefault(v => v.Id == versionId);
            
            if (version == null)
            {
                return Result<List<AgentVersionReviewDto>>.Failure("VERSION_NOT_FOUND", "版本不存在");
            }

            // Get reviewer IDs
            var reviewerIds = version.Reviews.Select(r => r.ReviewerId).Distinct().ToList();
            
            // Load admin users for reviewer names
            var adminUsers = await _unitOfWork.Repository<Domain.Entities.User.AdminUser>()
                .GetAsync(u => reviewerIds.Contains(u.Id), cancellationToken);
            var adminUserDict = adminUsers.ToDictionary(u => u.Id);
            
            var reviewDtos = version.Reviews.Select(r => new AgentVersionReviewDto
            {
                Id = r.Id,
                VersionId = r.AgentVersionId,
                ReviewerId = r.ReviewerId,
                ReviewerName = adminUserDict.TryGetValue(r.ReviewerId, out var reviewer) 
                    ? reviewer.Username 
                    : "Unknown Reviewer",
                Status = r.Status.ToString(),
                Comments = r.Comments,
                ReviewedAt = r.ReviewedAt,
                ReviewDetails = r.ReviewDetails
            }).ToList();

            return Result<List<AgentVersionReviewDto>>.Success(reviewDtos);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting version reviews: {AgentId} - {VersionId}", agentId, versionId);
            return Result<List<AgentVersionReviewDto>>.Failure("GET_REVIEWS_ERROR", "获取审核记录失败");
        }
    }

    public async Task<Result<AgentVersionUsageStatsDto>> GetVersionUsageStatsAsync(Guid agentId, Guid versionId, DateTime? startDate = null, DateTime? endDate = null, CancellationToken cancellationToken = default)
    {
        try
        {
            var versionRepository = _unitOfWork.Repository<AgentVersion>();
            var versions = await versionRepository.GetAsync(v => v.AgentId == agentId, cancellationToken);
            var version = versions.FirstOrDefault(v => v.Id == versionId);
            
            if (version == null)
            {
                return Result<AgentVersionUsageStatsDto>.Failure("VERSION_NOT_FOUND", "版本不存在");
            }

            var start = startDate ?? version.CreatedAt.Date;
            var end = endDate ?? DateTime.UtcNow.Date;

            var dailyStats = version.UsageStats
                .Where(s => s.Date >= start && s.Date <= end)
                .OrderBy(s => s.Date)
                .Select(s => new DailyUsageStatsDto
                {
                    Date = s.Date,
                    ConversationCount = s.ConversationCount,
                    MessageCount = s.MessageCount,
                    TokensConsumed = s.TotalTokensConsumed,
                    UniqueUserCount = s.UniqueUserCount,
                    AverageResponseTime = s.AverageResponseTime,
                    ErrorCount = s.ErrorCount,
                    SuccessRate = s.SuccessRate
                })
                .ToList();

            var statsDto = new AgentVersionUsageStatsDto
            {
                VersionId = versionId,
                VersionNumber = version.VersionNumber.ToString(),
                StartDate = start,
                EndDate = end,
                TotalConversations = dailyStats.Sum(s => s.ConversationCount),
                TotalMessages = dailyStats.Sum(s => s.MessageCount),
                TotalTokensConsumed = dailyStats.Sum(s => s.TokensConsumed),
                UniqueUsers = dailyStats.Max(s => s.UniqueUserCount),
                AverageResponseTime = dailyStats.Any() ? dailyStats.Average(s => s.AverageResponseTime) : 0,
                TotalErrors = dailyStats.Sum(s => s.ErrorCount),
                SuccessRate = dailyStats.Any() ? dailyStats.Average(s => s.SuccessRate) : 100,
                DailyStats = dailyStats
            };

            return Result<AgentVersionUsageStatsDto>.Success(statsDto);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting version usage stats: {AgentId} - {VersionId}", agentId, versionId);
            return Result<AgentVersionUsageStatsDto>.Failure("GET_USAGE_STATS_ERROR", "获取使用统计失败");
        }
    }

    public async Task<Result> RecordVersionUsageAsync(Guid versionId, VersionUsageRecordDto usageRecord, CancellationToken cancellationToken = default)
    {
        try
        {
            var versionRepository = _unitOfWork.Repository<AgentVersion>();
            var versions = await versionRepository.GetAsync(v => v.Id == versionId, cancellationToken);
            var version = versions.FirstOrDefault();
            
            if (version == null)
            {
                return Result.Failure("VERSION_NOT_FOUND", "版本不存在");
            }

            version.RecordUsage(
                usageRecord.MessageCount,
                usageRecord.TokensConsumed,
                usageRecord.ResponseTime,
                usageRecord.HasError
            );

            await _unitOfWork.SaveChangesAsync(cancellationToken);
            return Result.Success();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error recording version usage: {VersionId}", versionId);
            return Result.Failure("RECORD_USAGE_ERROR", "记录使用统计失败");
        }
    }

    public async Task<Result<DetailedVersionComparisonDto>> GetDetailedComparisonAsync(Guid agentId, Guid versionId1, Guid versionId2, CancellationToken cancellationToken = default)
    {
        try
        {
            var versionRepository = _unitOfWork.Repository<AgentVersion>();
            var versions = await versionRepository.GetAsync(v => v.AgentId == agentId, cancellationToken);
            var versionList = versions.ToList();
            
            var version1 = versionList.FirstOrDefault(v => v.Id == versionId1);
            var version2 = versionList.FirstOrDefault(v => v.Id == versionId2);

            if (version1 == null || version2 == null)
            {
                return Result<DetailedVersionComparisonDto>.Failure("VERSION_NOT_FOUND", "版本不存在");
            }

            var detailedChanges = version1.GetDetailedComparison(version2);
            var differences = ConvertToDifferences(detailedChanges);

            var comparison = new DetailedVersionComparisonDto
            {
                Version1 = MapToVersionDto(version1),
                Version2 = MapToVersionDto(version2),
                Differences = differences,
                DetailedChanges = detailedChanges,
                Summary = new VersionComparisonSummaryDto
                {
                    TotalChanges = differences.Count,
                    ChangedFields = differences.Select(d => d.Field).ToList(),
                    HasBreakingChanges = DetectBreakingChanges(detailedChanges),
                    ChangeType = DetermineChangeType(detailedChanges)
                }
            };

            return Result<DetailedVersionComparisonDto>.Success(comparison);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting detailed comparison: {AgentId} - {VersionId1} vs {VersionId2}", agentId, versionId1, versionId2);
            return Result<DetailedVersionComparisonDto>.Failure("DETAILED_COMPARE_ERROR", "详细比较失败");
        }
    }

    private List<VersionDifference> ConvertToDifferences(Dictionary<string, object> detailedChanges)
    {
        var differences = new List<VersionDifference>();
        
        foreach (var change in detailedChanges)
        {
            if (change.Value is Dictionary<string, object> dict && dict.ContainsKey("Old") && dict.ContainsKey("New"))
            {
                differences.Add(new VersionDifference
                {
                    Field = change.Key,
                    OldValue = dict["Old"],
                    NewValue = dict["New"]
                });
            }
        }
        
        return differences;
    }

    private bool DetectBreakingChanges(Dictionary<string, object> changes)
    {
        // Consider model type changes or major prompt changes as breaking
        return changes.ContainsKey("ModelType") || changes.ContainsKey("SystemPrompt");
    }

    private string DetermineChangeType(Dictionary<string, object> changes)
    {
        if (DetectBreakingChanges(changes))
            return "Breaking";
        
        if (changes.Count > 3)
            return "Major";
            
        return "Minor";
    }

    private AgentVersionDto MapToVersionDto(AgentVersion version)
    {
        // Calculate usage stats summary
        var totalConversations = version.UsageStats.Sum(s => s.ConversationCount);
        var totalMessages = version.UsageStats.Sum(s => s.MessageCount);
        var totalTokens = version.UsageStats.Sum(s => s.TotalTokensConsumed);
        
        return new AgentVersionDto
        {
            Id = version.Id,
            VersionNumber = version.VersionNumber.ToString(),
            Status = version.Status.ToString(),
            Config = new AgentConfigDto
            {
                ModelType = version.ModelConfig?.ModelType ?? "OpenAI",
                ModelConfig = new Dictionary<string, object>
                {
                    ["model"] = version.ModelConfig?.ModelName ?? "gpt-3.5-turbo",
                    ["maxContextTokens"] = version.ModelConfig?.MaxTokens ?? 4096
                },
                SystemPrompt = version.SystemPrompt,
                MaxTokens = version.ModelConfig?.MaxTokens ?? 4096,
                Temperature = version.ModelConfig?.Temperature ?? 0.7,
                Plugins = version.Plugins.ToList(),
                KnowledgeBases = version.KnowledgeBases.ToList()
            },
            ChangeLog = version.ChangeLog,
            CreatedAt = version.CreatedAt,
            
            // Approval workflow fields
            ReviewStatus = version.ReviewStatus.ToString(),
            SubmittedForReviewAt = version.SubmittedForReviewAt,
            PublishedAt = version.PublishedAt,
            PublishedBy = version.PublishedBy,
            CanPublish = version.CanPublish(),
            PublishAttemptCount = version.PublishAttemptCount,
            
            // Usage statistics summary
            TotalConversations = totalConversations,
            TotalMessages = totalMessages,
            TotalTokensConsumed = totalTokens,
            AverageRating = 0 // Note: To calculate rating async, use a separate method
        };
    }
    
    private async Task<double> CalculateVersionAverageRatingAsync(Guid agentId, Guid versionId, CancellationToken cancellationToken)
    {
        // For now, we'll use the overall agent rating since conversations don't track version
        // In the future, this should be enhanced to track which version was used in each conversation
        var agent = await _unitOfWork.Repository<Domain.Entities.Agent.Agent>()
            .GetAsync(a => a.Id == agentId, cancellationToken);
        
        var agentEntity = agent.FirstOrDefault();
        if (agentEntity == null)
            return 0;
            
        // Return the agent's average rating
        return agentEntity.AverageRating;
    }
}