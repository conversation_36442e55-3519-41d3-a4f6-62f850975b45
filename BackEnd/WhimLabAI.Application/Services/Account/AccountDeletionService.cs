using System;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using WhimLabAI.Abstractions.Application;
using WhimLabAI.Abstractions.Infrastructure;
using WhimLabAI.Domain.DomainEvents;
using WhimLabAI.Domain.Entities.User;
using WhimLabAI.Domain.Repositories;
using WhimLabAI.Shared.Dtos.Account;
using WhimLabAI.Shared.Enums;
using WhimLabAI.Shared.Exceptions;
using WhimLabAI.Shared.Results;
using WhimLabAI.Shared.Utilities;

namespace WhimLabAI.Application.Services.Account;

/// <summary>
/// 账号注销服务实现
/// </summary>
public class AccountDeletionService : IAccountDeletionService
{
    private readonly IAccountDeletionRequestRepository _deletionRequestRepository;
    private readonly ICustomerUserRepository _customerUserRepository;
    private readonly IUnitOfWork _unitOfWork;
    private readonly IComplianceService _complianceService;
    private readonly INotificationService _notificationService;
    private readonly IEmailService _emailService;
    private readonly IAuditLogger _auditLogger;
    private readonly ILogger<AccountDeletionService> _logger;

    public AccountDeletionService(
        IAccountDeletionRequestRepository deletionRequestRepository,
        ICustomerUserRepository customerUserRepository,
        IUnitOfWork unitOfWork,
        IComplianceService complianceService,
        INotificationService notificationService,
        IEmailService emailService,
        IAuditLogger auditLogger,
        ILogger<AccountDeletionService> logger)
    {
        _deletionRequestRepository = deletionRequestRepository;
        _customerUserRepository = customerUserRepository;
        _unitOfWork = unitOfWork;
        _complianceService = complianceService;
        _notificationService = notificationService;
        _emailService = emailService;
        _auditLogger = auditLogger;
        _logger = logger;
    }

    public async Task<Result<AccountDeletionRequestDto>> CreateDeletionRequestAsync(
        Guid userId,
        CreateAccountDeletionRequestDto request,
        string ipAddress,
        string? userAgent = null,
        CancellationToken cancellationToken = default)
    {
        try
        {
            // 获取用户
            var user = await _customerUserRepository.GetByIdAsync(userId, cancellationToken);
            if (user == null)
            {
                return Result<AccountDeletionRequestDto>.Failure("用户不存在");
            }

            // 验证密码
            if (!user.PasswordHash.Verify(request.Password))
            {
                await _auditLogger.LogActionAsync(
                    action: "AccountDeletionRequest_PasswordVerificationFailed",
                    module: "Account",
                    description: $"Password verification failed for user {userId}",
                    additionalData: new { userId, ipAddress, userAgent });
                    
                return Result<AccountDeletionRequestDto>.Failure("密码验证失败");
            }

            // 验证2FA（如果启用）
            if (user.TwoFactorEnabled && !string.IsNullOrEmpty(request.TwoFactorCode))
            {
                if (!user.ValidateTwoFactorCode(request.TwoFactorCode))
                {
                    await _auditLogger.LogActionAsync(
                        action: "AccountDeletionRequest_2FAVerificationFailed",
                        module: "Account",
                        description: $"2FA verification failed for user {userId}",
                        additionalData: new { userId, ipAddress, userAgent });
                        
                    return Result<AccountDeletionRequestDto>.Failure("两步验证码错误");
                }
            }

            // 检查是否已有活跃的注销请求
            if (await _deletionRequestRepository.HasActiveRequestAsync(userId, cancellationToken))
            {
                return Result<AccountDeletionRequestDto>.Failure("您已有一个进行中的注销请求");
            }

            // 生成验证码
            var verificationCode = GenerateVerificationCode();

            // 创建注销请求
            var deletionRequest = new AccountDeletionRequest(
                userId,
                request.Reason,
                request.ReasonCategory,
                ipAddress,
                userAgent,
                verificationCode);

            await _deletionRequestRepository.AddAsync(deletionRequest, cancellationToken);

            // 发送验证邮件
            if (user.Email != null)
            {
                await _emailService.SendAccountDeletionVerificationEmailAsync(
                    user.Email.Value,
                    user.Username,
                    verificationCode,
                    cancellationToken);
            }

            // 发送域事件
            user.RequestDeletion(
                deletionRequest.Id,
                request.Reason,
                request.ReasonCategory,
                deletionRequest.CoolingOffEndAt,
                deletionRequest.PlannedDeletionAt);
            
            _customerUserRepository.Update(user);
            await _unitOfWork.SaveChangesAsync(cancellationToken);

            // 记录审计日志
            await _auditLogger.LogActionAsync(
                action: "AccountDeletionRequest_Created",
                module: "Account",
                description: $"Deletion request created for user {userId}, Reason: {request.ReasonCategory}",
                additionalData: new { userId, requestId = deletionRequest.Id, ipAddress, userAgent });

            _logger.LogInformation("Account deletion request created for user {UserId}", userId);

            return Result<AccountDeletionRequestDto>.Success(MapToDto(deletionRequest, user));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to create account deletion request for user {UserId}", userId);
            return Result<AccountDeletionRequestDto>.Failure($"创建注销请求失败：{ex.Message}");
        }
    }

    public async Task<Result> VerifyDeletionRequestAsync(
        string verificationCode,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var request = await _deletionRequestRepository.GetByVerificationCodeAsync(
                verificationCode, 
                cancellationToken);
                
            if (request == null)
            {
                return Result.Failure("验证码无效或已过期");
            }

            // 验证请求
            request.Verify(verificationCode);
            request.StartCoolingOff();

            _deletionRequestRepository.Update(request);
            await _unitOfWork.SaveChangesAsync(cancellationToken);

            // 发送确认通知
            var customerUser = await _customerUserRepository.GetByIdAsync(request.CustomerUserId, cancellationToken);
            if (customerUser?.Email != null)
            {
                await _emailService.SendAccountDeletionConfirmedEmailAsync(
                    customerUser.Email.Value,
                    customerUser.Username,
                    request.CoolingOffEndAt,
                    cancellationToken);
            }

            // 记录审计日志
            await _auditLogger.LogActionAsync(
                action: "AccountDeletionRequest_Verified",
                module: "Account",
                description: $"Deletion request verified for user {request.CustomerUserId}",
                additionalData: new { userId = request.CustomerUserId, requestId = request.Id });

            _logger.LogInformation("Account deletion request {RequestId} verified", request.Id);

            return Result.Success();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to verify deletion request with code {Code}", verificationCode);
            return Result.Failure($"验证失败：{ex.Message}");
        }
    }

    public async Task<Result> CancelDeletionRequestAsync(
        Guid userId,
        string reason,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var request = await _deletionRequestRepository.GetLatestByCustomerUserIdAsync(
                userId, 
                cancellationToken);
                
            if (request == null)
            {
                return Result.Failure("未找到注销请求");
            }

            if (!request.CanBeCancelled())
            {
                return Result.Failure("该注销请求无法撤销");
            }

            // 撤销请求
            request.Cancel(reason);

            // 获取用户
            var user = await _customerUserRepository.GetByIdAsync(userId, cancellationToken);
            if (user == null)
            {
                return Result.Failure("用户不存在");
            }

            // 如果账号已被冻结，需要恢复
            if (request.Status == DeletionRequestStatus.AccountFrozen)
            {
                user.CancelDeletion();
            }

            // 发送域事件
            user.CancelDeletionRequest(request.Id, reason);
            
            _deletionRequestRepository.Update(request);
            _customerUserRepository.Update(user);

            await _unitOfWork.SaveChangesAsync(cancellationToken);

            // 发送通知
            if (user.Email != null)
            {
                await _emailService.SendAccountDeletionCancelledEmailAsync(
                    user.Email.Value,
                    user.Username,
                    cancellationToken);
            }

            // 记录审计日志
            await _auditLogger.LogActionAsync(
                action: "AccountDeletionRequest_Cancelled",
                module: "Account",
                description: $"Deletion request cancelled for user {userId}, Reason: {reason}",
                additionalData: new { userId, requestId = request.Id });

            _logger.LogInformation("Account deletion request {RequestId} cancelled", request.Id);

            return Result.Success();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to cancel deletion request for user {UserId}", userId);
            return Result.Failure($"撤销失败：{ex.Message}");
        }
    }

    public async Task<Result<AccountDeletionRequestDto?>> GetDeletionRequestStatusAsync(
        Guid userId,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var request = await _deletionRequestRepository.GetLatestByCustomerUserIdAsync(
                userId, 
                cancellationToken);
                
            if (request == null)
            {
                return Result<AccountDeletionRequestDto?>.Success(null);
            }

            var dto = MapToDto(request, request.CustomerUser);
            return Result<AccountDeletionRequestDto?>.Success(dto);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get deletion request status for user {UserId}", userId);
            return Result<AccountDeletionRequestDto?>.Failure($"获取状态失败：{ex.Message}");
        }
    }

    public async Task<Result<int>> ProcessCoolingOffCompletedRequestsAsync(
        CancellationToken cancellationToken = default)
    {
        try
        {
            var requests = await _deletionRequestRepository.GetPendingFreezingRequestsAsync(cancellationToken);
            var processedCount = 0;

            foreach (var request in requests)
            {
                try
                {
                    // 冻结账号
                    request.FreezeAccount();
                    request.CustomerUser.MarkForDeletion(request.Id, request.PlannedDeletionAt);

                    _deletionRequestRepository.Update(request);
                    _customerUserRepository.Update(request.CustomerUser);

                    // 发送通知
                    if (request.CustomerUser.Email != null)
                    {
                        await _emailService.SendAccountFrozenEmailAsync(
                            request.CustomerUser.Email.Value,
                            request.CustomerUser.Username,
                            request.PlannedDeletionAt,
                            cancellationToken);
                    }

                    request.MarkAccountFrozenNotificationSent();
                    processedCount++;

                    _logger.LogInformation("Account frozen for deletion request {RequestId}", request.Id);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Failed to process cooling off completed request {RequestId}", request.Id);
                }
            }

            await _unitOfWork.SaveChangesAsync(cancellationToken);

            return Result<int>.Success(processedCount);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to process cooling off completed requests");
            return Result<int>.Failure($"处理失败：{ex.Message}");
        }
    }

    public async Task<Result<int>> ProcessPendingAnonymizationRequestsAsync(
        CancellationToken cancellationToken = default)
    {
        try
        {
            var requests = await _deletionRequestRepository.GetPendingAnonymizationRequestsAsync(cancellationToken);
            var processedCount = 0;

            foreach (var request in requests)
            {
                try
                {
                    // 匿名化数据
                    await _complianceService.AnonymizeUserDataAsync(
                        request.CustomerUserId, 
                        cancellationToken);

                    // 更新请求状态
                    request.CompleteAnonymization();
                    request.Complete();

                    // 标记用户为已删除
                    request.CustomerUser.MarkAsDeleted(request.Id);

                    _deletionRequestRepository.Update(request);
                    _customerUserRepository.Update(request.CustomerUser);

                    // 发送账号删除完成通知
                    if (request.CustomerUser.Email != null)
                    {
                        await _emailService.SendAccountDeletedEmailAsync(
                            request.CustomerUser.Email.Value,
                            request.CustomerUser.Username,
                            cancellationToken);
                    }

                    processedCount++;

                    _logger.LogWarning("Account data anonymized for user {UserId}", request.CustomerUserId);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Failed to process anonymization for request {RequestId}", request.Id);
                }
            }

            await _unitOfWork.SaveChangesAsync(cancellationToken);

            return Result<int>.Success(processedCount);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to process pending anonymization requests");
            return Result<int>.Failure($"处理失败：{ex.Message}");
        }
    }

    public async Task<Result<int>> SendCoolingOffRemindersAsync(
        CancellationToken cancellationToken = default)
    {
        try
        {
            var requests = await _deletionRequestRepository.GetRequestsNeedingCoolingOffReminderAsync(cancellationToken);
            var sentCount = 0;

            foreach (var request in requests)
            {
                try
                {
                    // 发送提醒通知（邮件发送在下面已经实现）

                    if (request.CustomerUser.Email != null)
                    {
                        await _emailService.SendAccountDeletionReminderEmailAsync(
                            request.CustomerUser.Email.Value,
                            request.CustomerUser.Username,
                            request.CoolingOffEndAt,
                            cancellationToken);
                    }

                    request.MarkCoolingOffReminderSent();
                    _deletionRequestRepository.Update(request);
                    
                    sentCount++;

                    _logger.LogInformation("Cooling off reminder sent for request {RequestId}", request.Id);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Failed to send cooling off reminder for request {RequestId}", request.Id);
                }
            }

            await _unitOfWork.SaveChangesAsync(cancellationToken);

            return Result<int>.Success(sentCount);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to send cooling off reminders");
            return Result<int>.Failure($"发送提醒失败：{ex.Message}");
        }
    }

    private AccountDeletionRequestDto MapToDto(AccountDeletionRequest request, WhimLabAI.Domain.Entities.User.CustomerUser user)
    {
        return new AccountDeletionRequestDto
        {
            Id = request.Id,
            CustomerUserId = request.CustomerUserId,
            Username = user.Username,
            Email = user.Email?.Value,
            Reason = request.Reason,
            ReasonCategory = request.ReasonCategory,
            Status = request.Status,
            RequestedAt = request.RequestedAt,
            CoolingOffEndAt = request.CoolingOffEndAt,
            PlannedDeletionAt = request.PlannedDeletionAt,
            ActualDeletionAt = request.ActualDeletionAt,
            CancelledAt = request.CancelledAt,
            CancellationReason = request.CancellationReason,
            IsVerified = request.IsVerified,
            CanBeCancelled = request.CanBeCancelled(),
            RemainingCoolingOffDays = request.GetRemainingCoolingOffDays(),
            CreatedAt = request.CreatedAt,
            UpdatedAt = request.UpdatedAt
        };
    }
    
    private static string GenerateVerificationCode()
    {
        var random = new Random();
        return random.Next(100000, 999999).ToString();
    }
}