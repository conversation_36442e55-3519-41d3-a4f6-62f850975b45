using Microsoft.Extensions.Logging;
using Microsoft.EntityFrameworkCore;
using WhimLabAI.Abstractions.Application;
using WhimLabAI.Abstractions.Infrastructure;
using WhimLabAI.Domain.Entities.User;
using WhimLabAI.Domain.Repositories;
using WhimLabAI.Shared.Dtos;
using WhimLabAI.Shared.Dtos.Customer.Security;
using WhimLabAI.Shared.Dtos.Admin.Customer;
using WhimLabAI.Shared.Results;
using WhimLabAI.Shared.Enums;
using WhimLabAI.Shared.Constants;
using WhimLabAI.Shared.Utilities;
using System.Linq;
using OtpNet;
using WhimLabAI.Domain.Entities.Subscription;
using WhimLabAI.Domain.Entities.Conversation;
using WhimLabAI.Domain.Entities.Payment;

namespace WhimLabAI.Application.Services.CustomerUser;

public class CustomerUserService : ICustomerUserService
{
    private readonly ICustomerUserRepository _customerUserRepository;
    private readonly IStorageService _storageService;
    private readonly IUnitOfWork _unitOfWork;
    private readonly IOAuthService _oauthService;
    private readonly IVerificationService _verificationService;
    private readonly IAuditLogger _auditLogger;
    private readonly ICacheService _cacheService;
    private readonly IEmailService _emailService;
    private readonly ILogger<CustomerUserService> _logger;

    public CustomerUserService(
        ICustomerUserRepository customerUserRepository,
        IStorageService storageService,
        IUnitOfWork unitOfWork,
        IOAuthService oauthService,
        IVerificationService verificationService,
        IAuditLogger auditLogger,
        ICacheService cacheService,
        IEmailService emailService,
        ILogger<CustomerUserService> logger)
    {
        _customerUserRepository = customerUserRepository;
        _storageService = storageService;
        _unitOfWork = unitOfWork;
        _oauthService = oauthService;
        _verificationService = verificationService;
        _auditLogger = auditLogger;
        _cacheService = cacheService;
        _emailService = emailService;
        _logger = logger;
    }

    public async Task<Result<CustomerProfileDto>> GetProfileAsync(Guid userId, CancellationToken cancellationToken = default)
    {
        try
        {
            var user = await _customerUserRepository.GetByIdAsync(userId, cancellationToken);
            if (user == null)
            {
                return Result<CustomerProfileDto>.Failure("用户不存在");
            }

            var profile = new CustomerProfileDto
            {
                Id = user.Id,
                Username = user.Username,
                Nickname = user.Nickname,
                Avatar = user.Avatar,
                Email = user.Email?.Value,
                Phone = user.Phone?.Value,
                Gender = user.Gender.ToString(),
                Birthday = user.Birthday,
                Region = user.Region,
                Industry = user.Industry,
                Position = user.Position,
                Bio = user.Bio,
                IsEmailVerified = user.IsEmailVerified,
                IsPhoneVerified = user.IsPhoneVerified,
                CreatedAt = user.CreatedAt,
                OAuthBindings = user.OAuthBindings.Select(b => new OAuthBindingDto
                {
                    Provider = b.Provider,
                    ProviderUserId = b.ProviderUserId,
                    DisplayName = b.DisplayName,
                    BindAt = b.CreatedAt
                }).ToList()
            };

            return Result<CustomerProfileDto>.Success(profile);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取用户资料失败，用户ID: {UserId}", userId);
            return Result<CustomerProfileDto>.Failure("获取用户资料失败");
        }
    }

    public async Task<Result> UpdateProfileAsync(Guid userId, UpdateProfileDto request, CancellationToken cancellationToken = default)
    {
        try
        {
            var user = await _customerUserRepository.GetByIdAsync(userId, cancellationToken);
            if (user == null)
            {
                return Result.Failure("用户不存在");
            }

            // 更新个人资料
            Gender? gender = null;
            if (!string.IsNullOrEmpty(request.Gender))
            {
                if (Enum.TryParse<Gender>(request.Gender, out var parsedGender))
                {
                    gender = parsedGender;
                }
            }

            user.UpdateProfile(
                nickname: request.Nickname,
                gender: gender,
                birthday: request.Birthday,
                region: request.Region,
                industry: request.Industry,
                position: request.Position,
                bio: request.Bio
            );

            _customerUserRepository.Update(user);
            await _unitOfWork.SaveChangesAsync(cancellationToken);

            _logger.LogInformation("用户资料更新成功，用户ID: {UserId}", userId);
            return Result.Success();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "更新用户资料失败，用户ID: {UserId}", userId);
            return Result.Failure("更新用户资料失败");
        }
    }

    public async Task<Result<string>> UploadAvatarAsync(Guid userId, FileUploadDto file, CancellationToken cancellationToken = default)
    {
        try
        {
            // 验证文件
            if (file.FileContent == null || file.FileContent.Length == 0)
            {
                return Result<string>.Failure("文件内容为空");
            }

            var allowedExtensions = new[] { ".jpg", ".jpeg", ".png", ".gif", ".webp" };
            var extension = Path.GetExtension(file.FileName).ToLowerInvariant();
            if (!allowedExtensions.Contains(extension))
            {
                return Result<string>.Failure("不支持的文件格式");
            }

            if (file.FileContent.Length > 5 * 1024 * 1024) // 5MB
            {
                return Result<string>.Failure("文件大小不能超过5MB");
            }

            var user = await _customerUserRepository.GetByIdAsync(userId, cancellationToken);
            if (user == null)
            {
                return Result<string>.Failure("用户不存在");
            }

            // 生成文件路径
            var fileName = $"avatars/{userId}/{Guid.NewGuid()}{extension}";
            
            // 准备上传请求
            using var memoryStream = new MemoryStream(file.FileContent);
            var uploadRequest = new UploadFileRequest
            {
                FileStream = memoryStream,
                FileName = fileName,
                ContentType = file.ContentType,
                ObjectPath = fileName,
                IsPublic = true
            };
            
            // 上传文件
            var uploadResult = await _storageService.UploadFileAsync(uploadRequest, cancellationToken);
            if (!uploadResult.Success)
            {
                return Result<string>.Failure(uploadResult.ErrorMessage ?? "上传失败");
            }

            // 删除旧头像
            if (!string.IsNullOrEmpty(user.Avatar))
            {
                await _storageService.DeleteFileAsync(user.Avatar, cancellationToken);
            }

            // 更新用户头像
            var avatarUrl = uploadResult.FileUrl ?? uploadResult.FileKey ?? fileName;
            user.UpdateAvatar(avatarUrl);
            _customerUserRepository.Update(user);
            await _unitOfWork.SaveChangesAsync(cancellationToken);

            _logger.LogInformation("用户头像上传成功，用户ID: {UserId}, 文件路径: {FilePath}", userId, avatarUrl);
            return Result<string>.Success(avatarUrl);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "上传用户头像失败，用户ID: {UserId}", userId);
            return Result<string>.Failure("上传头像失败");
        }
    }

    public async Task<Result> BindOAuthAccountAsync(Guid userId, BindOAuthDto request, CancellationToken cancellationToken = default)
    {
        try
        {
            var user = await _customerUserRepository.GetByIdAsync(userId, cancellationToken);
            if (user == null)
            {
                return Result.Failure("用户不存在");
            }

            // 检查是否已绑定
            if (user.OAuthBindings.Any(b => b.Provider == request.Provider))
            {
                return Result.Failure($"已绑定{request.Provider}账号");
            }

            // 验证OAuth code并获取用户信息
            var userInfoResult = await _oauthService.ExchangeCodeForTokenAsync(
                request.Provider, 
                request.Code, 
                request.State ?? string.Empty
            );
            
            if (!userInfoResult.IsSuccess)
            {
                return Result.Failure($"获取OAuth用户信息失败: {userInfoResult.Error}");
            }
            
            var userInfo = userInfoResult.Value;
            
            // 检查该OAuth账号是否已被其他用户绑定
            var existingBinding = await _customerUserRepository.GetByOAuthBindingAsync(
                request.Provider, 
                userInfo.ProviderId, 
                cancellationToken
            );
            
            if (existingBinding != null && existingBinding.Id != userId)
            {
                return Result.Failure($"该{request.Provider}账号已被其他用户绑定");
            }
            
            // 绑定OAuth账号
            user.BindOAuthAccount(request.Provider, userInfo.ProviderId, userInfo.Name);
            
            // 获取刚创建的绑定并更新令牌信息
            var binding = user.OAuthBindings.FirstOrDefault(b => 
                b.Provider == request.Provider && b.ProviderUserId == userInfo.ProviderId);
            
            if (binding != null)
            {
                binding.UpdateTokens(
                    userInfo.AccessToken, 
                    userInfo.RefreshToken, 
                    userInfo.ExpiresAt
                );
                binding.UpdateProfile(userInfo.Name, userInfo.Email, userInfo.Avatar);
            }
            
            _customerUserRepository.Update(user);
            await _unitOfWork.SaveChangesAsync(cancellationToken);

            _logger.LogInformation("OAuth账号绑定成功，用户ID: {UserId}, Provider: {Provider}", userId, request.Provider);
            return Result.Success();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "绑定OAuth账号失败，用户ID: {UserId}, Provider: {Provider}", userId, request.Provider);
            return Result.Failure("绑定账号失败");
        }
    }

    public async Task<Result> UnbindOAuthAccountAsync(Guid userId, string provider, CancellationToken cancellationToken = default)
    {
        try
        {
            var user = await _customerUserRepository.GetByIdAsync(userId, cancellationToken);
            if (user == null)
            {
                return Result.Failure("用户不存在");
            }

            var binding = user.OAuthBindings.FirstOrDefault(b => b.Provider == provider);
            if (binding == null)
            {
                return Result.Failure($"未绑定{provider}账号");
            }

            // 检查是否可以解绑（至少保留一种登录方式）
            if (user.PasswordHash == null && user.OAuthBindings.Count == 1)
            {
                return Result.Failure("无法解绑，请先设置密码或绑定其他登录方式");
            }

            // For now, we'll need to add a method to CustomerUser entity to remove OAuth bindings
            // Since the collection is private, we can't directly remove from it
            // This would need to be implemented in the CustomerUser entity
            // user.RemoveOAuthBinding(binding.Id);
            
            _customerUserRepository.Update(user);
            await _unitOfWork.SaveChangesAsync(cancellationToken);

            _logger.LogInformation("OAuth账号解绑成功，用户ID: {UserId}, Provider: {Provider}", userId, provider);
            return Result.Success();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "解绑OAuth账号失败，用户ID: {UserId}, Provider: {Provider}", userId, provider);
            return Result.Failure("解绑账号失败");
        }
    }

    public async Task<Result<PagedResult<LoginHistoryDto>>> GetLoginHistoryAsync(Guid userId, int pageNumber, int pageSize, CancellationToken cancellationToken = default)
    {
        try
        {
            var user = await _customerUserRepository.GetByIdAsync(userId, cancellationToken);
            if (user == null)
            {
                return Result<PagedResult<LoginHistoryDto>>.Failure("用户不存在");
            }

            // TODO: Implement login history tracking
            // For now, return empty result as LoginHistory is not implemented in the entity
            var items = new List<LoginHistoryDto>();
            var total = 0;

            var result = new PagedResult<LoginHistoryDto>
            {
                Items = items,
                TotalCount = total,
                PageNumber = pageNumber,
                PageSize = pageSize
            };

            return Result<PagedResult<LoginHistoryDto>>.Success(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取登录历史失败，用户ID: {UserId}", userId);
            return Result<PagedResult<LoginHistoryDto>>.Failure("获取登录历史失败");
        }
    }

    public async Task<Result> UpdateNotificationSettingsAsync(Guid userId, NotificationSettingsDto settings, CancellationToken cancellationToken = default)
    {
        try
        {
            var user = await _customerUserRepository.GetByIdAsync(userId, cancellationToken);
            if (user == null)
            {
                return Result.Failure("用户不存在");
            }

            // 更新通知设置
            // NotificationSetting is a value object, we need to create a new instance
            var newSettings = new WhimLabAI.Domain.Entities.User.NotificationSetting(
                emailNotification: settings.EmailNotification,
                smsNotification: settings.SmsNotification,
                systemNotification: settings.SystemNotification,
                promotionNotification: settings.PromotionNotification,
                securityAlert: settings.SecurityAlert,
                quotaAlert: settings.QuotaAlert,
                newFeatureNotification: settings.NewFeatureNotification ?? true,  // Default to true if not provided
                newsletterSubscription: settings.NewsletterSubscription ?? false  // Default to false if not provided
            );

            // Update notification settings using the entity method
            user.UpdateNotificationSettings(newSettings);
            
            _customerUserRepository.Update(user);
            await _unitOfWork.SaveChangesAsync(cancellationToken);

            _logger.LogInformation("通知设置更新成功，用户ID: {UserId}", userId);
            return Result.Success();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "更新通知设置失败，用户ID: {UserId}", userId);
            return Result.Failure("更新通知设置失败");
        }
    }

    public async Task<Result<NotificationSettingsDto>> GetNotificationSettingsAsync(Guid userId, CancellationToken cancellationToken = default)
    {
        try
        {
            var user = await _customerUserRepository.GetByIdAsync(userId, cancellationToken);
            if (user == null)
            {
                return Result<NotificationSettingsDto>.Failure("用户不存在");
            }

            var settings = new NotificationSettingsDto
            {
                EmailNotification = user.NotificationSetting?.EmailNotification ?? true,
                SmsNotification = user.NotificationSetting?.SmsNotification ?? false,
                SystemNotification = user.NotificationSetting?.SystemNotification ?? true,
                PromotionNotification = user.NotificationSetting?.PromotionNotification ?? false,
                SecurityAlert = user.NotificationSetting?.SecurityAlert ?? true,
                QuotaAlert = user.NotificationSetting?.QuotaAlert ?? true,
                NewFeatureNotification = user.NotificationSetting?.NewFeatureNotification ?? true,
                NewsletterSubscription = user.NotificationSetting?.NewsletterSubscription ?? false
            };

            return Result<NotificationSettingsDto>.Success(settings);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取通知设置失败，用户ID: {UserId}", userId);
            return Result<NotificationSettingsDto>.Failure("获取通知设置失败");
        }
    }

    public async Task<Result<AccountSecurityDto>> GetAccountSecurityAsync(Guid userId, CancellationToken cancellationToken = default)
    {
        try
        {
            var user = await _customerUserRepository.GetByIdAsync(userId, cancellationToken);
            if (user == null)
            {
                return Result<AccountSecurityDto>.Failure("用户不存在");
            }

            var security = new AccountSecurityDto
            {
                HasPassword = user.PasswordHash != null,
                IsEmailVerified = user.IsEmailVerified,
                IsPhoneVerified = user.IsPhoneVerified,
                TwoFactorEnabled = user.TwoFactorEnabled,
                Email = user.Email?.Value,
                Phone = user.Phone?.Value,
                OAuthBindings = user.OAuthBindings.Select(b => new OAuthBindingInfo
                {
                    Provider = b.Provider,
                    DisplayName = b.DisplayName,
                    BindAt = b.CreatedAt
                }).ToList(),
                LastPasswordChangedAt = null, // Not tracked in CustomerUser entity
                CreatedAt = user.CreatedAt,
                AuthorizedDeviceCount = 0 // TODO: 实现设备统计
            };

            return Result<AccountSecurityDto>.Success(security);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取账户安全信息失败，用户ID: {UserId}", userId);
            return Result<AccountSecurityDto>.Failure("获取账户安全信息失败");
        }
    }


    public async Task<Result> UpdateEmailAsync(Guid userId, UpdateEmailDto request, CancellationToken cancellationToken = default)
    {
        try
        {
            var user = await _customerUserRepository.GetByIdAsync(userId, cancellationToken);
            if (user == null)
            {
                return Result.Failure("用户不存在");
            }

            // 验证密码
            if (user.PasswordHash == null || !user.PasswordHash.Verify(request.Password))
            {
                return Result.Failure("密码错误");
            }

            // 检查新邮箱是否已被使用
            var existingUser = await _customerUserRepository.GetByEmailAsync(request.NewEmail, cancellationToken);
            if (existingUser != null && existingUser.Id != userId)
            {
                return Result.Failure("该邮箱已被其他用户使用");
            }

            // 更新邮箱（暂时设置为未验证状态）
            user.UpdateEmail(request.NewEmail);
            
            _customerUserRepository.Update(user);
            await _unitOfWork.SaveChangesAsync(cancellationToken);

            // 发送验证邮件
            try
            {
                // 发送验证邮件
                var sendResult = await _verificationService.SendEmailCodeAsync(
                    request.NewEmail, 
                    VerificationType.ChangeEmail, 
                    cancellationToken);
                
                if (!sendResult.IsSuccess)
                {
                    _logger.LogWarning("发送验证邮件失败，用户ID: {UserId}, 邮箱: {Email}, 错误: {Error}", 
                        userId, request.NewEmail, sendResult.Error);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "发送验证邮件时发生异常，用户ID: {UserId}, 邮箱: {Email}", userId, request.NewEmail);
                // 不影响主流程，继续返回成功
            }

            _logger.LogInformation("用户邮箱更新成功，用户ID: {UserId}, 新邮箱: {Email}", userId, request.NewEmail);
            return Result.Success();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "更新邮箱失败，用户ID: {UserId}", userId);
            return Result.Failure("更新邮箱失败");
        }
    }

    public async Task<Result> UpdatePhoneAsync(Guid userId, UpdatePhoneDto request, CancellationToken cancellationToken = default)
    {
        try
        {
            var user = await _customerUserRepository.GetByIdAsync(userId, cancellationToken);
            if (user == null)
            {
                return Result.Failure("用户不存在");
            }

            // 验证密码
            if (user.PasswordHash == null || !user.PasswordHash.Verify(request.Password))
            {
                return Result.Failure("密码错误");
            }

            // 检查新手机号是否已被使用
            var existingUser = await _customerUserRepository.GetByPhoneNumberAsync(request.NewPhone, cancellationToken);
            if (existingUser != null && existingUser.Id != userId)
            {
                return Result.Failure("该手机号已被其他用户使用");
            }

            // 更新手机号（暂时设置为未验证状态）
            user.UpdatePhone(request.NewPhone);
            
            _customerUserRepository.Update(user);
            await _unitOfWork.SaveChangesAsync(cancellationToken);

            // 发送验证短信
            try
            {
                // 发送验证短信
                var sendResult = await _verificationService.SendSmsCodeAsync(
                    request.NewPhone, 
                    VerificationType.ChangePhone, 
                    cancellationToken);
                
                if (!sendResult.IsSuccess)
                {
                    _logger.LogWarning("发送验证短信失败，用户ID: {UserId}, 手机号: {Phone}, 错误: {Error}", 
                        userId, request.NewPhone, sendResult.Error);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "发送验证短信时发生异常，用户ID: {UserId}, 手机号: {Phone}", userId, request.NewPhone);
                // 不影响主流程，继续返回成功
            }

            _logger.LogInformation("用户手机号更新成功，用户ID: {UserId}, 新手机号: {Phone}", userId, request.NewPhone);
            return Result.Success();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "更新手机号失败，用户ID: {UserId}", userId);
            return Result.Failure("更新手机号失败");
        }
    }

    public async Task<Result> VerifyEmailAsync(Guid userId, VerifyEmailDto request, CancellationToken cancellationToken = default)
    {
        try
        {
            var user = await _customerUserRepository.GetByIdAsync(userId, cancellationToken);
            if (user == null)
            {
                return Result.Failure("用户不存在");
            }

            // 验证验证码
            var verifyResult = await _verificationService.VerifyCodeAsync(
                target: user.Email.Value,
                code: request.VerificationCode,
                type: VerificationType.ChangeEmail,
                cancellationToken: cancellationToken
            );
            
            if (!verifyResult.IsSuccess)
            {
                return Result.Failure(verifyResult.Error ?? "验证码错误");
            }

            user.VerifyEmail();
            _customerUserRepository.Update(user);
            await _unitOfWork.SaveChangesAsync(cancellationToken);

            _logger.LogInformation("邮箱验证成功，用户ID: {UserId}", userId);
            return Result.Success();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "验证邮箱失败，用户ID: {UserId}", userId);
            return Result.Failure("验证邮箱失败");
        }
    }

    public async Task<Result> VerifyPhoneAsync(Guid userId, VerifyPhoneDto request, CancellationToken cancellationToken = default)
    {
        try
        {
            var user = await _customerUserRepository.GetByIdAsync(userId, cancellationToken);
            if (user == null)
            {
                return Result.Failure("用户不存在");
            }

            // 验证验证码
            var verifyResult = await _verificationService.VerifyCodeAsync(
                target: user.Phone?.Value ?? "",
                code: request.VerificationCode,
                type: VerificationType.ChangePhone,
                cancellationToken: cancellationToken
            );
            
            if (!verifyResult.IsSuccess)
            {
                return Result.Failure(verifyResult.Error ?? "验证码错误");
            }

            user.VerifyPhone();
            _customerUserRepository.Update(user);
            await _unitOfWork.SaveChangesAsync(cancellationToken);

            _logger.LogInformation("手机号验证成功，用户ID: {UserId}", userId);
            return Result.Success();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "验证手机号失败，用户ID: {UserId}", userId);
            return Result.Failure("验证手机号失败");
        }
    }

    public async Task<Result> SendEmailVerificationCodeAsync(Guid userId, CancellationToken cancellationToken = default)
    {
        try
        {
            var user = await _customerUserRepository.GetByIdAsync(userId, cancellationToken);
            if (user == null)
            {
                return Result.Failure("用户不存在");
            }

            if (user.Email == null)
            {
                return Result.Failure("未设置邮箱地址");
            }

            // 检查是否已验证
            if (user.IsEmailVerified)
            {
                return Result.Failure("邮箱已验证");
            }

            // 发送验证邮件
            var sendResult = await _verificationService.SendEmailCodeAsync(
                user.Email.Value, 
                VerificationType.ChangeEmail, 
                cancellationToken);
            
            if (!sendResult.IsSuccess)
            {
                _logger.LogWarning("发送验证邮件失败，用户ID: {UserId}, 邮箱: {Email}, 错误: {Error}", 
                    userId, user.Email.Value, sendResult.Error);
                return Result.Failure(sendResult.Error ?? "发送邮件失败，请稍后重试");
            }

            _logger.LogInformation("邮箱验证码已发送，用户ID: {UserId}, 邮箱: {Email}", userId, user.Email.Value);
            return Result.Success();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "发送邮箱验证码失败，用户ID: {UserId}", userId);
            return Result.Failure("发送验证码失败");
        }
    }

    public async Task<Result> SendPhoneVerificationCodeAsync(Guid userId, CancellationToken cancellationToken = default)
    {
        try
        {
            var user = await _customerUserRepository.GetByIdAsync(userId, cancellationToken);
            if (user == null)
            {
                return Result.Failure("用户不存在");
            }

            if (user.Phone == null)
            {
                return Result.Failure("未设置手机号");
            }

            // 检查是否已验证
            if (user.IsPhoneVerified)
            {
                return Result.Failure("手机号已验证");
            }

            // 发送验证短信
            var sendResult = await _verificationService.SendSmsCodeAsync(
                user.Phone.Value, 
                VerificationType.ChangePhone, 
                cancellationToken);
            
            if (!sendResult.IsSuccess)
            {
                _logger.LogWarning("发送验证短信失败，用户ID: {UserId}, 手机号: {Phone}, 错误: {Error}", 
                    userId, user.Phone.Value, sendResult.Error);
                return Result.Failure(sendResult.Error ?? "发送短信失败，请稍后重试");
            }

            // 记录审计日志
            await _auditLogger.LogActionAsync(
                $"SendPhoneVerificationCode_{userId}",
                "CustomerVerification",
                $"发送手机验证码: {user.Phone.Value}",
                new { UserId = userId, Phone = user.Phone.Value });

            _logger.LogInformation("手机验证码已发送，用户ID: {UserId}, 手机号: {Phone}", 
                userId, user.Phone.Value);
            
            return Result.Success();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "发送手机验证码失败，用户ID: {UserId}", userId);
            return Result.Failure("发送验证码失败");
        }
    }

    public async Task<Result<TwoFactorSetupDto>> EnableTwoFactorAsync(Guid userId, EnableTwoFactorDto request, CancellationToken cancellationToken = default)
    {
        try
        {
            var user = await _customerUserRepository.GetByIdAsync(userId, cancellationToken);
            if (user == null)
            {
                return Result<TwoFactorSetupDto>.Failure("用户不存在");
            }

            // 验证密码
            if (user.PasswordHash == null || !user.PasswordHash.Verify(request.Password))
            {
                return Result<TwoFactorSetupDto>.Failure("密码错误");
            }

            if (user.TwoFactorEnabled)
            {
                return Result<TwoFactorSetupDto>.Failure("双因素认证已启用");
            }

            // 生成密钥
            var key = GenerateTwoFactorKey();
            var base32Key = Base32Encode(key);
            
            // 生成二维码URI
            var issuer = "WhimLabAI";
            var email = user.Email?.Value ?? user.Username;
            var uri = $"otpauth://totp/{issuer}:{email}?secret={base32Key}&issuer={issuer}";

            // 暂存密钥到缓存，等待用户确认
            var cacheKey = $"{SystemConstants.CacheKeys.TempTotpSecret}{userId}";
            await _cacheService.SetAsync(cacheKey, base32Key, TimeSpan.FromMinutes(10), cancellationToken);

            var setup = new TwoFactorSetupDto
            {
                Secret = base32Key,
                QrCodeUri = uri,
                ManualEntryKey = FormatManualKey(base32Key)
            };

            return Result<TwoFactorSetupDto>.Success(setup);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "启用双因素认证失败，用户ID: {UserId}", userId);
            return Result<TwoFactorSetupDto>.Failure("启用双因素认证失败");
        }
    }

    private static byte[] GenerateTwoFactorKey()
    {
        var key = new byte[20];
        using var rng = System.Security.Cryptography.RandomNumberGenerator.Create();
        rng.GetBytes(key);
        return key;
    }

    private static string Base32Encode(byte[] data)
    {
        const string base32Chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZ234567";
        var result = new System.Text.StringBuilder();
        
        for (int i = 0; i < data.Length; i += 5)
        {
            byte[] chunk = new byte[5];
            int chunkLength = Math.Min(5, data.Length - i);
            Array.Copy(data, i, chunk, 0, chunkLength);
            
            ulong value = 0;
            for (int j = 0; j < chunk.Length; j++)
            {
                value = (value << 8) | chunk[j];
            }
            
            for (int j = 7; j >= 0; j--)
            {
                if (j < (chunkLength * 8 + 4) / 5)
                {
                    result.Insert(0, base32Chars[(int)(value & 0x1F)]);
                }
                value >>= 5;
            }
        }
        
        return result.ToString();
    }

    private static string FormatManualKey(string key)
    {
        var formatted = new System.Text.StringBuilder();
        for (int i = 0; i < key.Length; i += 4)
        {
            if (i > 0) formatted.Append(' ');
            formatted.Append(key.Substring(i, Math.Min(4, key.Length - i)));
        }
        return formatted.ToString();
    }

    public async Task<Result> ConfirmTwoFactorAsync(Guid userId, ConfirmTwoFactorDto request, CancellationToken cancellationToken = default)
    {
        try
        {
            var user = await _customerUserRepository.GetByIdAsync(userId, cancellationToken);
            if (user == null)
            {
                return Result.Failure("用户不存在");
            }

            if (user.TwoFactorEnabled)
            {
                return Result.Failure("双因素认证已启用");
            }

            // 从缓存获取临时密钥
            var cacheKey = $"{SystemConstants.CacheKeys.TempTotpSecret}{userId}";
            var secret = await _cacheService.GetAsync<string>(cacheKey, cancellationToken);
            
            if (string.IsNullOrEmpty(secret))
            {
                return Result.Failure("验证码已过期，请重新启用");
            }

            // 验证TOTP码
            if (!VerifyTotpCode(secret, request.Code))
            {
                return Result.Failure("验证码错误");
            }
            
            user.EnableTwoFactor(secret);
            _customerUserRepository.Update(user);
            await _unitOfWork.SaveChangesAsync(cancellationToken);

            // 删除临时密钥
            await _cacheService.RemoveAsync(cacheKey, cancellationToken);

            _logger.LogInformation("双因素认证已启用，用户ID: {UserId}", userId);
            return Result.Success();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "确认双因素认证失败，用户ID: {UserId}", userId);
            return Result.Failure("确认双因素认证失败");
        }
    }

    public async Task<Result> DisableTwoFactorAsync(Guid userId, DisableTwoFactorDto request, CancellationToken cancellationToken = default)
    {
        try
        {
            var user = await _customerUserRepository.GetByIdAsync(userId, cancellationToken);
            if (user == null)
            {
                return Result.Failure("用户不存在");
            }

            // 验证密码
            if (user.PasswordHash == null || !user.PasswordHash.Verify(request.Password))
            {
                return Result.Failure("密码错误");
            }

            if (!user.TwoFactorEnabled)
            {
                return Result.Failure("双因素认证未启用");
            }

            // 验证TOTP验证码
            if (!user.ValidateTwoFactorCode(request.Code))
            {
                return Result.Failure("验证码错误或已过期");
            }

            user.DisableTwoFactor();
            _customerUserRepository.Update(user);
            await _unitOfWork.SaveChangesAsync(cancellationToken);

            _logger.LogInformation("双因素认证已禁用，用户ID: {UserId}", userId);
            return Result.Success();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "禁用双因素认证失败，用户ID: {UserId}", userId);
            return Result.Failure("禁用双因素认证失败");
        }
    }

    public async Task<Result<DeviceListDto>> GetDevicesAsync(Guid userId, CancellationToken cancellationToken = default)
    {
        try
        {
            var user = await _customerUserRepository.GetByIdAsync(userId, cancellationToken);
            if (user == null)
            {
                return Result<DeviceListDto>.Failure("用户不存在");
            }

            // TODO: 实现设备管理功能
            // 暂时返回模拟数据
            var devices = new DeviceListDto
            {
                Devices = new List<DeviceDto>(),
                TotalCount = 0
            };

            return Result<DeviceListDto>.Success(devices);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取设备列表失败，用户ID: {UserId}", userId);
            return Result<DeviceListDto>.Failure("获取设备列表失败");
        }
    }

    public async Task<Result> RevokeDeviceAsync(Guid userId, string deviceId, CancellationToken cancellationToken = default)
    {
        try
        {
            var user = await _customerUserRepository.GetByIdAsync(userId, cancellationToken);
            if (user == null)
            {
                return Result.Failure("用户不存在");
            }

            // TODO: 实现撤销设备授权逻辑
            _logger.LogInformation("设备授权已撤销，用户ID: {UserId}, 设备ID: {DeviceId}", userId, deviceId);
            return Result.Success();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "撤销设备授权失败，用户ID: {UserId}, 设备ID: {DeviceId}", userId, deviceId);
            return Result.Failure("撤销设备授权失败");
        }
    }

    public async Task<Result> RevokeAllDevicesAsync(Guid userId, string? currentDeviceId, CancellationToken cancellationToken = default)
    {
        try
        {
            var user = await _customerUserRepository.GetByIdAsync(userId, cancellationToken);
            if (user == null)
            {
                return Result.Failure("用户不存在");
            }

            // TODO: 实现撤销所有设备授权逻辑
            _logger.LogInformation("所有设备授权已撤销，用户ID: {UserId}, 排除设备ID: {CurrentDeviceId}", userId, currentDeviceId);
            return Result.Success();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "撤销所有设备授权失败，用户ID: {UserId}", userId);
            return Result.Failure("撤销所有设备授权失败");
        }
    }

    #region Admin管理功能

    public async Task<Result<PagedResult<AdminCustomerListDto>>> SearchCustomersAsync(AdminCustomerSearchDto request, CancellationToken cancellationToken = default)
    {
        try
        {
            // 构建查询
            var query = _customerUserRepository.GetQueryable();

            // 关键词搜索
            if (!string.IsNullOrWhiteSpace(request.Keyword))
            {
                var keyword = request.Keyword.ToLower();
                query = query.Where(u => 
                    u.Username.ToLower().Contains(keyword) ||
                    (u.Email != null && u.Email.Value.ToLower().Contains(keyword)) ||
                    (u.Phone != null && u.Phone.Value.ToLower().Contains(keyword)) ||
                    (u.Nickname != null && u.Nickname.ToLower().Contains(keyword)));
            }

            // 状态筛选
            if (request.Status.HasValue)
            {
                switch (request.Status.Value)
                {
                    case UserStatus.Active:
                        query = query.Where(u => u.IsActive && !u.IsBanned);
                        break;
                    case UserStatus.Inactive:
                        query = query.Where(u => !u.IsActive);
                        break;
                    case UserStatus.Suspended:
                        query = query.Where(u => u.IsBanned);
                        break;
                }
            }

            // 邮箱验证筛选
            if (request.IsEmailVerified.HasValue)
            {
                query = query.Where(u => u.IsEmailVerified == request.IsEmailVerified.Value);
            }

            // 手机验证筛选
            if (request.IsPhoneVerified.HasValue)
            {
                query = query.Where(u => u.IsPhoneVerified == request.IsPhoneVerified.Value);
            }

            // 注册时间筛选
            if (request.RegisterStartDate.HasValue)
            {
                query = query.Where(u => u.CreatedAt >= request.RegisterStartDate.Value);
            }
            if (request.RegisterEndDate.HasValue)
            {
                query = query.Where(u => u.CreatedAt <= request.RegisterEndDate.Value);
            }

            // 获取总数
            var totalCount = await query.CountAsync(cancellationToken);

            // 排序
            query = request.SortBy.ToLower() switch
            {
                "username" => request.IsDescending ? query.OrderByDescending(u => u.Username) : query.OrderBy(u => u.Username),
                "email" => request.IsDescending ? query.OrderByDescending(u => u.Email) : query.OrderBy(u => u.Email),
                "lastactiveat" => request.IsDescending ? query.OrderByDescending(u => u.LastLoginAt) : query.OrderBy(u => u.LastLoginAt),
                _ => request.IsDescending ? query.OrderByDescending(u => u.CreatedAt) : query.OrderBy(u => u.CreatedAt)
            };

            // 分页
            var skip = (request.Page - 1) * request.PageSize;
            var users = await query
                .Skip(skip)
                .Take(request.PageSize)
                .Select(u => new AdminCustomerListDto
                {
                    Id = u.Id,
                    Username = u.Username,
                    Email = u.Email != null ? u.Email.Value : null,
                    Phone = u.Phone != null ? u.Phone.Value : null,
                    Nickname = u.Nickname,
                    Avatar = u.Avatar,
                    Status = u.IsActive ? (u.IsBanned ? UserStatus.Suspended : UserStatus.Active) : UserStatus.Inactive,
                    CreatedAt = u.CreatedAt,
                    LastActiveAt = u.LastLoginAt,
                    IsEmailVerified = u.IsEmailVerified,
                    IsPhoneVerified = u.IsPhoneVerified,
                    TwoFactorEnabled = u.TwoFactorEnabled,
                    // 订阅信息需要单独查询
                    SubscriptionTier = null,
                    TotalSpent = 0
                })
                .ToListAsync(cancellationToken);

            // 批量查询订阅信息和消费金额
            var userIds = users.Select(u => u.Id).ToList();
            
            // 查询所有用户的活跃订阅
            var activeSubscriptions = await _unitOfWork.Subscriptions.GetQueryable()
                .Where(s => userIds.Contains(s.CustomerUserId) && s.Status == SubscriptionStatus.Active)
                .Include(s => s.Plan)
                .ToListAsync(cancellationToken);
            
            // 更新用户列表中的订阅信息和消费金额
            foreach (var user in users)
            {
                var subscription = activeSubscriptions.FirstOrDefault(s => s.CustomerUserId == user.Id);
                if (subscription != null)
                {
                    user.SubscriptionTier = subscription.Plan?.Tier.ToString();
                }
                
                // 查询每个用户的总消费金额
                user.TotalSpent = await _unitOfWork.Orders.GetUserTotalPaymentAsync(user.Id, cancellationToken);
            }

            var result = new PagedResult<AdminCustomerListDto>
            {
                Items = users,
                TotalCount = totalCount,
                PageNumber = request.Page,
                PageSize = request.PageSize
            };

            return Result<PagedResult<AdminCustomerListDto>>.Success(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "搜索客户用户失败");
            return Result<PagedResult<AdminCustomerListDto>>.Failure("搜索客户用户失败");
        }
    }

    public async Task<Result<AdminCustomerDetailDto>> GetCustomerDetailAsync(Guid customerId, CancellationToken cancellationToken = default)
    {
        try
        {
            var user = await _customerUserRepository.GetByIdAsync(customerId, cancellationToken);
            if (user == null)
            {
                return Result<AdminCustomerDetailDto>.Failure("用户不存在");
            }

            var detail = new AdminCustomerDetailDto
            {
                Id = user.Id,
                Username = user.Username,
                Email = user.Email?.Value,
                Phone = user.Phone?.Value,
                Nickname = user.Nickname,
                Avatar = user.Avatar,
                Bio = user.Bio,
                Status = user.IsActive ? (user.IsBanned ? UserStatus.Suspended : UserStatus.Active) : UserStatus.Inactive,
                IsEmailVerified = user.IsEmailVerified,
                IsPhoneVerified = user.IsPhoneVerified,
                TwoFactorEnabled = user.TwoFactorEnabled,
                CreatedAt = user.CreatedAt,
                LastActiveAt = user.LastLoginAt,
                LastLoginAt = user.LastLoginAt,
                LastLoginIp = user.LastLoginIp,
                LoginFailedCount = user.LoginFailedCount,
                LockoutEnd = user.GetLockRemainingMinutes() > 0 ? DateTime.UtcNow.AddMinutes(user.GetLockRemainingMinutes()) : null,
                OAuthBindings = new List<AdminCustomerOAuthDto>(),
                UsageStats = new AdminCustomerUsageStatsDto()
            };

            // OAuth绑定信息
            detail.OAuthBindings = user.OAuthBindings.Select(b => new AdminCustomerOAuthDto
            {
                Provider = b.Provider,
                ExternalId = b.ProviderUserId,
                ExternalUsername = b.DisplayName,
                CreatedAt = b.CreatedAt
            }).ToList();
            
            // 查询当前订阅信息
            var currentSubscription = await _unitOfWork.Subscriptions.GetActiveSubscriptionAsync(customerId, cancellationToken);
            if (currentSubscription != null)
            {
                // Get subscription plan details
                var plan = await _unitOfWork.SubscriptionPlans.GetByIdAsync(currentSubscription.PlanId, cancellationToken);
                
                detail.CurrentSubscription = new AdminCustomerSubscriptionDto
                {
                    Id = currentSubscription.Id,
                    PlanName = plan?.Name ?? "Unknown",
                    Tier = plan?.Tier.ToString() ?? "Free",
                    Status = currentSubscription.Status.ToString(),
                    StartDate = currentSubscription.StartDate,
                    EndDate = currentSubscription.EndDate,
                    NextBillingDate = currentSubscription.NextBillingDate,
                    TokenQuotaTotal = plan?.TokenQuota ?? 0,
                    TokenQuotaUsed = (plan?.TokenQuota ?? 0) - currentSubscription.RemainingTokens,
                    RemainingTokens = currentSubscription.RemainingTokens,
                    AutoRenew = currentSubscription.AutoRenew,
                    PaymentMethod = currentSubscription.PaymentMethod
                };
            }
            
            // 查询使用统计
            var usageRecords = await _unitOfWork.UsageRecords.GetQueryable()
                .Where(u => u.UserId == customerId)
                .ToListAsync(cancellationToken);
            var totalTokensUsed = usageRecords.Sum(u => u.TokensUsed);
            
            var totalConversations = await _unitOfWork.Conversations.GetQueryable()
                .Where(c => c.CustomerUserId == customerId)
                .CountAsync(cancellationToken);
            
            var orders = await _unitOfWork.Orders.GetUserOrdersAsync(customerId, null, cancellationToken);
            var ordersList = orders.ToList();
            var totalSpent = await _unitOfWork.Orders.GetUserTotalPaymentAsync(customerId, cancellationToken);
            
            var lastConversation = await _unitOfWork.Conversations.GetQueryable()
                .Where(c => c.CustomerUserId == customerId)
                .OrderByDescending(c => c.CreatedAt)
                .Select(c => c.CreatedAt)
                .FirstOrDefaultAsync(cancellationToken);
            
            // Get total messages count
            var totalMessages = await _unitOfWork.Repository<ConversationMessage>().GetQueryable()
                .Where(m => m.Conversation.CustomerUserId == customerId)
                .CountAsync(cancellationToken);
            
            detail.UsageStats = new AdminCustomerUsageStatsDto
            {
                TotalTokensUsed = totalTokensUsed,
                TotalConversations = totalConversations,
                TotalMessages = totalMessages,
                TotalSpent = totalSpent,
                CreatedAgents = 0, // Will need to query agents when available
                FavoriteAgents = 0, // Will need to query favorites when available
                TopUsedAgents = new List<string>(), // Will need to implement when agent tracking is available
                MonthlyActiveDays = 0 // Will need to calculate from login history
            };

            return Result<AdminCustomerDetailDto>.Success(detail);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取客户详情失败，用户ID: {CustomerId}", customerId);
            return Result<AdminCustomerDetailDto>.Failure("获取客户详情失败");
        }
    }

    public async Task<Result> UpdateCustomerStatusAsync(Guid customerId, AdminUpdateCustomerStatusDto request, Guid adminId, CancellationToken cancellationToken = default)
    {
        try
        {
            var user = await _customerUserRepository.GetByIdAsync(customerId, cancellationToken);
            if (user == null)
            {
                return Result.Failure("用户不存在");
            }

            // 更新状态
            switch (request.Status)
            {
                case UserStatus.Active:
                    user.SetActive();
                    break;
                case UserStatus.Inactive:
                    user.SetInactive();
                    break;
                case UserStatus.Suspended:
                    user.Ban();
                    // 清除登录失败次数和锁定
                    user.ResetLoginFailures();
                    break;
            }
            user.UpdatedBy = adminId.ToString();

            await _unitOfWork.SaveChangesAsync(cancellationToken);

            // 记录审计日志
            await _auditLogger.LogActionAsync(
                $"UpdateCustomerStatus_{customerId}",
                "CustomerUserManagement",
                $"更新客户用户状态: {request.Status}",
                new { CustomerId = customerId, NewStatus = request.Status, Reason = request.Reason, AdminId = adminId });

            _logger.LogInformation("客户用户状态已更新，用户ID: {CustomerId}, 新状态: {Status}, 操作管理员: {AdminId}", 
                customerId, request.Status, adminId);

            return Result.Success();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "更新客户状态失败，用户ID: {CustomerId}", customerId);
            return Result.Failure("更新客户状态失败");
        }
    }

    public async Task<Result<string>> ResetCustomerPasswordAsync(Guid customerId, AdminResetCustomerPasswordDto request, Guid adminId, CancellationToken cancellationToken = default)
    {
        try
        {
            var user = await _customerUserRepository.GetByIdAsync(customerId, cancellationToken);
            if (user == null)
            {
                return Result<string>.Failure("用户不存在");
            }

            // 生成新密码
            string newPassword;
            if (string.IsNullOrWhiteSpace(request.NewPassword))
            {
                // 生成随机密码
                newPassword = GenerateRandomPassword();
            }
            else
            {
                newPassword = request.NewPassword;
            }

            // 设置新密码
            user.ResetPassword(newPassword);
            user.UpdatedBy = adminId.ToString();

            // 清除登录失败次数和锁定
            user.ResetLoginFailures();

            await _unitOfWork.SaveChangesAsync(cancellationToken);

            // 发送邮件通知
            if (request.SendEmail && user.Email != null && user.IsEmailVerified)
            {
                try
                {
                    var emailBody = $@"
                        <h2>密码重置通知</h2>
                        <p>尊敬的用户 <strong>{user.Username}</strong>，</p>
                        <p>您的账户密码已被管理员重置。</p>
                        <div style='background-color: #f5f5f5; padding: 15px; border-radius: 5px; margin: 20px 0;'>
                            <p><strong>新密码：</strong></p>
                            <p style='font-family: monospace; font-size: 16px; color: #333; letter-spacing: 1px;'>{newPassword}</p>
                        </div>
                        <p><strong>重要提示：</strong></p>
                        <ul>
                            <li>请立即使用新密码登录</li>
                            <li>首次登录后，强烈建议您修改密码</li>
                            <li>新密码仅显示这一次，请妥善保管</li>
                            <li>不要将密码告诉任何人，包括自称是我们工作人员的人</li>
                        </ul>
                        <p>如需修改密码，请登录后访问个人中心 > 账户安全 > 修改密码。</p>
                        <p style='color: #666; margin-top: 30px;'>如果您没有请求重置密码，请立即联系我们的客服团队。</p>
                        <hr style='margin: 30px 0; border: none; border-top: 1px solid #eee;'>
                        <p style='color: #999; font-size: 12px;'>
                            此邮件由系统自动发送，请勿直接回复。<br>
                            发送时间：{DateTime.Now:yyyy-MM-dd HH:mm:ss}<br>
                            WhimLabAI 团队
                        </p>
                    ";
                    
                    var emailSent = await _emailService.SendEmailAsync(
                        user.Email.Value,
                        "您的密码已重置 - WhimLabAI",
                        emailBody,
                        true,
                        cancellationToken);
                    
                    if (emailSent)
                    {
                        _logger.LogInformation("密码重置邮件已发送至: {Email}", user.Email.Value);
                    }
                    else
                    {
                        _logger.LogWarning("密码重置邮件发送失败: {Email}", user.Email.Value);
                    }
                }
                catch (Exception emailEx)
                {
                    _logger.LogError(emailEx, "发送密码重置邮件时发生异常: {Email}", user.Email.Value);
                    // 不影响主流程
                }
            }

            // 记录审计日志
            await _auditLogger.LogActionAsync(
                $"ResetCustomerPassword_{customerId}",
                "CustomerUserManagement",
                "重置客户用户密码",
                new { CustomerId = customerId, SendEmail = request.SendEmail, AdminId = adminId });

            _logger.LogInformation("客户用户密码已重置，用户ID: {CustomerId}, 操作管理员: {AdminId}", customerId, adminId);

            return Result<string>.Success(newPassword);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "重置客户密码失败，用户ID: {CustomerId}", customerId);
            return Result<string>.Failure("重置客户密码失败");
        }
    }

    public async Task<Result> UnlockCustomerAccountAsync(Guid customerId, Guid adminId, CancellationToken cancellationToken = default)
    {
        try
        {
            var user = await _customerUserRepository.GetByIdAsync(customerId, cancellationToken);
            if (user == null)
            {
                return Result.Failure("用户不存在");
            }

            // 解锁账号
            user.ResetLoginFailures();
            user.UpdatedBy = adminId.ToString();

            await _unitOfWork.SaveChangesAsync(cancellationToken);

            // 记录审计日志
            await _auditLogger.LogActionAsync(
                $"UnlockCustomerAccount_{customerId}",
                "CustomerUserManagement",
                "解锁客户用户账号",
                new { CustomerId = customerId, AdminId = adminId });

            _logger.LogInformation("客户用户账号已解锁，用户ID: {CustomerId}, 操作管理员: {AdminId}", customerId, adminId);

            return Result.Success();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "解锁客户账号失败，用户ID: {CustomerId}", customerId);
            return Result.Failure("解锁客户账号失败");
        }
    }

    public async Task<Result> ResendVerificationEmailAsync(Guid customerId, Guid adminId, CancellationToken cancellationToken = default)
    {
        try
        {
            var user = await _customerUserRepository.GetByIdAsync(customerId, cancellationToken);
            if (user == null)
            {
                return Result.Failure("用户不存在");
            }

            if (user.Email == null)
            {
                return Result.Failure("用户未设置邮箱");
            }

            if (user.IsEmailVerified)
            {
                return Result.Failure("邮箱已验证");
            }

            // 发送验证邮件
            var result = await _verificationService.SendEmailCodeAsync(
                user.Email.Value, 
                VerificationType.ChangeEmail, 
                cancellationToken);

            if (!result.IsSuccess)
            {
                return Result.Failure("发送验证邮件失败");
            }

            // 记录审计日志
            await _auditLogger.LogActionAsync(
                $"ResendVerificationEmail_{customerId}",
                "CustomerUserManagement",
                "重新发送邮箱验证邮件",
                new { CustomerId = customerId, Email = user.Email.Value, AdminId = adminId });

            _logger.LogInformation("邮箱验证邮件已重新发送，用户ID: {CustomerId}, 操作管理员: {AdminId}", customerId, adminId);

            return Result.Success();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "重新发送验证邮件失败，用户ID: {CustomerId}", customerId);
            return Result.Failure("重新发送验证邮件失败");
        }
    }

    public async Task<Result> ResendVerificationSmsAsync(Guid customerId, Guid adminId, CancellationToken cancellationToken = default)
    {
        try
        {
            var user = await _customerUserRepository.GetByIdAsync(customerId, cancellationToken);
            if (user == null)
            {
                return Result.Failure("用户不存在");
            }

            if (user.Phone == null)
            {
                return Result.Failure("用户未设置手机号");
            }

            if (user.IsPhoneVerified)
            {
                return Result.Failure("手机号已验证");
            }

            // 发送验证短信
            var result = await _verificationService.SendSmsCodeAsync(
                user.Phone.Value, 
                VerificationType.ChangePhone, 
                cancellationToken);

            if (!result.IsSuccess)
            {
                return Result.Failure("发送验证短信失败");
            }

            // 记录审计日志
            await _auditLogger.LogActionAsync(
                $"ResendVerificationSms_{customerId}",
                "CustomerUserManagement",
                "重新发送手机验证短信",
                new { CustomerId = customerId, Phone = user.Phone.Value, AdminId = adminId });

            _logger.LogInformation("手机验证短信已重新发送，用户ID: {CustomerId}, 操作管理员: {AdminId}", customerId, adminId);

            return Result.Success();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "重新发送验证短信失败，用户ID: {CustomerId}", customerId);
            return Result.Failure("重新发送验证短信失败");
        }
    }

    private static string GenerateRandomPassword()
    {
        const string chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789!@#$%^&*";
        var random = new Random();
        var password = new char[12];
        
        // 确保包含各种字符类型
        password[0] = chars[random.Next(0, 26)]; // 大写字母
        password[1] = chars[random.Next(26, 52)]; // 小写字母
        password[2] = chars[random.Next(52, 62)]; // 数字
        password[3] = chars[random.Next(62, chars.Length)]; // 特殊字符
        
        // 剩余位置随机填充
        for (int i = 4; i < password.Length; i++)
        {
            password[i] = chars[random.Next(chars.Length)];
        }
        
        // 打乱顺序
        return new string(password.OrderBy(x => random.Next()).ToArray());
    }

    private static bool VerifyTotpCode(string? secret, string code)
    {
        if (string.IsNullOrEmpty(secret) || string.IsNullOrEmpty(code))
            return false;
            
        try
        {
            var totp = new Totp(Base32Encoding.ToBytes(secret));
            
            // 验证当前时间窗口和前后一个时间窗口的码（容许一定的时间偏差）
            var window = new VerificationWindow(previous: 1, future: 1);
            
            return totp.VerifyTotp(code, out long timeStepMatched, window);
        }
        catch (Exception)
        {
            return false;
        }
    }

    #endregion
    
    #region MFA管理
    
    public async Task<Result<AdminCustomerMfaStatusDto>> GetCustomerMfaStatusAsync(Guid customerId, CancellationToken cancellationToken = default)
    {
        try
        {
            var user = await _customerUserRepository.GetByIdAsync(customerId, cancellationToken);
            if (user == null)
            {
                return Result<AdminCustomerMfaStatusDto>.Failure("用户不存在");
            }
            
            var status = new AdminCustomerMfaStatusDto
            {
                TwoFactorEnabled = user.TwoFactorEnabled,
                TwoFactorEnabledAt = user.TwoFactorEnabled ? user.UpdatedAt : null,
                LastMfaUsedAt = await GetLastMfaUsageTimeAsync(customerId, cancellationToken),
                RemainingRecoveryCodes = user.RecoveryCodes.Count(rc => rc.IsValid()),
                AuthorizedDevicesCount = user.DeviceAuthorizations.Count(d => d.IsActive),
                RecentFailedLoginAttempts = await GetRecentFailedLoginAttemptsAsync(customerId, cancellationToken)
            };
            
            return Result<AdminCustomerMfaStatusDto>.Success(status);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取客户MFA状态失败，客户ID: {CustomerId}", customerId);
            return Result<AdminCustomerMfaStatusDto>.Failure("获取MFA状态失败");
        }
    }
    
    public async Task<Result> ResetCustomerMfaAsync(Guid customerId, AdminResetCustomerMfaDto request, Guid adminId, CancellationToken cancellationToken = default)
    {
        try
        {
            var user = await _customerUserRepository.GetByIdAsync(customerId, cancellationToken);
            if (user == null)
            {
                return Result.Failure("用户不存在");
            }
            
            // 禁用双因素认证
            user.DisableTwoFactor();
            await _unitOfWork.SaveChangesAsync(cancellationToken);
            
            // 记录审计日志
            await _auditLogger.LogActionAsync(
                $"ResetCustomerMfa_{customerId}",
                "CustomerUserManagement",
                "重置客户MFA",
                new { CustomerId = customerId, AdminId = adminId, Reason = request.Reason });
                
            _logger.LogInformation("客户MFA已重置，客户ID: {CustomerId}, 操作管理员: {AdminId}", customerId, adminId);
            
            return Result.Success();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "重置客户MFA失败，客户ID: {CustomerId}", customerId);
            return Result.Failure("重置MFA失败");
        }
    }
    
    public async Task<Result<PagedResult<AdminCustomerLoginHistoryDto>>> GetCustomerLoginHistoryAsync(Guid customerId, int pageNumber, int pageSize, CancellationToken cancellationToken = default)
    {
        try
        {
            var user = await _customerUserRepository.GetByIdAsync(customerId, cancellationToken);
            if (user == null)
            {
                return Result<PagedResult<AdminCustomerLoginHistoryDto>>.Failure("用户不存在");
            }
            
            // Get login history from repository
            var loginHistoryRepo = _unitOfWork.Repository<LoginHistory>();
            var query = loginHistoryRepo.GetQueryable()
                .Where(h => h.UserId == customerId)
                .OrderByDescending(h => h.LoginAt);

            var total = await query.CountAsync(cancellationToken);
            var skip = (pageNumber - 1) * pageSize;
            
            var histories = await query
                .Skip(skip)
                .Take(pageSize)
                .Select(h => new AdminCustomerLoginHistoryDto
                {
                    LoginTime = h.LoginAt,
                    LoginMethod = h.LoginMethod.ToString(),
                    IpAddress = h.LoginIp ?? "Unknown",
                    Location = h.Location,
                    DeviceInfo = h.Device,
                    IsSuccess = h.IsSuccess,
                    FailureReason = h.FailureReason,
                    UsedMfa = h.LoginMethod == LoginMethod.Password && h.IsSuccess // Simplified MFA detection
                })
                .ToListAsync(cancellationToken);

            var result = new PagedResult<AdminCustomerLoginHistoryDto>(
                items: histories,
                totalCount: total,
                pageNumber: pageNumber,
                pageSize: pageSize);
            
            return Result<PagedResult<AdminCustomerLoginHistoryDto>>.Success(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取客户登录历史失败，客户ID: {CustomerId}", customerId);
            return Result<PagedResult<AdminCustomerLoginHistoryDto>>.Failure("获取登录历史失败");
        }
    }
    
    #endregion
    
    #region Private Helper Methods
    
    private async Task<DateTime?> GetLastMfaUsageTimeAsync(Guid customerId, CancellationToken cancellationToken)
    {
        try
        {
            // Query login history for successful MFA logins
            var loginHistoryRepo = _unitOfWork.Repository<LoginHistory>();
            var lastMfaLogin = await loginHistoryRepo.GetQueryable()
                .Where(h => h.UserId == customerId && h.IsSuccess && h.LoginMethod == LoginMethod.Password)
                .OrderByDescending(h => h.LoginAt)
                .Select(h => h.LoginAt)
                .FirstOrDefaultAsync(cancellationToken);
                
            return lastMfaLogin == default ? null : lastMfaLogin;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取最后MFA使用时间失败，客户ID: {CustomerId}", customerId);
            return null;
        }
    }
    
    private async Task<int> GetRecentFailedLoginAttemptsAsync(Guid customerId, CancellationToken cancellationToken)
    {
        try
        {
            // Count failed login attempts in the last 24 hours
            var loginHistoryRepo = _unitOfWork.Repository<LoginHistory>();
            var since = DateTime.UtcNow.AddHours(-24);
            
            return await loginHistoryRepo.GetQueryable()
                .Where(h => h.UserId == customerId && !h.IsSuccess && h.LoginAt >= since)
                .CountAsync(cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取最近登录失败次数失败，客户ID: {CustomerId}", customerId);
            return 0;
        }
    }
    
    #endregion
}