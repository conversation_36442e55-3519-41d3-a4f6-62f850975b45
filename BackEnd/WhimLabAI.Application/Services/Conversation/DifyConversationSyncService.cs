using Microsoft.Extensions.Logging;
using WhimLabAI.Abstractions.Application;
using WhimLabAI.Abstractions.Infrastructure;
using WhimLabAI.Domain.Entities.Conversation;
using WhimLabAI.Domain.Repositories;
using WhimLabAI.Shared.Results;

namespace WhimLabAI.Application.Services.Conversation;

/// <summary>
/// Dify对话同步服务实现
/// </summary>
public class DifyConversationSyncService : IDifyConversationSyncService
{
    private readonly IUnitOfWork _unitOfWork;
    private readonly IDifyProvider _difyProvider;
    private readonly ICacheService _cacheService;
    private readonly IDataEncryptionService _encryptionService;
    private readonly ILogger<DifyConversationSyncService> _logger;
    private const string CachePrefixDifyMapping = "dify_conversation_mapping:";
    private const int CacheExpirationMinutes = 30;

    public DifyConversationSyncService(
        IUnitOfWork unitOfWork,
        IDifyProvider difyProvider,
        ICacheService cacheService,
        IDataEncryptionService encryptionService,
        ILogger<DifyConversationSyncService> logger)
    {
        _unitOfWork = unitOfWork;
        _difyProvider = difyProvider;
        _cacheService = cacheService;
        _encryptionService = encryptionService;
        _logger = logger;
    }

    public async Task<Result> CreateOrUpdateMappingAsync(
        Guid conversationId, 
        Guid agentId, 
        string difyConversationId, 
        CancellationToken cancellationToken = default)
    {
        try
        {
            // Check if mapping already exists
            var existingMapping = await _unitOfWork.DifyConversationMappings
                .GetByConversationIdAsync(conversationId, cancellationToken);
            
            if (existingMapping != null)
            {
                // Update existing mapping
                existingMapping.UpdateDifyConversationId(difyConversationId);
                existingMapping.UpdateSyncTime();
                await _unitOfWork.DifyConversationMappings.UpdateAsync(existingMapping, cancellationToken);
            }
            else
            {
                // Create new mapping
                var newMapping = DifyConversationMapping.Create(conversationId, agentId, difyConversationId);
                await _unitOfWork.DifyConversationMappings.AddAsync(newMapping, cancellationToken);
            }
            
            await _unitOfWork.SaveChangesAsync(cancellationToken);
            
            // Update cache
            var cacheKey = $"{CachePrefixDifyMapping}{conversationId}";
            await _cacheService.SetAsync(cacheKey, difyConversationId, TimeSpan.FromMinutes(CacheExpirationMinutes), cancellationToken);
            
            _logger.LogInformation("Created/Updated Dify conversation mapping: ConversationId={ConversationId}, DifyConversationId={DifyConversationId}", 
                conversationId, difyConversationId);
                
            return Result.Success();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating/updating Dify conversation mapping");
            return Result.Failure("DIFY_MAPPING_ERROR", "创建或更新Dify对话映射失败");
        }
    }

    public async Task<Result<string>> GetDifyConversationIdAsync(
        Guid conversationId, 
        CancellationToken cancellationToken = default)
    {
        try
        {
            // Check cache first
            var cacheKey = $"{CachePrefixDifyMapping}{conversationId}";
            var cachedValue = await _cacheService.GetAsync<string>(cacheKey, cancellationToken);
            
            if (!string.IsNullOrEmpty(cachedValue))
            {
                return Result<string>.Success(cachedValue);
            }
            
            // Get from database
            var mapping = await _unitOfWork.DifyConversationMappings
                .GetByConversationIdAsync(conversationId, cancellationToken);
                
            if (mapping == null)
            {
                return Result<string>.Failure("MAPPING_NOT_FOUND", "未找到Dify对话映射");
            }
            
            // Cache the result
            await _cacheService.SetAsync(cacheKey, mapping.DifyConversationId, 
                TimeSpan.FromMinutes(CacheExpirationMinutes), cancellationToken);
            
            return Result<string>.Success(mapping.DifyConversationId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting Dify conversation ID for ConversationId: {ConversationId}", conversationId);
            return Result<string>.Failure("GET_DIFY_ID_ERROR", "获取Dify对话ID失败");
        }
    }

    public async Task<Result> SyncConversationHistoryAsync(
        Guid conversationId, 
        CancellationToken cancellationToken = default)
    {
        try
        {
            // Get mapping
            var mapping = await _unitOfWork.DifyConversationMappings
                .GetByConversationIdAsync(conversationId, cancellationToken);
                
            if (mapping == null)
            {
                return Result.Failure("MAPPING_NOT_FOUND", "未找到Dify对话映射");
            }
            
            // Get agent to retrieve API key
            var conversation = await _unitOfWork.Conversations.GetByIdAsync(conversationId, cancellationToken);
            if (conversation == null)
            {
                return Result.Failure("CONVERSATION_NOT_FOUND", "对话不存在");
            }
            
            var agent = await _unitOfWork.Agents.GetByIdAsync(conversation.AgentId, cancellationToken);
            if (agent == null)
            {
                return Result.Failure("AGENT_NOT_FOUND", "Agent不存在");
            }
            
            var difyApiKey = agent.GetDifyApiKey();
            if (difyApiKey == null || !difyApiKey.IsValid())
            {
                return Result.Failure("DIFY_API_KEY_MISSING", "Dify API密钥未配置或已失效");
            }
            
            // Get history from Dify
            var decryptedApiKey = _encryptionService.DecryptString(difyApiKey.GetEncryptedApiKey());
            var history = await _difyProvider.GetConversationHistoryAsync(
                mapping.DifyConversationId, 
                decryptedApiKey, 
                cancellationToken);
            
            // Update sync time
            mapping.UpdateSyncTime();
            await _unitOfWork.DifyConversationMappings.UpdateAsync(mapping, cancellationToken);
            await _unitOfWork.SaveChangesAsync(cancellationToken);
            
            _logger.LogInformation("Synced Dify conversation history: ConversationId={ConversationId}, MessageCount={MessageCount}", 
                conversationId, history.Messages?.Count ?? 0);
                
            return Result.Success();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error syncing Dify conversation history");
            return Result.Failure("SYNC_HISTORY_ERROR", "同步Dify对话历史失败");
        }
    }

    public async Task<Result<int>> CleanupExpiredMappingsAsync(
        int daysToKeep = 30, 
        CancellationToken cancellationToken = default)
    {
        try
        {
            var cutoffDate = DateTime.UtcNow.AddDays(-daysToKeep);
            var expiredMappings = await _unitOfWork.DifyConversationMappings
                .GetByAgentIdAsync(Guid.Empty, cancellationToken); // Need to implement a better query method
            
            var deletedCount = 0;
            foreach (var mapping in expiredMappings.Where(m => m.LastSyncedAt < cutoffDate))
            {
                await _unitOfWork.DifyConversationMappings.DeleteAsync(mapping.Id, cancellationToken);
                deletedCount++;
                
                // Clear cache
                var cacheKey = $"{CachePrefixDifyMapping}{mapping.ConversationId}";
                await _cacheService.RemoveAsync(cacheKey, cancellationToken);
            }
            
            if (deletedCount > 0)
            {
                await _unitOfWork.SaveChangesAsync(cancellationToken);
            }
            
            _logger.LogInformation("Cleaned up {Count} expired Dify conversation mappings", deletedCount);
            return Result<int>.Success(deletedCount);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error cleaning up expired Dify mappings");
            return Result<int>.Failure("CLEANUP_ERROR", "清理过期映射失败");
        }
    }
}