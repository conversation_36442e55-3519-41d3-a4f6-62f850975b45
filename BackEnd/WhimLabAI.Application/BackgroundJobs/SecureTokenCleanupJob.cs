using Microsoft.Extensions.Logging;
using WhimLabAI.Abstractions.Infrastructure;

namespace WhimLabAI.Application.BackgroundJobs;

/// <summary>
/// Background job to clean up expired secure tokens
/// </summary>
public class SecureTokenCleanupJob
{
    private readonly ISecureTokenStorage _tokenStorage;
    private readonly ILogger<SecureTokenCleanupJob> _logger;

    public SecureTokenCleanupJob(
        ISecureTokenStorage tokenStorage,
        ILogger<SecureTokenCleanupJob> logger)
    {
        _tokenStorage = tokenStorage;
        _logger = logger;
    }

    /// <summary>
    /// Cleans up expired tokens
    /// </summary>
    public async Task CleanupExpiredTokensAsync(CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Starting secure token cleanup job");
            
            await _tokenStorage.CleanupExpiredTokensAsync();
            
            _logger.LogInformation("Secure token cleanup job completed successfully");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during secure token cleanup job");
            throw;
        }
    }
}