using System;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using WhimLabAI.Abstractions.Application;
using Hangfire;

namespace WhimLabAI.Application.BackgroundJobs;

/// <summary>
/// 账号注销后台任务
/// </summary>
public class AccountDeletionJob
{
    private readonly IAccountDeletionService _accountDeletionService;
    private readonly ILogger<AccountDeletionJob> _logger;

    public AccountDeletionJob(
        IAccountDeletionService accountDeletionService,
        ILogger<AccountDeletionJob> logger)
    {
        _accountDeletionService = accountDeletionService;
        _logger = logger;
    }

    /// <summary>
    /// 配置账号注销相关的定时任务
    /// </summary>
    public static void Configure()
    {
        // 每小时检查一次冷静期提醒
        RecurringJob.AddOrUpdate<AccountDeletionJob>(
            "account-deletion-reminders",
            job => job.SendCoolingOffRemindersAsync(CancellationToken.None),
            Cron.Hourly);

        // 每小时检查一次需要冻结的账号
        RecurringJob.AddOrUpdate<AccountDeletionJob>(
            "account-deletion-freeze",
            job => job.ProcessCoolingOffCompletedRequestsAsync(CancellationToken.None),
            Cron.Hourly);

        // 每天凌晨2点检查需要匿名化的账号
        RecurringJob.AddOrUpdate<AccountDeletionJob>(
            "account-deletion-anonymize",
            job => job.ProcessPendingAnonymizationRequestsAsync(CancellationToken.None),
            Cron.Daily(2));
    }

    /// <summary>
    /// 发送冷静期即将结束提醒
    /// </summary>
    [AutomaticRetry(Attempts = 3)]
    public async Task SendCoolingOffRemindersAsync(CancellationToken cancellationToken)
    {
        try
        {
            _logger.LogInformation("Starting to send cooling off reminders");
            
            var result = await _accountDeletionService.SendCoolingOffRemindersAsync(cancellationToken);
            
            if (result.IsSuccess)
            {
                _logger.LogInformation("Sent {Count} cooling off reminders", result.Value);
            }
            else
            {
                _logger.LogError("Failed to send cooling off reminders: {Error}", result.Error);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error occurred while sending cooling off reminders");
            throw;
        }
    }

    /// <summary>
    /// 处理冷静期结束的请求（冻结账号）
    /// </summary>
    [AutomaticRetry(Attempts = 3)]
    public async Task ProcessCoolingOffCompletedRequestsAsync(CancellationToken cancellationToken)
    {
        try
        {
            _logger.LogInformation("Starting to process cooling off completed requests");
            
            var result = await _accountDeletionService.ProcessCoolingOffCompletedRequestsAsync(cancellationToken);
            
            if (result.IsSuccess)
            {
                _logger.LogInformation("Processed {Count} cooling off completed requests", result.Value);
            }
            else
            {
                _logger.LogError("Failed to process cooling off completed requests: {Error}", result.Error);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error occurred while processing cooling off completed requests");
            throw;
        }
    }

    /// <summary>
    /// 处理待匿名化的请求
    /// </summary>
    [AutomaticRetry(Attempts = 3)]
    public async Task ProcessPendingAnonymizationRequestsAsync(CancellationToken cancellationToken)
    {
        try
        {
            _logger.LogInformation("Starting to process pending anonymization requests");
            
            var result = await _accountDeletionService.ProcessPendingAnonymizationRequestsAsync(cancellationToken);
            
            if (result.IsSuccess)
            {
                _logger.LogInformation("Processed {Count} anonymization requests", result.Value);
            }
            else
            {
                _logger.LogError("Failed to process anonymization requests: {Error}", result.Error);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error occurred while processing anonymization requests");
            throw;
        }
    }
}