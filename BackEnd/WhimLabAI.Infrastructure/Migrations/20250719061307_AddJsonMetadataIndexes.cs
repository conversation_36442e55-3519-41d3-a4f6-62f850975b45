﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace WhimLabAI.Infrastructure.Migrations
{
    /// <inheritdoc />
    public partial class AddJsonMetadataIndexes : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            // Note: CREATE INDEX CONCURRENTLY cannot run inside a transaction
            // EF Core migrations run in a transaction by default
            // So we'll create the indexes without CONCURRENTLY for the migration
            // In production, you may want to run these as separate SQL scripts with CONCURRENTLY

            // ==================================
            // GIN Indexes for JSON Metadata Queries
            // ==================================

            // Agent.metadata - General GIN index for all metadata queries
            migrationBuilder.Sql(@"
                CREATE INDEX IF NOT EXISTS ""IX_agents_metadata_GIN"" 
                ON agents USING GIN (metadata jsonb_path_ops);
            ");

            // Conversation.metadata - General GIN index
            migrationBuilder.Sql(@"
                CREATE INDEX IF NOT EXISTS ""IX_conversations_metadata_GIN"" 
                ON conversations USING GIN (metadata jsonb_path_ops);
            ");

            // Other JSON columns that exist in the database
            migrationBuilder.Sql(@"
                -- Agent tags
                CREATE INDEX IF NOT EXISTS ""IX_agents_tags_GIN"" 
                ON agents USING GIN (tags);
            ");

            // ==================================
            // Indexes for Frequently Filtered Fields
            // ==================================

            // Agent status and publishing indexes
            migrationBuilder.Sql(@"
                CREATE INDEX IF NOT EXISTS ""IX_agents_status"" 
                ON agents (status);

                CREATE INDEX IF NOT EXISTS ""IX_agents_published_at"" 
                ON agents (published_at DESC)
                WHERE published_at IS NOT NULL;
            ");

            // ==================================
            // Expression Indexes for Case-Insensitive Searches
            // ==================================

            migrationBuilder.Sql(@"
                -- Case-insensitive email search
                CREATE INDEX IF NOT EXISTS ""IX_customer_users_email_lower"" 
                ON customer_users (LOWER(email));

                CREATE INDEX IF NOT EXISTS ""IX_admin_users_email_lower"" 
                ON admin_users (LOWER(email));

                -- Case-insensitive username search
                CREATE INDEX IF NOT EXISTS ""IX_customer_users_username_lower"" 
                ON customer_users (LOWER(username));

                CREATE INDEX IF NOT EXISTS ""IX_admin_users_username_lower"" 
                ON admin_users (LOWER(username));

                -- Case-insensitive agent name search
                CREATE INDEX IF NOT EXISTS ""IX_agents_name_lower"" 
                ON agents (LOWER(name));

                -- Case-insensitive conversation title search
                CREATE INDEX IF NOT EXISTS ""IX_conversations_title_lower"" 
                ON conversations (LOWER(title))
                WHERE title IS NOT NULL;
            ");

            // ==================================
            // Composite Indexes for Common Query Patterns
            // ==================================

            // User's conversations
            migrationBuilder.Sql(@"
                CREATE INDEX IF NOT EXISTS ""IX_conversations_user_id_created_at"" 
                ON conversations (user_id, created_at DESC);
            ");

            // Agent's conversations
            migrationBuilder.Sql(@"
                CREATE INDEX IF NOT EXISTS ""IX_conversations_agent_id_created_at"" 
                ON conversations (agent_id, created_at DESC);
            ");

            // Active agents
            migrationBuilder.Sql(@"
                CREATE INDEX IF NOT EXISTS ""IX_agents_status_category_rating"" 
                ON agents (status, category_id, average_rating DESC);
            ");

            // Conversation archival status
            migrationBuilder.Sql(@"
                CREATE INDEX IF NOT EXISTS ""IX_conversations_is_archived"" 
                ON conversations (is_archived)
                WHERE is_archived = false;
            ");

            // Conversation last message time
            migrationBuilder.Sql(@"
                CREATE INDEX IF NOT EXISTS ""IX_conversations_last_message_at"" 
                ON conversations (last_message_at DESC);
            ");

            // Analyze tables to update statistics
            migrationBuilder.Sql(@"
                ANALYZE agents;
                ANALYZE conversations;
                ANALYZE customer_users;
                ANALYZE admin_users;
            ");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            // Drop all created indexes
            migrationBuilder.Sql(@"
                DROP INDEX IF EXISTS ""IX_agents_metadata_GIN"";
                DROP INDEX IF EXISTS ""IX_conversations_metadata_GIN"";
                DROP INDEX IF EXISTS ""IX_agents_tags_GIN"";
                DROP INDEX IF EXISTS ""IX_agents_status"";
                DROP INDEX IF EXISTS ""IX_agents_published_at"";
                DROP INDEX IF EXISTS ""IX_customer_users_email_lower"";
                DROP INDEX IF EXISTS ""IX_admin_users_email_lower"";
                DROP INDEX IF EXISTS ""IX_customer_users_username_lower"";
                DROP INDEX IF EXISTS ""IX_admin_users_username_lower"";
                DROP INDEX IF EXISTS ""IX_agents_name_lower"";
                DROP INDEX IF EXISTS ""IX_conversations_title_lower"";
                DROP INDEX IF EXISTS ""IX_conversations_user_id_created_at"";
                DROP INDEX IF EXISTS ""IX_conversations_agent_id_created_at"";
                DROP INDEX IF EXISTS ""IX_agents_status_category_rating"";
                DROP INDEX IF EXISTS ""IX_conversations_is_archived"";
                DROP INDEX IF EXISTS ""IX_conversations_last_message_at"";
            ");
        }
    }
}