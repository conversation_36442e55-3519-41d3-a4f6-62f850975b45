using System.Security.Cryptography;
using System.Security.Cryptography.X509Certificates;
using System.Text;
using System.Text.Json;
using System.Xml.Linq;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using WhimLabAI.Abstractions.Infrastructure;
using WhimLabAI.Shared.Enums;
using WhimLabAI.Shared.Results;

namespace WhimLabAI.Infrastructure.PaymentGateways;

/// <summary>
/// 微信支付网关真实实现
/// 基于微信支付API v2版本
/// </summary>
public class WeChatPayGateway : IPaymentGateway
{
    private readonly IConfiguration _configuration;
    private readonly ILogger<WeChatPayGateway> _logger;
    private readonly HttpClient _httpClient;
    private readonly IPaymentSignatureService _signatureService;
    private readonly ISecretManagementService _secretManagementService;
    private readonly string _appId;
    private readonly string _mchId;
    private readonly string _apiKey;
    private readonly string _notifyUrl;
    private readonly string _returnUrl;
    private readonly bool _isProduction;
    private readonly string _gateway;
    private readonly string? _certificatePath;
    private readonly string? _certificatePassword;

    public PaymentMethod PaymentMethod => PaymentMethod.WeChatPay;
    public bool SupportsRefund => true;

    public WeChatPayGateway(
        IConfiguration configuration, 
        ILogger<WeChatPayGateway> logger,
        IHttpClientFactory httpClientFactory,
        IPaymentSignatureService signatureService,
        ISecretManagementService secretManagementService)
    {
        _configuration = configuration;
        _logger = logger;
        _httpClient = httpClientFactory.CreateClient();
        _signatureService = signatureService;
        _secretManagementService = secretManagementService;

        // Load configuration
        var wechatConfig = configuration.GetSection("Payment:WeChatPay");
        _appId = wechatConfig["AppId"] ?? throw new InvalidOperationException("WeChatPay AppId not configured");
        _mchId = wechatConfig["MchId"] ?? throw new InvalidOperationException("WeChatPay MchId not configured");
        
        // Load API key from secret management service
        _apiKey = _secretManagementService.GetSecretAsync("WECHATPAY_API_KEY").GetAwaiter().GetResult() 
            ?? wechatConfig["ApiKey"] 
            ?? throw new InvalidOperationException("WeChatPay ApiKey not configured");
        _isProduction = wechatConfig.GetValue<bool>("IsProduction");
        _gateway = wechatConfig["Gateway"] ?? (_isProduction 
            ? "https://api.mch.weixin.qq.com" 
            : "https://api.mch.weixin.qq.com/sandboxnew");
            
        _notifyUrl = wechatConfig["NotifyUrl"] ?? string.Empty;
        _returnUrl = wechatConfig["ReturnUrl"] ?? string.Empty;
        _certificatePath = wechatConfig["CertificatePath"];
        _certificatePassword = wechatConfig["CertificatePassword"];
    }

    public async Task<Result<PaymentCreateResult>> CreatePaymentAsync(PaymentCreateRequest request, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Creating WeChat payment for order {OrderNo}, amount: {Amount}", 
                request.OrderNo, request.Amount);

            var nonceStr = GenerateNonceStr();
            var timeStamp = DateTimeOffset.UtcNow.ToUnixTimeSeconds().ToString();

            // Build unified order parameters
            var parameters = new SortedDictionary<string, string>
            {
                ["appid"] = _appId,
                ["mch_id"] = _mchId,
                ["nonce_str"] = nonceStr,
                ["body"] = request.Subject,
                ["detail"] = request.Description ?? "",
                ["out_trade_no"] = request.PaymentNo,
                ["total_fee"] = ((int)(request.Amount * 100)).ToString(), // WeChat uses cents
                ["spbill_create_ip"] = request.UserIp ?? "127.0.0.1",
                ["notify_url"] = request.NotifyUrl ?? _notifyUrl,
                ["trade_type"] = "NATIVE", // Native scan code payment
                ["product_id"] = request.PaymentNo
            };

            // Generate sign using signature service
            var sign = _signatureService.GenerateWeChatSignature(new Dictionary<string, string>(parameters), _apiKey);
            parameters["sign"] = sign;

            // Convert to XML
            var xmlData = ToXml(parameters);
            _logger.LogDebug("WeChat unified order request: {XmlData}", xmlData);

            // Make API call
            var content = new StringContent(xmlData, Encoding.UTF8, "text/xml");
            var response = await _httpClient.PostAsync($"{_gateway}/pay/unifiedorder", content, cancellationToken);
            var responseXml = await response.Content.ReadAsStringAsync(cancellationToken);
            
            _logger.LogDebug("WeChat unified order response: {Response}", responseXml);

            // Parse response
            var responseData = ParseXml(responseXml);
            
            if (responseData.GetValueOrDefault("return_code") != "SUCCESS")
            {
                var returnMsg = responseData.GetValueOrDefault("return_msg", "未知错误");
                return Result<PaymentCreateResult>.Failure("WECHAT_API_ERROR", $"微信支付接口错误: {returnMsg}");
            }

            if (responseData.GetValueOrDefault("result_code") != "SUCCESS")
            {
                var errCode = responseData.GetValueOrDefault("err_code", "UNKNOWN");
                var errDesc = responseData.GetValueOrDefault("err_code_des", "未知错误");
                return Result<PaymentCreateResult>.Failure(errCode, $"创建支付失败: {errDesc}");
            }

            // Get QR code URL
            var codeUrl = responseData.GetValueOrDefault("code_url", "");
            if (string.IsNullOrEmpty(codeUrl))
            {
                return Result<PaymentCreateResult>.Failure("NO_CODE_URL", "未获取到支付二维码");
            }

            var result = new PaymentCreateResult
            {
                PaymentNo = request.PaymentNo,
                TransactionId = null,
                QrCode = codeUrl,
                Type = PaymentCreateType.QrCode
            };

            _logger.LogInformation("WeChat payment created successfully for order {OrderNo}", request.OrderNo);
            return Result<PaymentCreateResult>.Success(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to create WeChat payment for order {OrderNo}", request.OrderNo);
            return Result<PaymentCreateResult>.Failure("WECHAT_CREATE_ERROR", $"创建微信支付失败: {ex.Message}");
        }
    }

    public async Task<Result<PaymentQueryResult>> QueryPaymentAsync(string paymentNo, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Querying WeChat payment status for {PaymentNo}", paymentNo);

            var nonceStr = GenerateNonceStr();
            
            // Build query parameters
            var parameters = new SortedDictionary<string, string>
            {
                ["appid"] = _appId,
                ["mch_id"] = _mchId,
                ["out_trade_no"] = paymentNo,
                ["nonce_str"] = nonceStr
            };

            // Generate sign using signature service
            var sign = _signatureService.GenerateWeChatSignature(new Dictionary<string, string>(parameters), _apiKey);
            parameters["sign"] = sign;

            // Convert to XML
            var xmlData = ToXml(parameters);

            // Make API call
            var content = new StringContent(xmlData, Encoding.UTF8, "text/xml");
            var response = await _httpClient.PostAsync($"{_gateway}/pay/orderquery", content, cancellationToken);
            var responseXml = await response.Content.ReadAsStringAsync(cancellationToken);
            
            // Parse response
            var responseData = ParseXml(responseXml);
            
            if (responseData.GetValueOrDefault("return_code") != "SUCCESS")
            {
                var returnMsg = responseData.GetValueOrDefault("return_msg", "未知错误");
                return Result<PaymentQueryResult>.Failure("WECHAT_API_ERROR", $"查询失败: {returnMsg}");
            }

            if (responseData.GetValueOrDefault("result_code") != "SUCCESS")
            {
                // Check if trade doesn't exist
                var errCode = responseData.GetValueOrDefault("err_code", "");
                if (errCode == "ORDERNOTEXIST")
                {
                    var result = new PaymentQueryResult
                    {
                        PaymentNo = paymentNo,
                        Status = TransactionStatus.Pending
                    };
                    return Result<PaymentQueryResult>.Success(result);
                }
                
                var errDesc = responseData.GetValueOrDefault("err_code_des", "查询失败");
                return Result<PaymentQueryResult>.Failure(errCode, errDesc);
            }

            // Parse trade state
            var tradeState = responseData.GetValueOrDefault("trade_state", "");
            var status = tradeState switch
            {
                "SUCCESS" => TransactionStatus.Success,
                "REFUND" => TransactionStatus.Success, // Refunded payments were successful
                "NOTPAY" => TransactionStatus.Pending,
                "CLOSED" => TransactionStatus.Failed,
                "REVOKED" => TransactionStatus.Cancelled,
                "USERPAYING" => TransactionStatus.Pending,
                "PAYERROR" => TransactionStatus.Failed,
                _ => TransactionStatus.Pending
            };

            var queryResult = new PaymentQueryResult
            {
                PaymentNo = paymentNo,
                TransactionId = responseData.GetValueOrDefault("transaction_id"),
                Status = status,
                Amount = decimal.Parse(responseData.GetValueOrDefault("total_fee", "0")) / 100m,
                PaidAt = status == TransactionStatus.Success && responseData.TryGetValue("time_end", out var timeEnd) 
                    ? ParseWeChatTime(timeEnd) 
                    : null,
                BuyerId = responseData.GetValueOrDefault("openid"),
                BuyerAccount = responseData.GetValueOrDefault("openid")
            };

            return Result<PaymentQueryResult>.Success(queryResult);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to query WeChat payment {PaymentNo}", paymentNo);
            return Result<PaymentQueryResult>.Failure("WECHAT_QUERY_ERROR", $"查询支付状态失败: {ex.Message}");
        }
    }

    public async Task<Result<PaymentCallbackResult>> ProcessCallbackAsync(Dictionary<string, string> parameters, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Processing WeChat payment callback with {Count} parameters", parameters.Count);

            // WeChat sends XML data in body, retrieve it
            var xmlContent = parameters.GetValueOrDefault("xml_content", "");
            if (string.IsNullOrEmpty(xmlContent))
            {
                _logger.LogWarning("No XML content in WeChat callback");
                return Result<PaymentCallbackResult>.Failure("INVALID_DATA", "回调数据无效");
            }

            // Parse XML data
            var callbackData = ParseXml(xmlContent);
            
            // Verify signature using signature service
            var signature = callbackData.GetValueOrDefault("sign", "");
            if (!_signatureService.VerifyWeChatSignature(callbackData, signature, _apiKey))
            {
                _logger.LogWarning("Invalid WeChat callback signature");
                return Result<PaymentCallbackResult>.Failure("INVALID_SIGNATURE", "签名验证失败");
            }

            // Check return code
            var returnCode = callbackData.GetValueOrDefault("return_code", "");
            if (returnCode != "SUCCESS")
            {
                var returnMsg = callbackData.GetValueOrDefault("return_msg", "未知错误");
                return Result<PaymentCallbackResult>.Failure("RETURN_FAIL", returnMsg);
            }

            // Check result code
            var resultCode = callbackData.GetValueOrDefault("result_code", "");
            var outTradeNo = callbackData.GetValueOrDefault("out_trade_no", "");
            var transactionId = callbackData.GetValueOrDefault("transaction_id", "");
            var totalFee = decimal.Parse(callbackData.GetValueOrDefault("total_fee", "0")) / 100m;
            var timeEnd = callbackData.GetValueOrDefault("time_end", "");
            var openId = callbackData.GetValueOrDefault("openid", "");
            
            var status = resultCode == "SUCCESS" ? TransactionStatus.Success : TransactionStatus.Failed;
            var paidTime = status == TransactionStatus.Success && !string.IsNullOrEmpty(timeEnd) 
                ? ParseWeChatTime(timeEnd) 
                : (DateTime?)null;

            var result = new PaymentCallbackResult
            {
                PaymentNo = outTradeNo,
                TransactionId = transactionId,
                Status = status,
                Amount = totalFee,
                PaidAt = paidTime,
                ResponseMessage = "success", // WeChat expects plain text "success"
                NeedResponse = true,
                BuyerId = openId,
                BuyerAccount = openId
            };

            _logger.LogInformation("WeChat callback processed: PaymentNo={PaymentNo}, Status={Status}, TransactionId={TransactionId}", 
                outTradeNo, status, transactionId);

            return Result<PaymentCallbackResult>.Success(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to process WeChat callback");
            return Result<PaymentCallbackResult>.Failure("CALLBACK_ERROR", $"处理回调失败: {ex.Message}");
        }
    }

    public async Task<Result<RefundCreateResult>> RefundAsync(RefundCreateRequest request, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Creating WeChat refund for payment {PaymentNo}, amount: {Amount}", 
                request.PaymentNo, request.RefundAmount);

            // Load certificate for refund (required by WeChat)
            if (string.IsNullOrEmpty(_certificatePath) || !File.Exists(_certificatePath))
            {
                return Result<RefundCreateResult>.Failure("CERT_NOT_FOUND", "未配置退款证书");
            }

            var nonceStr = GenerateNonceStr();
            
            // Build refund parameters
            var parameters = new SortedDictionary<string, string>
            {
                ["appid"] = _appId,
                ["mch_id"] = _mchId,
                ["nonce_str"] = nonceStr,
                ["out_trade_no"] = request.PaymentNo,
                ["out_refund_no"] = request.RefundNo,
                ["total_fee"] = ((int)(request.TotalAmount * 100)).ToString(),
                ["refund_fee"] = ((int)(request.RefundAmount * 100)).ToString(),
                ["refund_desc"] = request.Reason,
                ["notify_url"] = request.NotifyUrl ?? _notifyUrl.Replace("/callback/", "/refund-callback/")
            };

            if (!string.IsNullOrEmpty(request.TransactionId))
            {
                parameters["transaction_id"] = request.TransactionId;
            }

            // Generate sign using signature service
            var sign = _signatureService.GenerateWeChatSignature(new Dictionary<string, string>(parameters), _apiKey);
            parameters["sign"] = sign;

            // Convert to XML
            var xmlData = ToXml(parameters);

            // Create HTTPS client with certificate
            var handler = new HttpClientHandler();
            var cert = new X509Certificate2(_certificatePath, _certificatePassword ?? _mchId);
            handler.ClientCertificates.Add(cert);
            
            using var certClient = new HttpClient(handler);
            var content = new StringContent(xmlData, Encoding.UTF8, "text/xml");
            var response = await certClient.PostAsync($"{_gateway}/secapi/pay/refund", content, cancellationToken);
            var responseXml = await response.Content.ReadAsStringAsync(cancellationToken);
            
            // Parse response
            var responseData = ParseXml(responseXml);
            
            if (responseData.GetValueOrDefault("return_code") != "SUCCESS")
            {
                var returnMsg = responseData.GetValueOrDefault("return_msg", "未知错误");
                return Result<RefundCreateResult>.Failure("WECHAT_API_ERROR", $"退款接口错误: {returnMsg}");
            }

            if (responseData.GetValueOrDefault("result_code") != "SUCCESS")
            {
                var errCode = responseData.GetValueOrDefault("err_code", "UNKNOWN");
                var errDesc = responseData.GetValueOrDefault("err_code_des", "退款失败");
                return Result<RefundCreateResult>.Failure(errCode, errDesc);
            }

            var result = new RefundCreateResult
            {
                RefundNo = request.RefundNo,
                RefundTransactionId = responseData.GetValueOrDefault("refund_id"),
                Status = RefundStatus.Processing, // WeChat refunds need time to process
                RefundAmount = request.RefundAmount,
                RefundedAt = null
            };

            _logger.LogInformation("WeChat refund created successfully: {RefundNo}", request.RefundNo);
            return Result<RefundCreateResult>.Success(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to create WeChat refund for payment {PaymentNo}", request.PaymentNo);
            return Result<RefundCreateResult>.Failure("REFUND_ERROR", $"退款失败: {ex.Message}");
        }
    }

    public async Task<Result<RefundQueryResult>> QueryRefundAsync(string refundNo, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Querying WeChat refund status for {RefundNo}", refundNo);

            var nonceStr = GenerateNonceStr();
            
            // Build query parameters
            var parameters = new SortedDictionary<string, string>
            {
                ["appid"] = _appId,
                ["mch_id"] = _mchId,
                ["nonce_str"] = nonceStr,
                ["out_refund_no"] = refundNo
            };

            // Generate sign using signature service
            var sign = _signatureService.GenerateWeChatSignature(new Dictionary<string, string>(parameters), _apiKey);
            parameters["sign"] = sign;

            // Convert to XML
            var xmlData = ToXml(parameters);

            // Make API call
            var content = new StringContent(xmlData, Encoding.UTF8, "text/xml");
            var response = await _httpClient.PostAsync($"{_gateway}/pay/refundquery", content, cancellationToken);
            var responseXml = await response.Content.ReadAsStringAsync(cancellationToken);
            
            // Parse response
            var responseData = ParseXml(responseXml);
            
            if (responseData.GetValueOrDefault("return_code") != "SUCCESS")
            {
                var returnMsg = responseData.GetValueOrDefault("return_msg", "未知错误");
                return Result<RefundQueryResult>.Failure("WECHAT_API_ERROR", $"查询失败: {returnMsg}");
            }

            if (responseData.GetValueOrDefault("result_code") != "SUCCESS")
            {
                var errCode = responseData.GetValueOrDefault("err_code", "UNKNOWN");
                var errDesc = responseData.GetValueOrDefault("err_code_des", "查询失败");
                return Result<RefundQueryResult>.Failure(errCode, errDesc);
            }

            // Parse refund status (WeChat may have multiple refunds, we take the first one)
            var refundStatus = responseData.GetValueOrDefault("refund_status_0", "");
            var status = refundStatus switch
            {
                "SUCCESS" => RefundStatus.Completed,
                "PROCESSING" => RefundStatus.Processing,
                "REFUNDCLOSE" => RefundStatus.Failed,
                "CHANGE" => RefundStatus.Failed,
                _ => RefundStatus.Processing
            };

            var result = new RefundQueryResult
            {
                RefundNo = refundNo,
                RefundTransactionId = responseData.GetValueOrDefault("refund_id_0"),
                Status = status,
                RefundAmount = decimal.Parse(responseData.GetValueOrDefault("refund_fee_0", "0")) / 100m,
                RefundedAt = status == RefundStatus.Completed && responseData.TryGetValue("refund_success_time_0", out var successTime)
                    ? DateTime.Parse(successTime)
                    : null,
                PaymentNo = responseData.GetValueOrDefault("out_trade_no"),
                PaymentTransactionId = responseData.GetValueOrDefault("transaction_id"),
                TotalAmount = decimal.Parse(responseData.GetValueOrDefault("total_fee", "0")) / 100m
            };

            return Result<RefundQueryResult>.Success(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to query WeChat refund {RefundNo}", refundNo);
            return Result<RefundQueryResult>.Failure("REFUND_QUERY_ERROR", $"查询退款状态失败: {ex.Message}");
        }
    }

    public async Task<Result> ClosePaymentAsync(string paymentNo, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Closing WeChat payment {PaymentNo}", paymentNo);

            var nonceStr = GenerateNonceStr();
            
            // Build close parameters
            var parameters = new SortedDictionary<string, string>
            {
                ["appid"] = _appId,
                ["mch_id"] = _mchId,
                ["out_trade_no"] = paymentNo,
                ["nonce_str"] = nonceStr
            };

            // Generate sign using signature service
            var sign = _signatureService.GenerateWeChatSignature(new Dictionary<string, string>(parameters), _apiKey);
            parameters["sign"] = sign;

            // Convert to XML
            var xmlData = ToXml(parameters);

            // Make API call
            var content = new StringContent(xmlData, Encoding.UTF8, "text/xml");
            var response = await _httpClient.PostAsync($"{_gateway}/pay/closeorder", content, cancellationToken);
            var responseXml = await response.Content.ReadAsStringAsync(cancellationToken);
            
            // Parse response
            var responseData = ParseXml(responseXml);
            
            if (responseData.GetValueOrDefault("return_code") != "SUCCESS")
            {
                var returnMsg = responseData.GetValueOrDefault("return_msg", "未知错误");
                return Result.Failure("WECHAT_API_ERROR", $"关闭失败: {returnMsg}");
            }

            if (responseData.GetValueOrDefault("result_code") != "SUCCESS")
            {
                var errCode = responseData.GetValueOrDefault("err_code", "");
                
                // These are not real failures
                if (errCode == "ORDERPAID" || errCode == "ORDERCLOSED")
                {
                    return Result.Success();
                }
                
                var errDesc = responseData.GetValueOrDefault("err_code_des", "关闭失败");
                return Result.Failure(errCode, errDesc);
            }

            _logger.LogInformation("WeChat payment {PaymentNo} closed successfully", paymentNo);
            return Result.Success();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to close WeChat payment {PaymentNo}", paymentNo);
            return Result.Failure("CLOSE_ERROR", $"关闭交易失败: {ex.Message}");
        }
    }

    private string GenerateNonceStr()
    {
        return Guid.NewGuid().ToString("N");
    }


    /// <summary>
    /// 将字典转换为XML
    /// </summary>
    private string ToXml(SortedDictionary<string, string> parameters)
    {
        var xml = new StringBuilder("<xml>");
        foreach (var kvp in parameters)
        {
            if (!string.IsNullOrEmpty(kvp.Value))
            {
                xml.AppendFormat("<{0}><![CDATA[{1}]]></{0}>", kvp.Key, kvp.Value);
            }
        }
        xml.Append("</xml>");
        return xml.ToString();
    }

    /// <summary>
    /// 解析XML为字典
    /// </summary>
    private Dictionary<string, string> ParseXml(string xml)
    {
        var result = new Dictionary<string, string>();
        
        try
        {
            var doc = XDocument.Parse(xml);
            var root = doc.Root;
            
            if (root != null)
            {
                foreach (var element in root.Elements())
                {
                    result[element.Name.LocalName] = element.Value;
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to parse XML: {Xml}", xml);
        }
        
        return result;
    }

    private DateTime ParseWeChatTime(string timeStr)
    {
        // WeChat time format: yyyyMMddHHmmss
        if (string.IsNullOrEmpty(timeStr) || timeStr.Length != 14)
        {
            return DateTime.UtcNow;
        }

        return DateTime.ParseExact(timeStr, "yyyyMMddHHmmss", null);
    }

}