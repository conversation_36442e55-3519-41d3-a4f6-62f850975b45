using System.Security.Cryptography;
using System.Text;
using System.Text.Json;
using System.Web;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using WhimLabAI.Abstractions.Infrastructure;
using WhimLabAI.Shared.Enums;
using WhimLabAI.Shared.Results;

namespace WhimLabAI.Infrastructure.PaymentGateways;

/// <summary>
/// 支付宝支付网关真实实现
/// 基于支付宝开放平台API v2.0
/// </summary>
public class AlipayGateway : IPaymentGateway
{
    private readonly IConfiguration _configuration;
    private readonly ILogger<AlipayGateway> _logger;
    private readonly HttpClient _httpClient;
    private readonly IPaymentSignatureService _signatureService;
    private readonly ISecretManagementService _secretManagementService;
    private readonly string _appId;
    private readonly string _privateKey;
    private readonly string _publicKey;
    private readonly string _gateway;
    private readonly bool _isProduction;
    private readonly string _notifyUrl;
    private readonly string _returnUrl;
    private readonly string _signType = "RSA2";
    private readonly string _charset = "utf-8";
    private readonly string _version = "1.0";
    private readonly string _format = "json";

    public PaymentMethod PaymentMethod => PaymentMethod.Alipay;
    public bool SupportsRefund => true;

    public AlipayGateway(
        IConfiguration configuration, 
        ILogger<AlipayGateway> logger,
        IHttpClientFactory httpClientFactory,
        IPaymentSignatureService signatureService,
        ISecretManagementService secretManagementService)
    {
        _configuration = configuration;
        _logger = logger;
        _httpClient = httpClientFactory.CreateClient();
        _signatureService = signatureService;
        _secretManagementService = secretManagementService;
        
        // Load configuration
        var alipayConfig = configuration.GetSection("Payment:Alipay");
        _appId = alipayConfig["AppId"] ?? throw new InvalidOperationException("Alipay AppId not configured");
        
        // Load keys from secret management service
        _privateKey = _secretManagementService.GetSecretAsync("ALIPAY_PRIVATE_KEY").GetAwaiter().GetResult() 
            ?? alipayConfig["PrivateKey"] 
            ?? throw new InvalidOperationException("Alipay PrivateKey not configured");
        _publicKey = _secretManagementService.GetSecretAsync("ALIPAY_PUBLIC_KEY").GetAwaiter().GetResult() 
            ?? alipayConfig["PublicKey"] 
            ?? throw new InvalidOperationException("Alipay PublicKey not configured");
            
        _isProduction = alipayConfig.GetValue<bool>("IsProduction");
        _gateway = alipayConfig["Gateway"] ?? (_isProduction 
            ? "https://openapi.alipay.com/gateway.do" 
            : "https://openapi.alipaydev.com/gateway.do");
            
        _notifyUrl = alipayConfig["NotifyUrl"] ?? string.Empty;
        _returnUrl = alipayConfig["ReturnUrl"] ?? string.Empty;
    }

    public async Task<Result<PaymentCreateResult>> CreatePaymentAsync(PaymentCreateRequest request, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Creating Alipay payment for order {OrderNo}, amount: {Amount}", 
                request.OrderNo, request.Amount);

            var timestamp = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss");
            
            // Build business content
            var bizContent = new Dictionary<string, object>
            {
                ["out_trade_no"] = request.PaymentNo,
                ["product_code"] = "FAST_INSTANT_TRADE_PAY",
                ["total_amount"] = request.Amount.ToString("F2"),
                ["subject"] = request.Subject,
                ["body"] = request.Description ?? "",
                ["timeout_express"] = "30m"
            };

            // Build all parameters
            var parameters = new Dictionary<string, string>
            {
                ["app_id"] = _appId,
                ["method"] = "alipay.trade.page.pay",
                ["charset"] = _charset,
                ["sign_type"] = _signType,
                ["timestamp"] = timestamp,
                ["version"] = _version,
                ["notify_url"] = request.NotifyUrl ?? _notifyUrl,
                ["return_url"] = request.ReturnUrl ?? _returnUrl,
                ["biz_content"] = JsonSerializer.Serialize(bizContent)
            };

            // Generate signature using signature service
            var sign = _signatureService.GenerateAlipaySignature(parameters, _privateKey);
            parameters["sign"] = sign;

            // Build form HTML
            var formHtml = BuildPaymentForm(parameters);
            
            var result = new PaymentCreateResult
            {
                PaymentNo = request.PaymentNo,
                TransactionId = null,
                PaymentUrl = formHtml,
                Type = PaymentCreateType.Redirect
            };

            _logger.LogInformation("Alipay payment form created successfully for order {OrderNo}", request.OrderNo);
            return Result<PaymentCreateResult>.Success(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to create Alipay payment for order {OrderNo}", request.OrderNo);
            return Result<PaymentCreateResult>.Failure("ALIPAY_CREATE_ERROR", $"创建支付宝支付失败: {ex.Message}");
        }
    }

    public async Task<Result<PaymentQueryResult>> QueryPaymentAsync(string paymentNo, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Querying Alipay payment status for {PaymentNo}", paymentNo);

            var timestamp = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss");
            
            var bizContent = new Dictionary<string, string>
            {
                ["out_trade_no"] = paymentNo
            };

            var parameters = new Dictionary<string, string>
            {
                ["app_id"] = _appId,
                ["method"] = "alipay.trade.query",
                ["charset"] = _charset,
                ["sign_type"] = _signType,
                ["timestamp"] = timestamp,
                ["version"] = _version,
                ["format"] = _format,
                ["biz_content"] = JsonSerializer.Serialize(bizContent)
            };

            var sign = _signatureService.GenerateAlipaySignature(parameters, _privateKey);
            parameters["sign"] = sign;

            // Make API call
            var response = await _httpClient.PostAsync(_gateway, new FormUrlEncodedContent(parameters), cancellationToken);
            var responseContent = await response.Content.ReadAsStringAsync(cancellationToken);
            
            var jsonDoc = JsonDocument.Parse(responseContent);
            var root = jsonDoc.RootElement;
            
            if (root.TryGetProperty("alipay_trade_query_response", out var queryResponse))
            {
                var code = queryResponse.GetProperty("code").GetString();
                
                if (code == "10000") // Success
                {
                    var tradeStatus = queryResponse.GetProperty("trade_status").GetString();
                    var status = tradeStatus switch
                    {
                        "TRADE_SUCCESS" => TransactionStatus.Success,
                        "TRADE_FINISHED" => TransactionStatus.Success,
                        "TRADE_CLOSED" => TransactionStatus.Failed,
                        "WAIT_BUYER_PAY" => TransactionStatus.Pending,
                        _ => TransactionStatus.Pending
                    };

                    var result = new PaymentQueryResult
                    {
                        PaymentNo = paymentNo,
                        TransactionId = queryResponse.TryGetProperty("trade_no", out var tradeNo) ? tradeNo.GetString() : null,
                        Status = status,
                        Amount = queryResponse.TryGetProperty("total_amount", out var amount) ? decimal.Parse(amount.GetString() ?? "0") : 0,
                        PaidAt = status == TransactionStatus.Success && queryResponse.TryGetProperty("send_pay_date", out var payDate) 
                            ? DateTime.Parse(payDate.GetString() ?? DateTime.Now.ToString()) 
                            : null,
                        BuyerId = queryResponse.TryGetProperty("buyer_user_id", out var buyerId) ? buyerId.GetString() : null,
                        BuyerAccount = queryResponse.TryGetProperty("buyer_logon_id", out var buyerAccount) ? buyerAccount.GetString() : null
                    };

                    return Result<PaymentQueryResult>.Success(result);
                }
                else if (code == "40004" && queryResponse.TryGetProperty("sub_code", out var subCode) && subCode.GetString() == "ACQ.TRADE_NOT_EXIST")
                {
                    // Trade doesn't exist yet, might be pending
                    var result = new PaymentQueryResult
                    {
                        PaymentNo = paymentNo,
                        Status = TransactionStatus.Pending
                    };
                    return Result<PaymentQueryResult>.Success(result);
                }
                else
                {
                    var msg = queryResponse.TryGetProperty("msg", out var msgProp) ? msgProp.GetString() : "Unknown error";
                    return Result<PaymentQueryResult>.Failure(code ?? "UNKNOWN", msg ?? "查询失败");
                }
            }

            return Result<PaymentQueryResult>.Failure("INVALID_RESPONSE", "支付宝返回格式错误");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to query Alipay payment {PaymentNo}", paymentNo);
            return Result<PaymentQueryResult>.Failure("ALIPAY_QUERY_ERROR", $"查询支付状态失败: {ex.Message}");
        }
    }

    public async Task<Result<PaymentCallbackResult>> ProcessCallbackAsync(Dictionary<string, string> parameters, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Processing Alipay callback with {Count} parameters", parameters.Count);

            // Verify signature using signature service
            var signature = parameters.GetValueOrDefault("sign", "");
            if (!_signatureService.VerifyAlipaySignature(parameters, signature, _publicKey))
            {
                _logger.LogWarning("Invalid Alipay callback signature");
                return Result<PaymentCallbackResult>.Failure("INVALID_SIGNATURE", "签名验证失败");
            }

            // Extract payment info
            var tradeStatus = parameters.GetValueOrDefault("trade_status", "");
            var outTradeNo = parameters.GetValueOrDefault("out_trade_no", "");
            var tradeNo = parameters.GetValueOrDefault("trade_no", "");
            var totalAmount = decimal.Parse(parameters.GetValueOrDefault("total_amount", "0"));
            var gmtPayment = parameters.GetValueOrDefault("gmt_payment", "");
            var buyerId = parameters.GetValueOrDefault("buyer_id", "");
            var buyerLogonId = parameters.GetValueOrDefault("buyer_logon_id", "");
            
            var paidTime = !string.IsNullOrEmpty(gmtPayment) 
                ? DateTime.Parse(gmtPayment).ToUniversalTime() 
                : DateTime.UtcNow;

            var transactionStatus = tradeStatus switch
            {
                "TRADE_SUCCESS" => TransactionStatus.Success,
                "TRADE_FINISHED" => TransactionStatus.Success,
                "TRADE_CLOSED" => TransactionStatus.Failed,
                "WAIT_BUYER_PAY" => TransactionStatus.Pending,
                _ => TransactionStatus.Pending
            };

            var result = new PaymentCallbackResult
            {
                PaymentNo = outTradeNo,
                TransactionId = tradeNo,
                Status = transactionStatus,
                Amount = totalAmount,
                PaidAt = transactionStatus == TransactionStatus.Success ? paidTime : null,
                ResponseMessage = "success",
                NeedResponse = true,
                BuyerId = buyerId,
                BuyerAccount = buyerLogonId
            };

            _logger.LogInformation("Alipay callback processed: PaymentNo={PaymentNo}, Status={Status}, TradeNo={TradeNo}", 
                outTradeNo, transactionStatus, tradeNo);

            return Result<PaymentCallbackResult>.Success(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to process Alipay callback");
            return Result<PaymentCallbackResult>.Failure("CALLBACK_ERROR", $"处理回调失败: {ex.Message}");
        }
    }

    public async Task<Result<RefundCreateResult>> RefundAsync(RefundCreateRequest request, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Creating Alipay refund for payment {PaymentNo}, amount: {Amount}", 
                request.PaymentNo, request.RefundAmount);

            var timestamp = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss");
            
            var bizContent = new Dictionary<string, object>
            {
                ["out_trade_no"] = request.PaymentNo,
                ["refund_amount"] = request.RefundAmount.ToString("F2"),
                ["refund_reason"] = request.Reason,
                ["out_request_no"] = request.RefundNo
            };

            if (!string.IsNullOrEmpty(request.TransactionId))
            {
                bizContent["trade_no"] = request.TransactionId;
            }

            var parameters = new Dictionary<string, string>
            {
                ["app_id"] = _appId,
                ["method"] = "alipay.trade.refund",
                ["charset"] = _charset,
                ["sign_type"] = _signType,
                ["timestamp"] = timestamp,
                ["version"] = _version,
                ["format"] = _format,
                ["biz_content"] = JsonSerializer.Serialize(bizContent)
            };

            var sign = _signatureService.GenerateAlipaySignature(parameters, _privateKey);
            parameters["sign"] = sign;

            var response = await _httpClient.PostAsync(_gateway, new FormUrlEncodedContent(parameters), cancellationToken);
            var responseContent = await response.Content.ReadAsStringAsync(cancellationToken);
            
            var jsonDoc = JsonDocument.Parse(responseContent);
            var root = jsonDoc.RootElement;
            
            if (root.TryGetProperty("alipay_trade_refund_response", out var refundResponse))
            {
                var code = refundResponse.GetProperty("code").GetString();
                
                if (code == "10000" && refundResponse.TryGetProperty("fund_change", out var fundChange) && fundChange.GetString() == "Y")
                {
                    var result = new RefundCreateResult
                    {
                        RefundNo = request.RefundNo,
                        RefundTransactionId = refundResponse.TryGetProperty("trade_no", out var tradeNo) ? tradeNo.GetString() : null,
                        Status = RefundStatus.Completed,
                        RefundAmount = decimal.Parse(refundResponse.TryGetProperty("refund_fee", out var fee) ? fee.GetString() ?? request.RefundAmount.ToString() : request.RefundAmount.ToString()),
                        RefundedAt = DateTime.UtcNow
                    };

                    _logger.LogInformation("Alipay refund created successfully: {RefundNo}", request.RefundNo);
                    return Result<RefundCreateResult>.Success(result);
                }
                else
                {
                    var msg = refundResponse.TryGetProperty("msg", out var msgProp) ? msgProp.GetString() : "退款失败";
                    var result = new RefundCreateResult
                    {
                        RefundNo = request.RefundNo,
                        Status = RefundStatus.Failed,
                        RefundAmount = request.RefundAmount,
                        FailReason = msg
                    };
                    
                    return Result<RefundCreateResult>.Failure(code ?? "REFUND_FAILED", msg ?? "退款失败");
                }
            }

            return Result<RefundCreateResult>.Failure("INVALID_RESPONSE", "支付宝返回格式错误");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to create Alipay refund for payment {PaymentNo}", request.PaymentNo);
            return Result<RefundCreateResult>.Failure("REFUND_ERROR", $"退款失败: {ex.Message}");
        }
    }

    public async Task<Result<RefundQueryResult>> QueryRefundAsync(string refundNo, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Querying Alipay refund status for {RefundNo}", refundNo);

            var timestamp = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss");
            
            var bizContent = new Dictionary<string, string>
            {
                ["out_request_no"] = refundNo
            };

            var parameters = new Dictionary<string, string>
            {
                ["app_id"] = _appId,
                ["method"] = "alipay.trade.fastpay.refund.query",
                ["charset"] = _charset,
                ["sign_type"] = _signType,
                ["timestamp"] = timestamp,
                ["version"] = _version,
                ["format"] = _format,
                ["biz_content"] = JsonSerializer.Serialize(bizContent)
            };

            var sign = _signatureService.GenerateAlipaySignature(parameters, _privateKey);
            parameters["sign"] = sign;

            var response = await _httpClient.PostAsync(_gateway, new FormUrlEncodedContent(parameters), cancellationToken);
            var responseContent = await response.Content.ReadAsStringAsync(cancellationToken);
            
            var jsonDoc = JsonDocument.Parse(responseContent);
            var root = jsonDoc.RootElement;
            
            if (root.TryGetProperty("alipay_trade_fastpay_refund_query_response", out var queryResponse))
            {
                var code = queryResponse.GetProperty("code").GetString();
                
                if (code == "10000")
                {
                    var refundStatus = queryResponse.TryGetProperty("refund_status", out var status) ? status.GetString() : "PROCESSING";
                    
                    var result = new RefundQueryResult
                    {
                        RefundNo = refundNo,
                        RefundTransactionId = queryResponse.TryGetProperty("trade_no", out var tradeNo) ? tradeNo.GetString() : null,
                        Status = refundStatus == "REFUND_SUCCESS" ? RefundStatus.Completed : RefundStatus.Processing,
                        RefundAmount = decimal.Parse(queryResponse.TryGetProperty("refund_amount", out var amount) ? amount.GetString() ?? "0" : "0"),
                        RefundedAt = refundStatus == "REFUND_SUCCESS" ? DateTime.UtcNow : null,
                        PaymentNo = queryResponse.TryGetProperty("out_trade_no", out var outTradeNo) ? outTradeNo.GetString() : null,
                        PaymentTransactionId = tradeNo.GetString(),
                        TotalAmount = decimal.Parse(queryResponse.TryGetProperty("total_amount", out var total) ? total.GetString() ?? "0" : "0")
                    };

                    return Result<RefundQueryResult>.Success(result);
                }
                else
                {
                    var msg = queryResponse.TryGetProperty("msg", out var msgProp) ? msgProp.GetString() : "查询失败";
                    return Result<RefundQueryResult>.Failure(code ?? "QUERY_FAILED", msg ?? "查询失败");
                }
            }

            return Result<RefundQueryResult>.Failure("INVALID_RESPONSE", "支付宝返回格式错误");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to query Alipay refund {RefundNo}", refundNo);
            return Result<RefundQueryResult>.Failure("REFUND_QUERY_ERROR", $"查询退款状态失败: {ex.Message}");
        }
    }

    public async Task<Result> ClosePaymentAsync(string paymentNo, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Closing Alipay payment {PaymentNo}", paymentNo);

            var timestamp = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss");
            
            var bizContent = new Dictionary<string, string>
            {
                ["out_trade_no"] = paymentNo
            };

            var parameters = new Dictionary<string, string>
            {
                ["app_id"] = _appId,
                ["method"] = "alipay.trade.close",
                ["charset"] = _charset,
                ["sign_type"] = _signType,
                ["timestamp"] = timestamp,
                ["version"] = _version,
                ["format"] = _format,
                ["biz_content"] = JsonSerializer.Serialize(bizContent)
            };

            var sign = _signatureService.GenerateAlipaySignature(parameters, _privateKey);
            parameters["sign"] = sign;

            var response = await _httpClient.PostAsync(_gateway, new FormUrlEncodedContent(parameters), cancellationToken);
            var responseContent = await response.Content.ReadAsStringAsync(cancellationToken);
            
            var jsonDoc = JsonDocument.Parse(responseContent);
            var root = jsonDoc.RootElement;
            
            if (root.TryGetProperty("alipay_trade_close_response", out var closeResponse))
            {
                var code = closeResponse.GetProperty("code").GetString();
                
                if (code == "10000")
                {
                    _logger.LogInformation("Alipay payment {PaymentNo} closed successfully", paymentNo);
                    return Result.Success();
                }
                else
                {
                    var subCode = closeResponse.TryGetProperty("sub_code", out var sub) ? sub.GetString() : "";
                    
                    // These are not real failures
                    if (subCode == "ACQ.TRADE_NOT_EXIST" || subCode == "ACQ.TRADE_HAS_CLOSE")
                    {
                        return Result.Success();
                    }
                    
                    var msg = closeResponse.TryGetProperty("msg", out var msgProp) ? msgProp.GetString() : "关闭失败";
                    return Result.Failure(code ?? "CLOSE_FAILED", msg ?? "关闭失败");
                }
            }

            return Result.Failure("INVALID_RESPONSE", "支付宝返回格式错误");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to close Alipay payment {PaymentNo}", paymentNo);
            return Result.Failure("CLOSE_ERROR", $"关闭交易失败: {ex.Message}");
        }
    }


    /// <summary>
    /// 构建支付表单
    /// </summary>
    private string BuildPaymentForm(Dictionary<string, string> parameters)
    {
        var formId = $"alipay_form_{Guid.NewGuid():N}";
        var formBuilder = new StringBuilder();
        
        formBuilder.AppendLine($"<form id='{formId}' name='alipaysubmit' action='{_gateway}' method='post'>");
        
        foreach (var param in parameters)
        {
            var value = HttpUtility.HtmlEncode(param.Value);
            formBuilder.AppendLine($"    <input type='hidden' name='{param.Key}' value='{value}'/>");
        }
        
        formBuilder.AppendLine("</form>");
        formBuilder.AppendLine($"<script>document.getElementById('{formId}').submit();</script>");
        
        return formBuilder.ToString();
    }
}