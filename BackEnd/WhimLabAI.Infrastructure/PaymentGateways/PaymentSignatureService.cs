using System;
using System.Collections.Generic;
using System.Linq;
using System.Security.Cryptography;
using System.Text;
using Microsoft.Extensions.Logging;
using WhimLabAI.Abstractions.Infrastructure;

namespace WhimLabAI.Infrastructure.PaymentGateways
{
    /// <summary>
    /// Implementation of payment signature service for secure payment data handling
    /// </summary>
    public class PaymentSignatureService : IPaymentSignatureService
    {
        private readonly ILogger<PaymentSignatureService> _logger;

        public PaymentSignatureService(ILogger<PaymentSignatureService> logger)
        {
            _logger = logger;
        }

        public string GenerateSignature(Dictionary<string, string> paymentData, string secretKey)
        {
            try
            {
                // Sort parameters by key
                var sortedParams = paymentData
                    .Where(x => !string.IsNullOrEmpty(x.Value) && x.Key != "sign" && x.Key != "sign_type")
                    .OrderBy(x => x.Key)
                    .Select(x => $"{x.Key}={x.Value}")
                    .ToList();

                var signString = string.Join("&", sortedParams);
                return ComputeHmacSha256(signString, secretKey);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error generating signature");
                throw;
            }
        }

        public bool VerifySignature(Dictionary<string, string> paymentData, string signature, string secretKey)
        {
            try
            {
                var computedSignature = GenerateSignature(paymentData, secretKey);
                return string.Equals(computedSignature, signature, StringComparison.OrdinalIgnoreCase);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error verifying signature");
                return false;
            }
        }

        public string GenerateAlipaySignature(Dictionary<string, string> parameters, string privateKey)
        {
            try
            {
                // Sort and concatenate parameters
                var sortedParams = parameters
                    .Where(x => !string.IsNullOrEmpty(x.Value) && x.Key != "sign" && x.Key != "sign_type")
                    .OrderBy(x => x.Key)
                    .Select(x => $"{x.Key}={x.Value}")
                    .ToList();

                var signContent = string.Join("&", sortedParams);

                // Default to RSA2 (RSA256) for Alipay
                return SignWithRSA256(signContent, privateKey);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error generating Alipay signature");
                throw;
            }
        }

        public bool VerifyAlipaySignature(Dictionary<string, string> parameters, string signature, string publicKey)
        {
            try
            {
                // Sort and concatenate parameters
                var sortedParams = parameters
                    .Where(x => !string.IsNullOrEmpty(x.Value) && x.Key != "sign" && x.Key != "sign_type")
                    .OrderBy(x => x.Key)
                    .Select(x => $"{x.Key}={x.Value}")
                    .ToList();

                var signContent = string.Join("&", sortedParams);

                // Default to RSA2 (RSA256) for Alipay
                return VerifyWithRSA256(signContent, signature, publicKey);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error verifying Alipay signature");
                return false;
            }
        }

        public string GenerateWeChatSignature(Dictionary<string, string> parameters, string apiKey)
        {
            try
            {
                // Sort and concatenate parameters
                var sortedParams = parameters
                    .Where(x => !string.IsNullOrEmpty(x.Value) && x.Key != "sign")
                    .OrderBy(x => x.Key)
                    .Select(x => $"{x.Key}={x.Value}")
                    .ToList();

                var stringA = string.Join("&", sortedParams);
                var stringSignTemp = $"{stringA}&key={apiKey}";

                // WeChat uses MD5 for signature
                return ComputeMD5Hash(stringSignTemp).ToUpper();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error generating WeChat signature");
                throw;
            }
        }

        public bool VerifyWeChatSignature(Dictionary<string, string> parameters, string signature, string apiKey)
        {
            try
            {
                var computedSignature = GenerateWeChatSignature(parameters, apiKey);
                return string.Equals(computedSignature, signature, StringComparison.OrdinalIgnoreCase);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error verifying WeChat signature");
                return false;
            }
        }

        public long GenerateTimestamp()
        {
            return DateTimeOffset.UtcNow.ToUnixTimeSeconds();
        }

        public string GenerateNonce(int length = 32)
        {
            const string chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789";
            var random = new Random();
            var nonce = new char[length];
            
            for (int i = 0; i < length; i++)
            {
                nonce[i] = chars[random.Next(chars.Length)];
            }
            
            return new string(nonce);
        }

        public bool IsTimestampValid(long timestamp, int maxAgeSeconds = 300)
        {
            var currentTimestamp = GenerateTimestamp();
            var age = Math.Abs(currentTimestamp - timestamp);
            return age <= maxAgeSeconds;
        }

        public IPaymentSignatureAlgorithm GetAlgorithm(string gateway)
        {
            // For now, return a default implementation
            // In a full implementation, this would return gateway-specific algorithms
            return new DefaultSignatureAlgorithm();
        }

        private string ComputeHmacSha256(string data, string key)
        {
            var keyBytes = Encoding.UTF8.GetBytes(key);
            var dataBytes = Encoding.UTF8.GetBytes(data);

            using (var hmac = new HMACSHA256(keyBytes))
            {
                var hash = hmac.ComputeHash(dataBytes);
                return Convert.ToBase64String(hash);
            }
        }

        private string ComputeMD5Hash(string input)
        {
            using (var md5 = MD5.Create())
            {
                var inputBytes = Encoding.UTF8.GetBytes(input);
                var hashBytes = md5.ComputeHash(inputBytes);
                return BitConverter.ToString(hashBytes).Replace("-", "").ToLower();
            }
        }

        private string SignWithRSA256(string data, string privateKey)
        {
            var privateKeyBytes = Convert.FromBase64String(privateKey);
            using (var rsa = RSA.Create())
            {
                rsa.ImportPkcs8PrivateKey(privateKeyBytes, out _);
                var dataBytes = Encoding.UTF8.GetBytes(data);
                var signature = rsa.SignData(dataBytes, HashAlgorithmName.SHA256, RSASignaturePadding.Pkcs1);
                return Convert.ToBase64String(signature);
            }
        }

        private string SignWithRSA(string data, string privateKey)
        {
            var privateKeyBytes = Convert.FromBase64String(privateKey);
            using (var rsa = RSA.Create())
            {
                rsa.ImportPkcs8PrivateKey(privateKeyBytes, out _);
                var dataBytes = Encoding.UTF8.GetBytes(data);
                var signature = rsa.SignData(dataBytes, HashAlgorithmName.SHA1, RSASignaturePadding.Pkcs1);
                return Convert.ToBase64String(signature);
            }
        }

        private bool VerifyWithRSA256(string data, string signature, string publicKey)
        {
            try
            {
                var publicKeyBytes = Convert.FromBase64String(publicKey);
                var signatureBytes = Convert.FromBase64String(signature);
                using (var rsa = RSA.Create())
                {
                    rsa.ImportSubjectPublicKeyInfo(publicKeyBytes, out _);
                    var dataBytes = Encoding.UTF8.GetBytes(data);
                    return rsa.VerifyData(dataBytes, signatureBytes, HashAlgorithmName.SHA256, RSASignaturePadding.Pkcs1);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in RSA256 verification");
                return false;
            }
        }

        private bool VerifyWithRSA(string data, string signature, string publicKey)
        {
            try
            {
                var publicKeyBytes = Convert.FromBase64String(publicKey);
                var signatureBytes = Convert.FromBase64String(signature);
                using (var rsa = RSA.Create())
                {
                    rsa.ImportSubjectPublicKeyInfo(publicKeyBytes, out _);
                    var dataBytes = Encoding.UTF8.GetBytes(data);
                    return rsa.VerifyData(dataBytes, signatureBytes, HashAlgorithmName.SHA1, RSASignaturePadding.Pkcs1);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in RSA verification");
                return false;
            }
        }
    }

    /// <summary>
    /// Default signature algorithm implementation
    /// </summary>
    internal class DefaultSignatureAlgorithm : IPaymentSignatureAlgorithm
    {
        public string AlgorithmName => "HMAC-SHA256";

        public string Sign(string data, string secretKey)
        {
            using var hmac = new HMACSHA256(Encoding.UTF8.GetBytes(secretKey));
            var hash = hmac.ComputeHash(Encoding.UTF8.GetBytes(data));
            return Convert.ToBase64String(hash);
        }

        public string BuildSignString(Dictionary<string, string> parameters)
        {
            var sortedParams = new SortedDictionary<string, string>(parameters);
            var parts = new List<string>();
            
            foreach (var kvp in sortedParams)
            {
                if (!string.IsNullOrEmpty(kvp.Value) && kvp.Key != "sign" && kvp.Key != "sign_type")
                {
                    parts.Add($"{kvp.Key}={kvp.Value}");
                }
            }
            
            return string.Join("&", parts);
        }
    }
}