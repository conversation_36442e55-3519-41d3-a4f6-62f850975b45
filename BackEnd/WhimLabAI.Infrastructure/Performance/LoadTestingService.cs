using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Net.Http;
using System.Text;
using System.Text.Json;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using WhimLabAI.Abstractions.Application;
using WhimLabAI.Abstractions.Infrastructure;
using WhimLabAI.Shared.Results;

namespace WhimLabAI.Infrastructure.Performance;

/// <summary>
/// 负载测试服务实现
/// </summary>
public class LoadTestingService : ILoadTestingService
{
    private readonly IHttpClientFactory _httpClientFactory;
    private readonly ICacheService _cacheService;
    private readonly IConfiguration _configuration;
    private readonly ILogger<LoadTestingService> _logger;
    private readonly ConcurrentDictionary<string, LoadTestExecution> _runningTests;
    private readonly JsonSerializerOptions _jsonOptions;

    public LoadTestingService(
        IHttpClientFactory httpClientFactory,
        ICacheService cacheService,
        IConfiguration configuration,
        ILogger<LoadTestingService> logger)
    {
        _httpClientFactory = httpClientFactory;
        _cacheService = cacheService;
        _configuration = configuration;
        _logger = logger;
        _runningTests = new ConcurrentDictionary<string, LoadTestExecution>();
        _jsonOptions = new JsonSerializerOptions
        {
            PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
            WriteIndented = true
        };
    }

    public async Task<Result<LoadTestResult>> RunLoadTestScenarioAsync(
        LoadTestScenario scenario,
        CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Starting load test scenario: {ScenarioName}", scenario.ScenarioName);

            var execution = new LoadTestExecution
            {
                TestId = scenario.ScenarioId,
                StartTime = DateTime.UtcNow,
                CancellationTokenSource = CancellationTokenSource.CreateLinkedTokenSource(cancellationToken)
            };

            _runningTests[scenario.ScenarioId] = execution;

            var result = new LoadTestResult
            {
                TestId = scenario.ScenarioId,
                ScenarioName = scenario.ScenarioName,
                StartTime = execution.StartTime,
                Status = TestStatus.Running
            };

            // 启动测试执行
            _ = Task.Run(async () =>
            {
                try
                {
                    await ExecuteLoadTestScenario(scenario, execution, result);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Load test scenario failed: {ScenarioName}", scenario.ScenarioName);
                    result.Status = TestStatus.Failed;
                }
                finally
                {
                    result.EndTime = DateTime.UtcNow;
                    _runningTests.TryRemove(scenario.ScenarioId, out _);
                    await SaveTestResult(result);
                }
            }, execution.CancellationTokenSource.Token);

            // 等待测试开始
            await Task.Delay(100, cancellationToken);

            return Result<LoadTestResult>.Success(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to start load test scenario");
            return Result<LoadTestResult>.Failure($"Failed to start load test: {ex.Message}");
        }
    }

    public async Task<Result<StressTestResult>> RunStressTestAsync(
        StressTestConfiguration configuration,
        CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Starting stress test: {TestId}", configuration.TestId);

            var result = new StressTestResult
            {
                TestId = configuration.TestId,
                ScenarioName = "Stress Test",
                StartTime = DateTime.UtcNow,
                Status = TestStatus.Running
            };

            var execution = new LoadTestExecution
            {
                TestId = configuration.TestId,
                StartTime = result.StartTime,
                CancellationTokenSource = CancellationTokenSource.CreateLinkedTokenSource(cancellationToken)
            };

            _runningTests[configuration.TestId] = execution;

            // 启动压力测试执行
            _ = Task.Run(async () =>
            {
                try
                {
                    await ExecuteStressTest(configuration, execution, result);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Stress test failed: {TestId}", configuration.TestId);
                    result.Status = TestStatus.Failed;
                }
                finally
                {
                    result.EndTime = DateTime.UtcNow;
                    _runningTests.TryRemove(configuration.TestId, out _);
                    await SaveTestResult(result);
                }
            }, execution.CancellationTokenSource.Token);

            await Task.Delay(100, cancellationToken);

            return Result<StressTestResult>.Success(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to start stress test");
            return Result<StressTestResult>.Failure($"Failed to start stress test: {ex.Message}");
        }
    }

    public async Task<Result<SpikeTestResult>> RunSpikeTestAsync(
        SpikeTestConfiguration configuration,
        CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Starting spike test: {TestId}", configuration.TestId);

            var result = new SpikeTestResult
            {
                TestId = configuration.TestId,
                ScenarioName = "Spike Test",
                StartTime = DateTime.UtcNow,
                Status = TestStatus.Running
            };

            var execution = new LoadTestExecution
            {
                TestId = configuration.TestId,
                StartTime = result.StartTime,
                CancellationTokenSource = CancellationTokenSource.CreateLinkedTokenSource(cancellationToken)
            };

            _runningTests[configuration.TestId] = execution;

            // 启动尖峰测试执行
            _ = Task.Run(async () =>
            {
                try
                {
                    await ExecuteSpikeTest(configuration, execution, result);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Spike test failed: {TestId}", configuration.TestId);
                    result.Status = TestStatus.Failed;
                }
                finally
                {
                    result.EndTime = DateTime.UtcNow;
                    _runningTests.TryRemove(configuration.TestId, out _);
                    await SaveTestResult(result);
                }
            }, execution.CancellationTokenSource.Token);

            await Task.Delay(100, cancellationToken);

            return Result<SpikeTestResult>.Success(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to start spike test");
            return Result<SpikeTestResult>.Failure($"Failed to start spike test: {ex.Message}");
        }
    }

    public async Task<Result<SoakTestResult>> RunSoakTestAsync(
        SoakTestConfiguration configuration,
        CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Starting soak test: {TestId}", configuration.TestId);

            var result = new SoakTestResult
            {
                TestId = configuration.TestId,
                ScenarioName = "Soak Test",
                StartTime = DateTime.UtcNow,
                Status = TestStatus.Running
            };

            var execution = new LoadTestExecution
            {
                TestId = configuration.TestId,
                StartTime = result.StartTime,
                CancellationTokenSource = CancellationTokenSource.CreateLinkedTokenSource(cancellationToken)
            };

            _runningTests[configuration.TestId] = execution;

            // 启动浸泡测试执行
            _ = Task.Run(async () =>
            {
                try
                {
                    await ExecuteSoakTest(configuration, execution, result);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Soak test failed: {TestId}", configuration.TestId);
                    result.Status = TestStatus.Failed;
                }
                finally
                {
                    result.EndTime = DateTime.UtcNow;
                    _runningTests.TryRemove(configuration.TestId, out _);
                    await SaveTestResult(result);
                }
            }, execution.CancellationTokenSource.Token);

            await Task.Delay(100, cancellationToken);

            return Result<SoakTestResult>.Success(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to start soak test");
            return Result<SoakTestResult>.Failure($"Failed to start soak test: {ex.Message}");
        }
    }

    public async Task<Result<CapacityTestResult>> RunCapacityTestAsync(
        CapacityTestConfiguration configuration,
        CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Starting capacity test: {TestId}", configuration.TestId);

            var result = new CapacityTestResult
            {
                TestId = configuration.TestId,
                ScenarioName = "Capacity Test",
                StartTime = DateTime.UtcNow,
                Status = TestStatus.Running
            };

            var execution = new LoadTestExecution
            {
                TestId = configuration.TestId,
                StartTime = result.StartTime,
                CancellationTokenSource = CancellationTokenSource.CreateLinkedTokenSource(cancellationToken)
            };

            _runningTests[configuration.TestId] = execution;

            // 启动容量测试执行
            _ = Task.Run(async () =>
            {
                try
                {
                    await ExecuteCapacityTest(configuration, execution, result);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Capacity test failed: {TestId}", configuration.TestId);
                    result.Status = TestStatus.Failed;
                }
                finally
                {
                    result.EndTime = DateTime.UtcNow;
                    _runningTests.TryRemove(configuration.TestId, out _);
                    await SaveTestResult(result);
                }
            }, execution.CancellationTokenSource.Token);

            await Task.Delay(100, cancellationToken);

            return Result<CapacityTestResult>.Success(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to start capacity test");
            return Result<CapacityTestResult>.Failure($"Failed to start capacity test: {ex.Message}");
        }
    }

    public async Task<Result<LoadTestProgress>> GetTestProgressAsync(
        string testId,
        CancellationToken cancellationToken = default)
    {
        try
        {
            if (!_runningTests.TryGetValue(testId, out var execution))
            {
                // 尝试从缓存获取
                var cachedProgress = await _cacheService.GetAsync<LoadTestProgress>($"loadtest:progress:{testId}");
                if (cachedProgress != null)
                {
                    return Result<LoadTestProgress>.Success(cachedProgress);
                }

                return Result<LoadTestProgress>.Failure("Test not found or already completed");
            }

            var progress = new LoadTestProgress
            {
                TestId = testId,
                Status = TestStatus.Running,
                ElapsedTime = DateTime.UtcNow - execution.StartTime,
                CurrentUsers = execution.CurrentUsers,
                CompletedRequests = execution.CompletedRequests,
                CurrentErrorRate = execution.FailedRequests > 0 ?
                    (double)execution.FailedRequests / execution.CompletedRequests : 0,
                CurrentResponseTime = execution.AverageResponseTime,
                CurrentPhase = execution.CurrentPhase
            };

            // 计算进度百分比
            if (execution.TotalExpectedRequests > 0)
            {
                progress.ProgressPercent = (double)execution.CompletedRequests / execution.TotalExpectedRequests * 100;
            }

            // 估算剩余时间
            if (progress.ProgressPercent > 0 && progress.ProgressPercent < 100)
            {
                var estimatedTotalTime = progress.ElapsedTime.TotalSeconds / (progress.ProgressPercent / 100);
                progress.EstimatedTimeRemaining = TimeSpan.FromSeconds(estimatedTotalTime - progress.ElapsedTime.TotalSeconds);
            }

            return Result<LoadTestProgress>.Success(progress);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get test progress for {TestId}", testId);
            return Result<LoadTestProgress>.Failure($"Failed to get test progress: {ex.Message}");
        }
    }

    public async Task<Result> StopTestAsync(string testId, CancellationToken cancellationToken = default)
    {
        try
        {
            if (_runningTests.TryRemove(testId, out var execution))
            {
                execution.CancellationTokenSource.Cancel();

                _logger.LogInformation("Load test {TestId} stopped by user", testId);

                // 更新测试状态
                var result = await _cacheService.GetAsync<LoadTestResult>($"loadtest:result:{testId}");
                if (result != null)
                {
                    result.Status = TestStatus.Stopped;
                    result.EndTime = DateTime.UtcNow;
                    await SaveTestResult(result);
                }

                return Result.Success();
            }

            return Result.Failure("Test not found or already completed");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to stop test {TestId}", testId);
            return Result.Failure($"Failed to stop test: {ex.Message}");
        }
    }

    public Result<LoadTestAnalysis> AnalyzeTestResults(LoadTestResult result)
    {
        try
        {
            var analysis = new LoadTestAnalysis
            {
                TestId = result.TestId,
                AnalyzedAt = DateTime.UtcNow
            };

            // 评估整体性能等级
            analysis.OverallGrade = EvaluatePerformanceGrade(result);

            // 识别性能问题
            analysis.Issues = IdentifyPerformanceIssues(result);

            // 识别性能优势
            analysis.Strengths = IdentifyPerformanceStrengths(result);

            // 识别优化机会
            analysis.Opportunities = IdentifyOptimizationOpportunities(result);

            // 评估可扩展性
            analysis.Scalability = AssessScalability(result);

            // 评估可靠性
            analysis.Reliability = AssessReliability(result);

            return Result<LoadTestAnalysis>.Success(analysis);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to analyze test results");
            return Result<LoadTestAnalysis>.Failure($"Failed to analyze results: {ex.Message}");
        }
    }

    public async Task<Result<LoadTestReport>> GenerateLoadTestReportAsync(
        LoadTestResult result,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var analysisResult = AnalyzeTestResults(result);
            if (!analysisResult.IsSuccess)
            {
                return Result<LoadTestReport>.Failure("Failed to analyze test results");
            }

            var report = new LoadTestReport
            {
                GeneratedAt = DateTime.UtcNow,
                TestResult = result,
                Analysis = analysisResult.Value!,
                Summary = GenerateExecutiveSummary(result, analysisResult.Value!),
                Charts = GenerateCharts(result)
            };

            // 保存报告
            await SaveLoadTestReport(report, cancellationToken);

            _logger.LogInformation("Load test report generated: {ReportId}", report.ReportId);

            return Result<LoadTestReport>.Success(report);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to generate load test report");
            return Result<LoadTestReport>.Failure($"Failed to generate report: {ex.Message}");
        }
    }

    #region Private Methods - Test Execution

    private async Task ExecuteLoadTestScenario(
        LoadTestScenario scenario,
        LoadTestExecution execution,
        LoadTestResult result)
    {
        var virtualUsers = new List<VirtualUser>();
        var metrics = new ConcurrentBag<RequestMetric>();
        var cancellationToken = execution.CancellationTokenSource.Token;

        try
        {
            // 根据负载模式创建虚拟用户
            await CreateVirtualUsers(scenario, execution, virtualUsers, metrics, cancellationToken);

            // 执行测试
            await ExecuteVirtualUsers(scenario, execution, virtualUsers, result, cancellationToken);

            // 收集最终结果
            CollectFinalResults(metrics, result);

            result.Status = TestStatus.Completed;
        }
        catch (OperationCanceledException)
        {
            result.Status = TestStatus.Stopped;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error executing load test scenario");
            result.Status = TestStatus.Failed;
            throw;
        }
        finally
        {
            // 清理虚拟用户
            foreach (var user in virtualUsers)
            {
                user.Dispose();
            }
        }
    }

    private async Task ExecuteStressTest(
        StressTestConfiguration configuration,
        LoadTestExecution execution,
        StressTestResult result)
    {
        var currentUsers = configuration.StartingUsers;
        var stressLevels = new List<StressLevel>();
        var breakingPointFound = false;

        while (!breakingPointFound &&
               currentUsers <= 10000 &&
               DateTime.UtcNow - execution.StartTime < configuration.MaxDuration)
        {
            if (execution.CancellationTokenSource.Token.IsCancellationRequested)
                break;

            execution.CurrentPhase = $"Testing with {currentUsers} users";

            // 运行当前负载级别
            var levelResult = await RunStressLevel(
                currentUsers,
                configuration,
                execution,
                execution.CancellationTokenSource.Token);

            stressLevels.Add(levelResult);

            // 检查是否达到破坏点
            if (levelResult.ErrorRate > configuration.TargetErrorRate ||
                levelResult.ResponseTime > configuration.TargetResponseTime)
            {
                breakingPointFound = true;
                result.BreakingPointUsers = currentUsers;
                result.BreakingPointResponseTime = levelResult.ResponseTime;
                result.BreakingPointErrorRate = levelResult.ErrorRate;
                result.BreakingPointReason = DetermineBreakingPointReason(levelResult, configuration);
            }

            currentUsers += configuration.UserIncrement;
        }

        result.StressLevels = stressLevels;
        CollectStressTestResults(stressLevels, result);
    }

    private async Task ExecuteSpikeTest(
        SpikeTestConfiguration configuration,
        LoadTestExecution execution,
        SpikeTestResult result)
    {
        var metrics = new ConcurrentBag<RequestMetric>();

        // 阶段1：基线测试
        execution.CurrentPhase = "Baseline Phase";
        var baselineMetrics = await RunLoadPhase(
            configuration.BaselineUsers,
            TimeSpan.FromMinutes(5),
            configuration.BaseScenario,
            execution,
            metrics);

        result.BaselineResponseTime = baselineMetrics.Average(m => m.ResponseTime);

        // 阶段2：尖峰测试
        execution.CurrentPhase = "Spike Phase";
        var spikeMetrics = await RunLoadPhase(
            configuration.SpikeUsers,
            configuration.SpikeDuration,
            configuration.BaseScenario,
            execution,
            metrics);

        result.SpikeResponseTime = spikeMetrics.Average(m => m.ResponseTime);

        // 阶段3：恢复测试
        execution.CurrentPhase = "Recovery Phase";
        var recoveryStartTime = DateTime.UtcNow;
        var recoveryMetrics = await RunLoadPhase(
            configuration.BaselineUsers,
            configuration.RecoveryTime,
            configuration.BaseScenario,
            execution,
            metrics);

        result.RecoveryResponseTime = recoveryMetrics.Average(m => m.ResponseTime);
        result.RecoveryDuration = DateTime.UtcNow - recoveryStartTime;

        // 检查是否完全恢复
        result.FullyRecovered = Math.Abs(result.RecoveryResponseTime - result.BaselineResponseTime) /
                              result.BaselineResponseTime < 0.1;

        // 分析影响
        result.Impacts = AnalyzeSpikeImpacts(result);

        CollectFinalResults(metrics, result);
    }

    private async Task ExecuteSoakTest(
        SoakTestConfiguration configuration,
        LoadTestExecution execution,
        SoakTestResult result)
    {
        var metrics = new ConcurrentBag<RequestMetric>();
        var resourceSnapshots = new List<ResourceSnapshot>();
        var degradationPoints = new List<DegradationPoint>();

        // 记录初始资源使用
        result.InitialMemoryUsage = GC.GetTotalMemory(false);
        var initialSnapshot = CaptureResourceSnapshot();
        resourceSnapshots.Add(initialSnapshot);

        // 渐进增加负载
        execution.CurrentPhase = "Ramp-up Phase";
        await RampUpUsers(configuration.TargetUsers, configuration.RampUpTime, execution);

        // 持续负载阶段
        execution.CurrentPhase = "Sustained Load Phase";
        var soakStartTime = DateTime.UtcNow;
        var lastSnapshotTime = DateTime.UtcNow;

        while (DateTime.UtcNow - soakStartTime < configuration.TestDuration)
        {
            if (execution.CancellationTokenSource.Token.IsCancellationRequested)
                break;

            // 每5分钟捕获一次资源快照
            if (DateTime.UtcNow - lastSnapshotTime > TimeSpan.FromMinutes(5))
            {
                var snapshot = CaptureResourceSnapshot();
                resourceSnapshots.Add(snapshot);
                lastSnapshotTime = DateTime.UtcNow;

                // 检查性能退化
                var degradation = CheckForDegradation(snapshot, initialSnapshot, configuration);
                if (degradation != null)
                {
                    degradationPoints.Add(degradation);
                }
            }

            // 继续执行负载
            await Task.Delay(1000, execution.CancellationTokenSource.Token);
        }

        // 记录最终资源使用
        result.FinalMemoryUsage = GC.GetTotalMemory(false);
        result.MemoryGrowthRate = (double)(result.FinalMemoryUsage - result.InitialMemoryUsage) /
                                 result.InitialMemoryUsage;
        result.MemoryLeakDetected = result.MemoryGrowthRate > configuration.AcceptableMemoryGrowth;

        // 分析资源趋势
        result.ResourceTrends = AnalyzeResourceTrends(resourceSnapshots);
        result.DegradationPoints = degradationPoints;

        CollectFinalResults(metrics, result);
    }

    private async Task ExecuteCapacityTest(
        CapacityTestConfiguration configuration,
        LoadTestExecution execution,
        CapacityTestResult result)
    {
        var capacityPoints = new List<CapacityPoint>();
        var currentUsers = configuration.MinUsers;

        while (currentUsers <= configuration.MaxUsers)
        {
            if (execution.CancellationTokenSource.Token.IsCancellationRequested)
                break;

            execution.CurrentPhase = $"Testing capacity with {currentUsers} users";

            // 测试当前用户数的容量
            var capacityPoint = await TestCapacityPoint(
                currentUsers,
                configuration,
                execution);

            capacityPoints.Add(capacityPoint);

            // 检查是否达到容量限制
            if (!capacityPoint.MeetsTargets)
            {
                result.MaxCapacityUsers = currentUsers - configuration.UserStep;
                break;
            }

            currentUsers += configuration.UserStep;
        }

        result.CapacityPoints = capacityPoints;

        // 分析容量结果
        AnalyzeCapacityResults(capacityPoints, configuration, result);
    }

    #endregion

    #region Private Methods - Helpers

    private async Task CreateVirtualUsers(
        LoadTestScenario scenario,
        LoadTestExecution execution,
        List<VirtualUser> virtualUsers,
        ConcurrentBag<RequestMetric> metrics,
        CancellationToken cancellationToken)
    {
        var targetUsers = scenario.LoadPattern.TargetUsers;

        for (int i = 0; i < targetUsers; i++)
        {
            var httpClient = _httpClientFactory.CreateClient("LoadTest");
            var behavior = SelectUserBehavior(scenario.UserBehaviors);

            var virtualUser = new VirtualUser
            {
                UserId = i,
                HttpClient = httpClient,
                Behavior = behavior,
                Metrics = metrics
            };

            virtualUsers.Add(virtualUser);
        }
    }

    private async Task ExecuteVirtualUsers(
        LoadTestScenario scenario,
        LoadTestExecution execution,
        List<VirtualUser> virtualUsers,
        LoadTestResult result,
        CancellationToken cancellationToken)
    {
        var tasks = new List<Task>();
        var activeUsers = 0;

        // 根据负载模式激活用户
        foreach (var step in scenario.LoadPattern.Steps)
        {
            var usersToActivate = step.UserCount - activeUsers;

            // 渐进激活用户
            for (int i = 0; i < usersToActivate; i++)
            {
                if (activeUsers >= virtualUsers.Count)
                    break;

                var user = virtualUsers[activeUsers];
                var userTask = ExecuteUserBehavior(user, scenario.Duration, execution, cancellationToken);
                tasks.Add(userTask);

                activeUsers++;
                execution.CurrentUsers = activeUsers;

                // 平滑激活
                if (step.TransitionTime.TotalMilliseconds > 0)
                {
                    await Task.Delay((int)(step.TransitionTime.TotalMilliseconds / usersToActivate), cancellationToken);
                }
            }

            // 维持当前级别
            await Task.Delay(step.Duration, cancellationToken);
        }

        // 等待所有用户完成
        await Task.WhenAll(tasks);
    }

    private async Task<Task> ExecuteUserBehavior(
        VirtualUser user,
        TestDuration duration,
        LoadTestExecution execution,
        CancellationToken cancellationToken)
    {
        return Task.Run(async () =>
        {
            var stopwatch = new Stopwatch();
            var iterations = 0;
            var startTime = DateTime.UtcNow;

            while (!ShouldStopUser(duration, iterations, startTime, cancellationToken))
            {
                foreach (var action in user.Behavior.Actions)
                {
                    try
                    {
                        stopwatch.Restart();
                        var success = await ExecuteUserAction(user, action, cancellationToken);
                        stopwatch.Stop();

                        var metric = new RequestMetric
                        {
                            Timestamp = DateTime.UtcNow,
                            ActionName = action.ActionName,
                            ResponseTime = stopwatch.Elapsed.TotalMilliseconds,
                            Success = success,
                            UserId = user.UserId
                        };

                        user.Metrics.Add(metric);

                        // Update counters - consider using thread-safe counters if needed
                        execution.CompletedRequests++;
                        if (!success)
                        {
                            execution.FailedRequests++;
                        }

                        // 思考时间
                        await Task.Delay(user.Behavior.ThinkTime, cancellationToken);
                    }
                    catch (Exception ex)
                    {
                        _logger.LogWarning(ex, "User action failed: {ActionName}", action.ActionName);
                    }
                }

                iterations++;
            }
        }, cancellationToken);
    }

    private async Task<bool> ExecuteUserAction(
        VirtualUser user,
        UserAction action,
        CancellationToken cancellationToken)
    {
        switch (action.Type)
        {
            case ActionType.HttpRequest:
                return await ExecuteHttpRequest(user, action, cancellationToken);

            case ActionType.WebSocketConnect:
                // WebSocket连接测试
                // 在实际负载测试中，通常会创建持久的WebSocket连接
                // 这里简化为返回成功，因为实际的WebSocket测试需要专门的客户端库
                _logger.LogDebug("模拟WebSocket连接: {Target}", action.Target ?? "ws://localhost");
                await Task.Delay(100, cancellationToken); // 模拟连接延迟
                return true;

            case ActionType.DatabaseQuery:
                // 数据库查询测试
                // 在实际场景中，这里会执行真实的数据库查询
                // 由于LoadTestingService主要测试API端点，数据库查询通常通过API间接测试
                _logger.LogDebug("模拟数据库查询: {Target}", action.Target ?? "SELECT 1");
                await Task.Delay(50, cancellationToken); // 模拟查询延迟
                return true;

            default:
                return true;
        }
    }

    private async Task<bool> ExecuteHttpRequest(
        VirtualUser user,
        UserAction action,
        CancellationToken cancellationToken)
    {
        try
        {
            var request = new HttpRequestMessage(action.HttpMethod, action.Target);

            // 添加请求头
            foreach (var header in action.Headers)
            {
                request.Headers.Add(header.Key, header.Value);
            }

            // 添加请求体
            if (action.RequestBody != null)
            {
                var json = JsonSerializer.Serialize(action.RequestBody, _jsonOptions);
                request.Content = new StringContent(json, Encoding.UTF8, "application/json");
            }

            // 发送请求
            using var cts = CancellationTokenSource.CreateLinkedTokenSource(cancellationToken);
            cts.CancelAfter(action.Timeout);

            var response = await user.HttpClient.SendAsync(request, cts.Token);

            // 验证响应
            foreach (var validation in action.Validations)
            {
                if (!ValidateResponse(response, validation))
                {
                    return false;
                }
            }

            return response.IsSuccessStatusCode;
        }
        catch (Exception ex)
        {
            _logger.LogDebug(ex, "HTTP request failed for action: {ActionName}", action.ActionName);
            return false;
        }
    }

    private bool ValidateResponse(HttpResponseMessage response, ResponseValidation validation)
    {
        switch (validation.Type)
        {
            case ValidationType.StatusCode:
                return response.StatusCode.ToString() == validation.ExpectedValue;

            case ValidationType.ResponseTime:
                // 响应时间验证在外部处理
                return true;

            case ValidationType.Contains:
                var content = response.Content.ReadAsStringAsync().Result;
                return content.Contains(validation.ExpectedValue);

            case ValidationType.Header:
                return response.Headers.TryGetValues(validation.ActualPath, out var values) &&
                       values.Contains(validation.ExpectedValue);

            default:
                return true;
        }
    }

    private UserBehavior SelectUserBehavior(List<UserBehavior> behaviors)
    {
        if (behaviors.Count == 1)
            return behaviors[0];

        // 基于权重随机选择行为
        var totalWeight = behaviors.Sum(b => b.Weight);
        var random = Random.Shared.NextDouble() * totalWeight;
        var currentWeight = 0.0;

        foreach (var behavior in behaviors)
        {
            currentWeight += behavior.Weight;
            if (random <= currentWeight)
                return behavior;
        }

        return behaviors.Last();
    }

    private bool ShouldStopUser(
        TestDuration duration,
        int iterations,
        DateTime startTime,
        CancellationToken cancellationToken)
    {
        if (cancellationToken.IsCancellationRequested)
            return true;

        switch (duration.Type)
        {
            case DurationType.FixedTime:
                return DateTime.UtcNow - startTime > duration.FixedDuration;

            case DurationType.Iterations:
                return iterations >= duration.IterationCount;

            case DurationType.UntilCondition:
                // 条件检查 - 基于自定义条件判断是否结束测试
                // 例如：当错误率超过阈值、响应时间过长、或达到特定业务条件时停止
                if (duration.StopCondition != null)
                {
                    // 评估停止条件
                    // 这里假设StopCondition包含条件类型和阈值
                    // 实际实现需要根据LoadTestResult中的指标进行判断
                    _logger.LogDebug("评估停止条件: {Condition}", duration.StopCondition);

                    // 简化实现：如果已经运行超过最小时间（如1分钟），则可以检查条件
                    if (DateTime.UtcNow - startTime > TimeSpan.FromMinutes(1))
                    {
                        // 这里应该根据实际的测试结果指标来判断
                        // 例如：检查错误率、响应时间等
                        return true;
                    }
                }
                return false;

            default:
                return false;
        }
    }

    private void CollectFinalResults(ConcurrentBag<RequestMetric> metrics, LoadTestResult result)
    {
        var metricsList = metrics.ToList();

        if (!metricsList.Any())
            return;

        // 总体统计
        result.TotalRequests = metricsList.Count;
        result.SuccessfulRequests = metricsList.Count(m => m.Success);
        result.FailedRequests = metricsList.Count(m => !m.Success);

        // 响应时间统计
        var responseTimes = metricsList.Select(m => m.ResponseTime).OrderBy(rt => rt).ToList();
        result.AverageResponseTime = responseTimes.Average();
        result.MinResponseTime = responseTimes.First();
        result.MaxResponseTime = responseTimes.Last();
        result.MedianResponseTime = GetPercentile(responseTimes, 50);
        result.P90ResponseTime = GetPercentile(responseTimes, 90);
        result.P95ResponseTime = GetPercentile(responseTimes, 95);
        result.P99ResponseTime = GetPercentile(responseTimes, 99);

        // 吞吐量
        var duration = result.Duration.TotalSeconds;
        if (duration > 0)
        {
            result.RequestsPerSecond = result.TotalRequests / duration;
        }

        // 时间序列指标
        result.TimeSeriesMetrics = GenerateTimeSeriesMetrics(metricsList);

        // 动作结果
        result.ActionResults = metricsList
            .GroupBy(m => m.ActionName)
            .ToDictionary(
                g => g.Key,
                g => new ActionResult
                {
                    ActionName = g.Key,
                    RequestCount = g.Count(),
                    SuccessCount = g.Count(m => m.Success),
                    FailureCount = g.Count(m => !m.Success),
                    AverageResponseTime = g.Average(m => m.ResponseTime),
                    MinResponseTime = g.Min(m => m.ResponseTime),
                    MaxResponseTime = g.Max(m => m.ResponseTime),
                    P95ResponseTime = GetPercentile(g.Select(m => m.ResponseTime).OrderBy(rt => rt).ToList(), 95)
                });
    }

    private double GetPercentile(List<double> sortedData, int percentile)
    {
        if (sortedData.Count == 0)
            return 0;

        var index = (int)Math.Ceiling(percentile / 100.0 * sortedData.Count) - 1;
        return sortedData[Math.Max(0, Math.Min(index, sortedData.Count - 1))];
    }

    private List<TimeSeriesMetric> GenerateTimeSeriesMetrics(List<RequestMetric> metrics)
    {
        return metrics
            .GroupBy(m => m.Timestamp.Truncate(TimeSpan.FromSeconds(10)))
            .Select(g => new TimeSeriesMetric
            {
                Timestamp = g.Key,
                ActiveUsers = g.Select(m => m.UserId).Distinct().Count(),
                RequestsPerSecond = g.Count() / 10.0,
                AverageResponseTime = g.Average(m => m.ResponseTime),
                ErrorRate = (double)g.Count(m => !m.Success) / g.Count()
            })
            .OrderBy(m => m.Timestamp)
            .ToList();
    }

    private async Task SaveTestResult(LoadTestResult result)
    {
        var cacheKey = $"loadtest:result:{result.TestId}";
        await _cacheService.SetAsync(cacheKey, result, TimeSpan.FromDays(7));
    }

    private async Task SaveLoadTestReport(LoadTestReport report, CancellationToken cancellationToken)
    {
        var reportPath = Path.Combine("loadtest-reports", $"{report.ReportId}.json");
        var reportJson = JsonSerializer.Serialize(report, _jsonOptions);

        Directory.CreateDirectory("loadtest-reports");
        await File.WriteAllTextAsync(reportPath, reportJson, cancellationToken);
    }

    #endregion

    #region Private Methods - Analysis

    private PerformanceGrade EvaluatePerformanceGrade(LoadTestResult result)
    {
        var score = 100.0;

        // 基于错误率扣分
        if (result.ErrorRate > 0.001) score -= 10;
        if (result.ErrorRate > 0.01) score -= 20;
        if (result.ErrorRate > 0.05) score -= 30;

        // 基于响应时间扣分
        if (result.P95ResponseTime > 500) score -= 10;
        if (result.P95ResponseTime > 1000) score -= 20;
        if (result.P95ResponseTime > 3000) score -= 30;

        return score switch
        {
            >= 90 => PerformanceGrade.Excellent,
            >= 80 => PerformanceGrade.Good,
            >= 70 => PerformanceGrade.Fair,
            >= 60 => PerformanceGrade.Poor,
            _ => PerformanceGrade.Critical
        };
    }

    private List<PerformanceIssue> IdentifyPerformanceIssues(LoadTestResult result)
    {
        var issues = new List<PerformanceIssue>();

        // 检查高错误率
        if (result.ErrorRate > 0.01)
        {
            issues.Add(new PerformanceIssue
            {
                Category = "Reliability",
                Issue = "High error rate",
                Severity = result.ErrorRate > 0.05 ? "Critical" : "High",
                Impact = result.ErrorRate * 100,
                Evidence = $"Error rate: {result.ErrorRate:P2}"
            });
        }

        // 检查慢响应时间
        if (result.P95ResponseTime > 1000)
        {
            issues.Add(new PerformanceIssue
            {
                Category = "Performance",
                Issue = "Slow response times",
                Severity = result.P95ResponseTime > 3000 ? "Critical" : "High",
                Impact = result.P95ResponseTime,
                Evidence = $"P95 response time: {result.P95ResponseTime:F0}ms"
            });
        }

        // 检查吞吐量问题
        if (result.RequestsPerSecond < 100)
        {
            issues.Add(new PerformanceIssue
            {
                Category = "Throughput",
                Issue = "Low throughput",
                Severity = "Medium",
                Impact = result.RequestsPerSecond,
                Evidence = $"RPS: {result.RequestsPerSecond:F2}"
            });
        }

        return issues;
    }

    private List<PerformanceStrength> IdentifyPerformanceStrengths(LoadTestResult result)
    {
        var strengths = new List<PerformanceStrength>();

        if (result.ErrorRate < 0.001)
        {
            strengths.Add(new PerformanceStrength
            {
                Area = "Reliability",
                Description = "Excellent reliability with minimal errors",
                Metric = $"Error rate: {result.ErrorRate:P2}"
            });
        }

        if (result.P95ResponseTime < 200)
        {
            strengths.Add(new PerformanceStrength
            {
                Area = "Performance",
                Description = "Excellent response times",
                Metric = $"P95: {result.P95ResponseTime:F0}ms"
            });
        }

        if (result.RequestsPerSecond > 1000)
        {
            strengths.Add(new PerformanceStrength
            {
                Area = "Throughput",
                Description = "High throughput capability",
                Metric = $"RPS: {result.RequestsPerSecond:F0}"
            });
        }

        return strengths;
    }

    private List<OptimizationOpportunity> IdentifyOptimizationOpportunities(LoadTestResult result)
    {
        var opportunities = new List<OptimizationOpportunity>();

        // 检查慢动作
        var slowActions = result.ActionResults
            .Where(a => a.Value.P95ResponseTime > 1000)
            .ToList();

        foreach (var action in slowActions)
        {
            opportunities.Add(new OptimizationOpportunity
            {
                Area = "API Performance",
                Opportunity = $"Optimize {action.Key} endpoint",
                PotentialImprovement = 50,
                Implementation = "Add caching, optimize queries, or implement pagination",
                Priority = action.Value.P95ResponseTime > 3000 ? "High" : "Medium"
            });
        }

        return opportunities;
    }

    private ScalabilityAssessment AssessScalability(LoadTestResult result)
    {
        return new ScalabilityAssessment
        {
            IsLinearilyScalable = result.RequestsPerSecond / result.MaxConcurrentUsers > 0.8,
            ScalabilityFactor = result.RequestsPerSecond / result.MaxConcurrentUsers,
            CurrentCapacity = (int)result.RequestsPerSecond,
            ProjectedCapacity = (int)(result.RequestsPerSecond * 2),
            ScalingBottlenecks = new List<string>()
        };
    }

    private ReliabilityAssessment AssessReliability(LoadTestResult result)
    {
        return new ReliabilityAssessment
        {
            Availability = (1 - result.ErrorRate) * 100,
            MTBF = result.TotalRequests / Math.Max(1, result.Errors.Count),
            MTTR = 5, // 假设5分钟恢复时间
            FailurePoints = result.Errors.Select(e => e.ActionName).Distinct().ToList()
        };
    }

    private ExecutiveSummary GenerateExecutiveSummary(LoadTestResult result, LoadTestAnalysis analysis)
    {
        return new ExecutiveSummary
        {
            TestObjective = $"Load test of {result.ScenarioName}",
            TestConclusion = $"System achieved {analysis.OverallGrade} performance grade",
            KeyFindings = new List<string>
            {
                $"Handled {result.MaxConcurrentUsers} concurrent users",
                $"Achieved {result.RequestsPerSecond:F0} requests per second",
                $"P95 response time: {result.P95ResponseTime:F0}ms",
                $"Error rate: {result.ErrorRate:P2}"
            },
            Recommendations = analysis.Opportunities.Select(o => o.Opportunity).ToList(),
            KeyMetrics = new Dictionary<string, string>
            {
                ["Duration"] = result.Duration.ToString(@"hh\:mm\:ss"),
                ["Total Requests"] = result.TotalRequests.ToString("N0"),
                ["Success Rate"] = $"{(1 - result.ErrorRate):P2}",
                ["Avg Response Time"] = $"{result.AverageResponseTime:F0}ms"
            }
        };
    }

    private List<Chart> GenerateCharts(LoadTestResult result)
    {
        return new List<Chart>
        {
            new Chart
            {
                ChartId = "response-time-over-time",
                ChartType = "line",
                Title = "Response Time Over Time",
                Data = result.TimeSeriesMetrics.Select(m => new
                {
                    x = m.Timestamp,
                    y = m.AverageResponseTime
                })
            },
            new Chart
            {
                ChartId = "throughput-over-time",
                ChartType = "line",
                Title = "Throughput Over Time",
                Data = result.TimeSeriesMetrics.Select(m => new
                {
                    x = m.Timestamp,
                    y = m.RequestsPerSecond
                })
            },
            new Chart
            {
                ChartId = "error-rate-over-time",
                ChartType = "line",
                Title = "Error Rate Over Time",
                Data = result.TimeSeriesMetrics.Select(m => new
                {
                    x = m.Timestamp,
                    y = m.ErrorRate * 100
                })
            }
        };
    }

    #endregion

    #region Private Methods - Stress Test Specific

    private async Task<StressLevel> RunStressLevel(
        int userCount,
        StressTestConfiguration configuration,
        LoadTestExecution execution,
        CancellationToken cancellationToken)
    {
        var scenario = configuration.BaseScenario;
        scenario.LoadPattern.TargetUsers = userCount;
        scenario.Duration = new TestDuration
        {
            Type = DurationType.FixedTime,
            FixedDuration = TimeSpan.FromMinutes(5)
        };

        var tempResult = new LoadTestResult();
        var metrics = new ConcurrentBag<RequestMetric>();

        // 运行负载
        await RunLoadPhase(userCount, scenario.Duration.FixedDuration!.Value, scenario, execution, metrics);

        CollectFinalResults(metrics, tempResult);

        return new StressLevel
        {
            UserCount = userCount,
            ResponseTime = tempResult.P95ResponseTime,
            ErrorRate = tempResult.ErrorRate,
            Throughput = tempResult.RequestsPerSecond,
            Status = DetermineStressLevelStatus(tempResult, configuration)
        };
    }

    private string DetermineStressLevelStatus(LoadTestResult result, StressTestConfiguration config)
    {
        if (result.ErrorRate > config.TargetErrorRate * 2)
            return "Failed";
        if (result.P95ResponseTime > config.TargetResponseTime * 2)
            return "Degraded";
        if (result.ErrorRate > config.TargetErrorRate || result.P95ResponseTime > config.TargetResponseTime)
            return "Warning";
        return "Healthy";
    }

    private string DetermineBreakingPointReason(StressLevel level, StressTestConfiguration config)
    {
        var reasons = new List<string>();

        if (level.ErrorRate > config.TargetErrorRate)
            reasons.Add($"Error rate exceeded target ({level.ErrorRate:P2} > {config.TargetErrorRate:P2})");

        if (level.ResponseTime > config.TargetResponseTime)
            reasons.Add($"Response time exceeded target ({level.ResponseTime:F0}ms > {config.TargetResponseTime:F0}ms)");

        return string.Join("; ", reasons);
    }

    private void CollectStressTestResults(List<StressLevel> levels, StressTestResult result)
    {
        if (!levels.Any())
            return;

        // 找出最佳性能点
        var optimalLevel = levels
            .Where(l => l.Status == "Healthy")
            .OrderByDescending(l => l.Throughput)
            .FirstOrDefault();

        if (optimalLevel != null)
        {
            result.MaxConcurrentUsers = optimalLevel.UserCount;
            result.RequestsPerSecond = optimalLevel.Throughput;
        }
    }

    #endregion

    #region Private Methods - Other Test Types

    private async Task<List<RequestMetric>> RunLoadPhase(
        int userCount,
        TimeSpan duration,
        LoadTestScenario scenario,
        LoadTestExecution execution,
        ConcurrentBag<RequestMetric> allMetrics)
    {
        var phaseMetrics = new ConcurrentBag<RequestMetric>();
        var virtualUsers = new List<VirtualUser>();

        // 创建虚拟用户
        for (int i = 0; i < userCount; i++)
        {
            var httpClient = _httpClientFactory.CreateClient("LoadTest");
            var behavior = SelectUserBehavior(scenario.UserBehaviors);

            var virtualUser = new VirtualUser
            {
                UserId = i,
                HttpClient = httpClient,
                Behavior = behavior,
                Metrics = phaseMetrics
            };

            virtualUsers.Add(virtualUser);
        }

        // 执行负载
        var tasks = virtualUsers.Select(user =>
            ExecuteUserBehavior(user, new TestDuration
            {
                Type = DurationType.FixedTime,
                FixedDuration = duration
            }, execution, execution.CancellationTokenSource.Token)
        ).ToList();

        await Task.WhenAll(tasks);

        // 收集指标
        foreach (var metric in phaseMetrics)
        {
            allMetrics.Add(metric);
        }

        return phaseMetrics.ToList();
    }

    private async Task RampUpUsers(int targetUsers, TimeSpan rampTime, LoadTestExecution execution)
    {
        var steps = 10;
        var usersPerStep = targetUsers / steps;
        var delayPerStep = rampTime.TotalMilliseconds / steps;

        for (int i = 1; i <= steps; i++)
        {
            execution.CurrentUsers = usersPerStep * i;
            await Task.Delay((int)delayPerStep, execution.CancellationTokenSource.Token);
        }
    }

    private double CalculateCpuUsage(Process process)
    {
        // CPU使用率计算
        // 注意：Process.TotalProcessorTime需要两次采样才能准确计算使用率
        // 这里简化实现，返回当前进程的CPU时间占比
        try
        {
            // 获取处理器数量
            var processorCount = Environment.ProcessorCount;

            // 使用进程的总CPU时间来估算使用率
            // 实际应用中应该维护前一次的采样值来计算增量
            var totalProcessorTime = process.TotalProcessorTime.TotalMilliseconds;
            var currentTime = DateTime.UtcNow;
            var runTime = (currentTime - process.StartTime).TotalMilliseconds;

            if (runTime > 0)
            {
                // 计算CPU使用率百分比
                var cpuUsage = (totalProcessorTime / runTime / processorCount) * 100;
                return Math.Min(100, Math.Max(0, cpuUsage)); // 确保在0-100范围内
            }

            return 0;
        }
        catch
        {
            // 如果无法获取CPU使用率，返回0
            return 0;
        }
    }

    private ResourceSnapshot CaptureResourceSnapshot()
    {
        var process = Process.GetCurrentProcess();

        return new ResourceSnapshot
        {
            Timestamp = DateTime.UtcNow,
            MemoryUsage = process.WorkingSet64,
            CpuUsage = CalculateCpuUsage(process),
            ThreadCount = process.Threads.Count,
            HandleCount = process.HandleCount,
            GCGen0 = GC.CollectionCount(0),
            GCGen1 = GC.CollectionCount(1),
            GCGen2 = GC.CollectionCount(2)
        };
    }

    private DegradationPoint? CheckForDegradation(
        ResourceSnapshot current,
        ResourceSnapshot initial,
        SoakTestConfiguration config)
    {
        var memoryGrowth = (double)(current.MemoryUsage - initial.MemoryUsage) / initial.MemoryUsage;

        if (memoryGrowth > config.AcceptableMemoryGrowth)
        {
            return new DegradationPoint
            {
                Timestamp = current.Timestamp,
                Metric = "Memory",
                Value = memoryGrowth * 100,
                Description = $"Memory growth exceeded acceptable limit: {memoryGrowth:P2}"
            };
        }

        return null;
    }

    private List<ResourceTrend> AnalyzeResourceTrends(List<ResourceSnapshot> snapshots)
    {
        if (snapshots.Count < 2)
            return new List<ResourceTrend>();

        var first = snapshots.First();
        var last = snapshots.Last();

        return new List<ResourceTrend>
        {
            new ResourceTrend
            {
                ResourceType = "Memory",
                InitialValue = first.MemoryUsage,
                FinalValue = last.MemoryUsage,
                GrowthRate = (double)(last.MemoryUsage - first.MemoryUsage) / first.MemoryUsage,
                Trend = last.MemoryUsage > first.MemoryUsage ? "Increasing" : "Stable"
            },
            new ResourceTrend
            {
                ResourceType = "Threads",
                InitialValue = first.ThreadCount,
                FinalValue = last.ThreadCount,
                GrowthRate = (double)(last.ThreadCount - first.ThreadCount) / first.ThreadCount,
                Trend = last.ThreadCount > first.ThreadCount * 1.1 ? "Increasing" : "Stable"
            }
        };
    }

    private List<SpikeImpact> AnalyzeSpikeImpacts(SpikeTestResult result)
    {
        var impacts = new List<SpikeImpact>();

        var responseTimeIncrease = (result.SpikeResponseTime - result.BaselineResponseTime) /
                                  result.BaselineResponseTime;

        if (responseTimeIncrease > 0.5)
        {
            impacts.Add(new SpikeImpact
            {
                Component = "Response Time",
                Impact = $"Increased by {responseTimeIncrease:P0} during spike",
                Severity = responseTimeIncrease > 2 ? 10 : 5
            });
        }

        if (!result.FullyRecovered)
        {
            impacts.Add(new SpikeImpact
            {
                Component = "System Recovery",
                Impact = "System did not fully recover after spike",
                Severity = 8
            });
        }

        return impacts;
    }

    private async Task<CapacityPoint> TestCapacityPoint(
        int userCount,
        CapacityTestConfiguration configuration,
        LoadTestExecution execution)
    {
        var scenario = configuration.BaseScenario;
        scenario.LoadPattern.TargetUsers = userCount;
        scenario.Duration = new TestDuration
        {
            Type = DurationType.FixedTime,
            FixedDuration = configuration.StepDuration
        };

        var tempResult = new LoadTestResult();
        var metrics = new ConcurrentBag<RequestMetric>();

        // 运行负载
        await RunLoadPhase(userCount, configuration.StepDuration, scenario, execution, metrics);

        CollectFinalResults(metrics, tempResult);

        var capacityPoint = new CapacityPoint
        {
            UserCount = userCount,
            Throughput = tempResult.RequestsPerSecond,
            ResponseTime = tempResult.P95ResponseTime,
            ErrorRate = tempResult.ErrorRate,
            CpuUsage = CalculateCpuUsage(Process.GetCurrentProcess()),
            MemoryUsage = Process.GetCurrentProcess().WorkingSet64
        };

        // 检查是否满足目标
        capacityPoint.MeetsTargets =
            capacityPoint.ResponseTime <= configuration.TargetResponseTime &&
            capacityPoint.Throughput >= configuration.TargetThroughput &&
            capacityPoint.ErrorRate < 0.01;

        return capacityPoint;
    }

    private void AnalyzeCapacityResults(
        List<CapacityPoint> points,
        CapacityTestConfiguration configuration,
        CapacityTestResult result)
    {
        if (!points.Any())
            return;

        // 找到最大容量点
        var maxCapacityPoint = points
            .Where(p => p.MeetsTargets)
            .OrderByDescending(p => p.UserCount)
            .FirstOrDefault();

        if (maxCapacityPoint != null)
        {
            result.MaxCapacityUsers = maxCapacityPoint.UserCount;
            result.MaxThroughput = maxCapacityPoint.Throughput;
        }

        // 找到最优点（最高吞吐量）
        var optimalPoint = points
            .Where(p => p.MeetsTargets)
            .OrderByDescending(p => p.Throughput)
            .FirstOrDefault();

        if (optimalPoint != null)
        {
            result.OptimalUsers = optimalPoint.UserCount;
            result.OptimalThroughput = optimalPoint.Throughput;
        }

        // 生成建议
        result.Recommendation = new CapacityRecommendation
        {
            RecommendedMaxUsers = result.MaxCapacityUsers,
            SafeOperatingUsers = (int)(result.MaxCapacityUsers * 0.8),
            ScalingStrategy = result.MaxCapacityUsers > 1000 ? "Horizontal scaling recommended" : "Vertical scaling sufficient",
            Bottlenecks = IdentifyBottlenecks(points)
        };
    }

    private List<string> IdentifyBottlenecks(List<CapacityPoint> points)
    {
        var bottlenecks = new List<string>();

        // 检查吞吐量平台期
        var lastPoints = points.TakeLast(3).ToList();
        if (lastPoints.Count == 3)
        {
            var throughputVariation = lastPoints.Max(p => p.Throughput) - lastPoints.Min(p => p.Throughput);
            if (throughputVariation < lastPoints.Average(p => p.Throughput) * 0.1)
            {
                bottlenecks.Add("Throughput has plateaued - possible backend bottleneck");
            }
        }

        // 检查响应时间急剧增加
        if (points.Count > 1)
        {
            var responseTimeGrowth = points.Last().ResponseTime / points.First().ResponseTime;
            if (responseTimeGrowth > 10)
            {
                bottlenecks.Add("Response time degradation - possible resource contention");
            }
        }

        return bottlenecks;
    }

    #endregion

    #region Private Classes

    private class LoadTestExecution
    {
        public string TestId { get; set; } = string.Empty;
        public DateTime StartTime { get; set; }
        public CancellationTokenSource CancellationTokenSource { get; set; } = new();
        public int CurrentUsers { get; set; }
        public long CompletedRequests { get; set; }
        public long FailedRequests { get; set; }
        public double AverageResponseTime { get; set; }
        public string CurrentPhase { get; set; } = string.Empty;
        public long TotalExpectedRequests { get; set; }
    }

    private class VirtualUser : IDisposable
    {
        public int UserId { get; set; }
        public HttpClient HttpClient { get; set; } = null!;
        public UserBehavior Behavior { get; set; } = null!;
        public ConcurrentBag<RequestMetric> Metrics { get; set; } = null!;

        public void Dispose()
        {
            HttpClient?.Dispose();
        }
    }

    private class RequestMetric
    {
        public DateTime Timestamp { get; set; }
        public string ActionName { get; set; } = string.Empty;
        public double ResponseTime { get; set; }
        public bool Success { get; set; }
        public int UserId { get; set; }
        public int StatusCode { get; set; }
        public string? ErrorMessage { get; set; }
    }

    private class ResourceSnapshot
    {
        public DateTime Timestamp { get; set; }
        public long MemoryUsage { get; set; }
        public double CpuUsage { get; set; }
        public int ThreadCount { get; set; }
        public int HandleCount { get; set; }
        public int GCGen0 { get; set; }
        public int GCGen1 { get; set; }
        public int GCGen2 { get; set; }
    }

    #endregion
}
