using QuestPDF.Fluent;
using QuestPDF.Helpers;
using QuestPDF.Infrastructure;
using System.Globalization;
using WhimLabAI.Abstractions.Infrastructure;
using WhimLabAI.Shared.Dtos.Invoice;

namespace WhimLabAI.Infrastructure.Pdf;

/// <summary>
/// PDF服务实现
/// </summary>
public class PdfService : IPdfService
{
    static PdfService()
    {
        // 配置QuestPDF许可证（社区版本免费用于大多数用途）
        QuestPDF.Settings.License = LicenseType.Community;
    }

    /// <summary>
    /// 生成发票PDF
    /// </summary>
    public async Task<byte[]> GenerateInvoicePdfAsync(InvoiceDto invoice, CancellationToken cancellationToken = default)
    {
        var document = Document.Create(container =>
        {
            container.Page(page =>
            {
                page.Size(PageSizes.A4);
                page.Margin(2, Unit.Centimetre);
                page.PageColor(Colors.White);
                page.DefaultTextStyle(x => x.FontSize(11).FontColor(Colors.Black));

                page.Header()
                    .Height(100)
                    .Element(container => BuildInvoiceHeader(container, invoice));

                page.Content()
                    .PaddingVertical(1, Unit.Centimetre)
                    .Element(container => BuildInvoiceContent(container, invoice));

                page.Footer()
                    .Height(50)
                    .AlignCenter()
                    .Text(text =>
                    {
                        text.CurrentPageNumber();
                        text.Span(" / ");
                        text.TotalPages();
                    });
            });
        });

        var pdfBytes = document.GeneratePdf();
        return await Task.FromResult(pdfBytes);
    }

    private void BuildInvoiceHeader(IContainer container, InvoiceDto invoice)
    {
        container.Row(row =>
        {
            row.RelativeItem().Column(column =>
            {
                column.Item().Text("INVOICE")
                    .FontSize(24)
                    .Bold()
                    .FontColor(Colors.Blue.Darken2);

                column.Item().Text($"Invoice #: {invoice.InvoiceNumber}")
                    .FontSize(14)
                    .SemiBold();

                column.Item().Text($"Date: {invoice.IssueDate:yyyy-MM-dd}")
                    .FontSize(12);

                column.Item().Text($"Due Date: {invoice.DueDate:yyyy-MM-dd}")
                    .FontSize(12);
            });

            row.ConstantItem(200).Column(column =>
            {
                if (invoice.CompanyInfo != null)
                {
                    column.Item().Text(invoice.CompanyInfo.CompanyName)
                        .FontSize(14)
                        .Bold()
                        .AlignRight();

                    column.Item().Text($"Tax ID: {invoice.CompanyInfo.TaxId}")
                        .FontSize(10)
                        .AlignRight();

                    column.Item().Text(invoice.CompanyInfo.Address)
                        .FontSize(10)
                        .AlignRight();

                    column.Item().Text(invoice.CompanyInfo.Phone)
                        .FontSize(10)
                        .AlignRight();
                }
            });
        });
    }

    private void BuildInvoiceContent(IContainer container, InvoiceDto invoice)
    {
        container.Column(column =>
        {
            // Customer info
            column.Item().PaddingVertical(10).Row(row =>
            {
                row.RelativeItem().Column(col =>
                {
                    col.Item().Text("Bill To:")
                        .FontSize(12)
                        .Bold();
                    col.Item().Text($"Customer ID: {invoice.CustomerUserId}")
                        .FontSize(10);
                });
            });

            // Invoice items table
            column.Item().PaddingVertical(10).Table(table =>
            {
                table.ColumnsDefinition(columns =>
                {
                    columns.RelativeColumn(3);
                    columns.ConstantColumn(60);
                    columns.ConstantColumn(80);
                    columns.ConstantColumn(100);
                });

                // Header
                table.Header(header =>
                {
                    header.Cell().Element(CellStyle).Text("Description").Bold();
                    header.Cell().Element(CellStyle).AlignRight().Text("Qty").Bold();
                    header.Cell().Element(CellStyle).AlignRight().Text("Unit Price").Bold();
                    header.Cell().Element(CellStyle).AlignRight().Text("Total").Bold();

                    static IContainer CellStyle(IContainer container)
                    {
                        return container.DefaultTextStyle(x => x.SemiBold())
                            .PaddingVertical(5)
                            .BorderBottom(1)
                            .BorderColor(Colors.Black);
                    }
                });

                // Items
                foreach (var item in invoice.Items)
                {
                    table.Cell().Element(CellStyle).Text(item.Description);
                    table.Cell().Element(CellStyle).AlignRight().Text(item.Quantity.ToString());
                    table.Cell().Element(CellStyle).AlignRight().Text(item.UnitPrice.ToString("C", CultureInfo.GetCultureInfo("zh-CN")));
                    table.Cell().Element(CellStyle).AlignRight().Text(item.TotalAmount.ToString("C", CultureInfo.GetCultureInfo("zh-CN")));

                    static IContainer CellStyle(IContainer container)
                    {
                        return container.BorderBottom(1).BorderColor(Colors.Grey.Lighten2).PaddingVertical(5);
                    }
                }
            });

            // Totals
            column.Item().AlignRight().Width(250).Column(col =>
            {
                col.Item().Row(row =>
                {
                    row.RelativeItem().Text("Subtotal:").AlignRight();
                    row.ConstantItem(100).Text(invoice.SubtotalAmount.ToString("C", CultureInfo.GetCultureInfo("zh-CN"))).AlignRight();
                });

                col.Item().Row(row =>
                {
                    row.RelativeItem().Text("Tax:").AlignRight();
                    row.ConstantItem(100).Text(invoice.TaxAmount.ToString("C", CultureInfo.GetCultureInfo("zh-CN"))).AlignRight();
                });

                col.Item().PaddingTop(5).Row(row =>
                {
                    row.RelativeItem().Text("Total:").Bold().AlignRight();
                    row.ConstantItem(100).Text(invoice.TotalAmount.ToString("C", CultureInfo.GetCultureInfo("zh-CN")))
                        .Bold()
                        .AlignRight();
                });
            });

            // Status
            column.Item().PaddingTop(20).Row(row =>
            {
                row.RelativeItem().Text($"Status: {invoice.Status}")
                    .FontColor(GetStatusColor(invoice.Status.ToString()))
                    .Bold();
            });
        });
    }

    private string GetStatusColor(string status)
    {
        return status.ToLower() switch
        {
            "paid" => Colors.Green.Darken2,
            "issued" => Colors.Blue.Darken2,
            "draft" => Colors.Grey.Darken1,
            "overdue" => Colors.Red.Darken2,
            "cancelled" => Colors.Grey.Darken2,
            "refunded" => Colors.Purple.Darken2,
            _ => Colors.Black
        };
    }

    /// <summary>
    /// 生成报告PDF
    /// </summary>
    public async Task<byte[]> GenerateReportPdfAsync(string reportContent, string title, CancellationToken cancellationToken = default)
    {
        var document = Document.Create(container =>
        {
            container.Page(page =>
            {
                page.Size(PageSizes.A4);
                page.Margin(2, Unit.Centimetre);
                page.PageColor(Colors.White);
                page.DefaultTextStyle(x => x.FontSize(11));

                page.Header()
                    .Height(60)
                    .Element(container =>
                    {
                        container.Column(column =>
                        {
                            column.Item().Text(title)
                                .FontSize(20)
                                .Bold()
                                .FontColor(Colors.Blue.Darken2);

                            column.Item().PaddingTop(5).Text($"Generated: {DateTime.UtcNow:yyyy-MM-dd HH:mm:ss} UTC")
                                .FontSize(10)
                                .FontColor(Colors.Grey.Darken2);
                        });
                    });

                page.Content()
                    .PaddingVertical(1, Unit.Centimetre)
                    .Column(column =>
                    {
                        // 解析报告内容，支持基本的格式化
                        var lines = reportContent.Split('\n');
                        foreach (var line in lines)
                        {
                            if (string.IsNullOrWhiteSpace(line))
                            {
                                column.Item().PaddingBottom(10);
                                continue;
                            }

                            // 检测是否是标题（以#开头）
                            if (line.TrimStart().StartsWith("#"))
                            {
                                var headerLevel = line.TakeWhile(c => c == '#').Count();
                                var headerText = line.TrimStart('#').Trim();
                                
                                column.Item().PaddingBottom(5).Text(headerText)
                                    .FontSize(20 - headerLevel * 2)
                                    .Bold();
                            }
                            // 检测是否是列表项（以-或*开头）
                            else if (line.TrimStart().StartsWith("-") || line.TrimStart().StartsWith("*"))
                            {
                                var listItemText = line.TrimStart('-', '*').Trim();
                                column.Item().Row(row =>
                                {
                                    row.ConstantItem(20).Text("•");
                                    row.RelativeItem().Text(listItemText);
                                });
                            }
                            // 普通段落
                            else
                            {
                                column.Item().Text(line).LineHeight(1.5f);
                            }
                        }
                    });

                page.Footer()
                    .Height(50)
                    .Element(container =>
                    {
                        container.Row(row =>
                        {
                            row.RelativeItem().Text(text =>
                            {
                                text.Span("WhimLabAI Report").FontSize(9).FontColor(Colors.Grey.Darken1);
                            });

                            row.ConstantItem(100).AlignRight().Text(text =>
                            {
                                text.Span("Page ");
                                text.CurrentPageNumber();
                                text.Span(" of ");
                                text.TotalPages();
                            });
                        });
                    });
            });
        });

        var pdfBytes = document.GeneratePdf();
        return await Task.FromResult(pdfBytes);
    }
}