using System;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using WhimLabAI.Abstractions.Infrastructure;
using WhimLabAI.Domain.Repositories;
using WhimLabAI.Infrastructure.Sms;
using WhimLabAI.Infrastructure.Security;

namespace WhimLabAI.Infrastructure.BackgroundServices;

/// <summary>
/// SMS重试后台服务
/// </summary>
public class SmsRetryService : BackgroundService
{
    private readonly IServiceProvider _serviceProvider;
    private readonly ILogger<SmsRetryService> _logger;
    private readonly SmsRetryOptions _options;

    public SmsRetryService(
        IServiceProvider serviceProvider,
        IOptions<SmsRetryOptions> options,
        ILogger<SmsRetryService> logger)
    {
        _serviceProvider = serviceProvider;
        _options = options.Value;
        _logger = logger;
    }

    protected override async Task ExecuteAsync(CancellationToken stoppingToken)
    {
        while (!stoppingToken.IsCancellationRequested)
        {
            try
            {
                await ProcessPendingRetries(stoppingToken);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error processing SMS retries");
            }

            await Task.Delay(TimeSpan.FromMinutes(_options.RetryIntervalMinutes), stoppingToken);
        }
    }

    private async Task ProcessPendingRetries(CancellationToken cancellationToken)
    {
        using var scope = _serviceProvider.CreateScope();
        var smsLogRepository = scope.ServiceProvider.GetRequiredService<ISmsLogRepository>();
        var smsService = scope.ServiceProvider.GetRequiredService<ISmsService>();

        var pendingSms = await smsLogRepository.GetPendingRetryAsync(
            _options.MaxRetryCount, 
            _options.BatchSize, 
            cancellationToken);

        if (!pendingSms.Any())
        {
            return;
        }

        _logger.LogInformation("Processing {Count} pending SMS retries", pendingSms.Count);

        foreach (var sms in pendingSms)
        {
            if (cancellationToken.IsCancellationRequested)
                break;

            try
            {
                // 增加重试计数
                sms.IncrementRetryCount();

                // 根据类型重试
                SmsSendResult result;
                if (sms.Type == "VerificationCode" && !string.IsNullOrEmpty(sms.VerificationCode))
                {
                    // 对于验证码，检查是否已过期
                    if (sms.IsExpired())
                    {
                        sms.MarkAsFailed("EXPIRED", "Verification code expired");
                        smsLogRepository.Update(sms);
                        continue;
                    }

                    // 解密验证码并重发
                    var encryptionService = scope.ServiceProvider.GetRequiredService<IDataEncryptionService>();
                    var code = encryptionService.DecryptString(sms.VerificationCode);
                    
                    result = await smsService.SendVerificationCodeAsync(
                        encryptionService.DecryptString(sms.PhoneNumber),
                        code,
                        sms.Purpose,
                        cancellationToken);
                }
                else if (!string.IsNullOrEmpty(sms.TemplateId) && !string.IsNullOrEmpty(sms.TemplateParams))
                {
                    // 模板短信
                    var templateParams = System.Text.Json.JsonSerializer.Deserialize<Dictionary<string, string>>(sms.TemplateParams);
                    
                    result = await smsService.SendNotificationAsync(
                        scope.ServiceProvider.GetRequiredService<IDataEncryptionService>().DecryptString(sms.PhoneNumber),
                        sms.TemplateId,
                        templateParams ?? new Dictionary<string, string>(),
                        cancellationToken);
                }
                else
                {
                    // 无法重试
                    sms.MarkAsFailed("INVALID_RETRY", "Cannot retry this SMS type");
                    smsLogRepository.Update(sms);
                    continue;
                }

                // 更新状态
                if (result.Success)
                {
                    sms.MarkAsSent(result.MessageId, result.Cost);
                    _logger.LogInformation("SMS retry successful: {MessageId}", result.MessageId);
                }
                else
                {
                    if (sms.RetryCount >= _options.MaxRetryCount)
                    {
                        sms.MarkAsFailed("MAX_RETRY", $"Max retry count reached. Last error: {result.ErrorMessage}");
                        _logger.LogWarning("SMS retry failed after max attempts: {PhoneMask}", sms.PhoneNumberMask);
                    }
                    else
                    {
                        sms.MarkAsFailed(result.ErrorCode ?? "RETRY_FAIL", result.ErrorMessage ?? "Retry failed");
                    }
                }

                smsLogRepository.Update(sms);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrying SMS: {SmsId}", sms.Id);
                sms.MarkAsFailed("RETRY_ERROR", ex.Message);
                smsLogRepository.Update(sms);
            }
        }

        // Note: SaveChanges should be called by the unit of work or the service that uses this
    }
}

/// <summary>
/// SMS重试配置选项
/// </summary>
public class SmsRetryOptions
{
    /// <summary>
    /// 重试间隔（分钟）
    /// </summary>
    public int RetryIntervalMinutes { get; set; } = 5;
    
    /// <summary>
    /// 最大重试次数
    /// </summary>
    public int MaxRetryCount { get; set; } = 3;
    
    /// <summary>
    /// 批处理大小
    /// </summary>
    public int BatchSize { get; set; } = 50;
}