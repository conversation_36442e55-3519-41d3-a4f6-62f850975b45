using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using WhimLabAI.Domain.Repositories;

namespace WhimLabAI.Infrastructure.BackgroundServices;

/// <summary>
/// Background service that automatically resumes paused subscriptions when their resume date is reached
/// </summary>
public class SubscriptionAutoResumeService : BackgroundService
{
    private readonly IServiceProvider _serviceProvider;
    private readonly ILogger<SubscriptionAutoResumeService> _logger;
    private readonly TimeSpan _checkInterval = TimeSpan.FromMinutes(15); // Check every 15 minutes

    public SubscriptionAutoResumeService(
        IServiceProvider serviceProvider,
        ILogger<SubscriptionAutoResumeService> logger)
    {
        _serviceProvider = serviceProvider;
        _logger = logger;
    }

    protected override async Task ExecuteAsync(CancellationToken stoppingToken)
    {
        _logger.LogInformation("Subscription Auto-Resume Service started");

        while (!stoppingToken.IsCancellationRequested)
        {
            try
            {
                await ProcessAutoResumes(stoppingToken);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error processing subscription auto-resumes");
            }

            await Task.Delay(_checkInterval, stoppingToken);
        }

        _logger.LogInformation("Subscription Auto-Resume Service stopped");
    }

    private async Task ProcessAutoResumes(CancellationToken cancellationToken)
    {
        using var scope = _serviceProvider.CreateScope();
        var unitOfWork = scope.ServiceProvider.GetRequiredService<IUnitOfWork>();
        var subscriptionRepository = unitOfWork.Repository<Domain.Entities.Subscription.Subscription>() as ISubscriptionRepository;
        
        if (subscriptionRepository == null)
        {
            _logger.LogError("Unable to get ISubscriptionRepository from unit of work");
            return;
        }

        try
        {
            // Get all subscriptions that need to be auto-resumed
            var subscriptionsToResume = await subscriptionRepository.GetSubscriptionsToAutoResumeAsync(cancellationToken);
            
            var resumeCount = 0;
            foreach (var subscription in subscriptionsToResume)
            {
                if (cancellationToken.IsCancellationRequested)
                    break;

                try
                {
                    var success = await subscriptionRepository.ResumeSubscriptionAsync(subscription.Id, cancellationToken);
                    if (success)
                    {
                        resumeCount++;
                        _logger.LogInformation("Auto-resumed subscription {SubscriptionId} for user {UserId}", 
                            subscription.Id, subscription.CustomerUserId);
                    }
                    else
                    {
                        _logger.LogWarning("Failed to auto-resume subscription {SubscriptionId}", subscription.Id);
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error auto-resuming subscription {SubscriptionId}", subscription.Id);
                }
            }

            if (resumeCount > 0)
            {
                _logger.LogInformation("Auto-resumed {Count} subscriptions", resumeCount);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error querying subscriptions to auto-resume");
            throw;
        }
    }
}