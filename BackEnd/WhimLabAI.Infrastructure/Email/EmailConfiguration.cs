namespace WhimLabAI.Infrastructure.Email;

/// <summary>
/// 邮件配置选项
/// </summary>
public class EmailConfiguration
{
    /// <summary>
    /// 配置节名称
    /// </summary>
    public const string SectionName = "Email";
    
    /// <summary>
    /// SMTP配置
    /// </summary>
    public SmtpConfiguration Smtp { get; set; } = new();
    
    /// <summary>
    /// 发件人配置
    /// </summary>
    public FromConfiguration From { get; set; } = new();
    
    /// <summary>
    /// 重试配置
    /// </summary>
    public int MaxRetryAttempts { get; set; } = 3;
    
    /// <summary>
    /// 重试延迟（秒）
    /// </summary>
    public int RetryDelaySeconds { get; set; } = 2;
    
    /// <summary>
    /// 是否启用邮件发送（用于测试环境）
    /// </summary>
    public bool Enabled { get; set; } = true;
    
    /// <summary>
    /// 是否记录邮件内容（仅用于调试）
    /// </summary>
    public bool LogEmailContent { get; set; } = false;
}

/// <summary>
/// SMTP配置
/// </summary>
public class SmtpConfiguration
{
    /// <summary>
    /// SMTP服务器地址
    /// </summary>
    public string Host { get; set; } = "smtp.gmail.com";
    
    /// <summary>
    /// SMTP端口
    /// </summary>
    public int Port { get; set; } = 587;
    
    /// <summary>
    /// 是否使用SSL
    /// </summary>
    public bool UseSsl { get; set; } = true;
    
    /// <summary>
    /// SMTP用户名
    /// </summary>
    public string Username { get; set; } = "";
    
    /// <summary>
    /// SMTP密码
    /// </summary>
    public string Password { get; set; } = "";
    
    /// <summary>
    /// 连接超时（毫秒）
    /// </summary>
    public int Timeout { get; set; } = 30000;
}

/// <summary>
/// 发件人配置
/// </summary>
public class FromConfiguration
{
    /// <summary>
    /// 发件人名称
    /// </summary>
    public string Name { get; set; } = "WhimLab AI";
    
    /// <summary>
    /// 发件人邮箱地址
    /// </summary>
    public string Address { get; set; } = "<EMAIL>";
}