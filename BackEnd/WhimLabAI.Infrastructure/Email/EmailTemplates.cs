using System;
using System.Collections.Generic;

namespace WhimLabAI.Infrastructure.Email;

/// <summary>
/// 邮件模板管理
/// </summary>
public static class EmailTemplates
{
    /// <summary>
    /// 获取验证邮件模板
    /// </summary>
    public static (string subject, string body) GetVerificationEmailTemplate(string verificationCode, int expirationMinutes = 30)
    {
        var subject = "验证您的 WhimLab AI 账户";
        var body = $@"
<!DOCTYPE html>
<html>
<head>
    <style>
        body {{ font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif; line-height: 1.6; color: #333; }}
        .container {{ max-width: 600px; margin: 0 auto; padding: 20px; }}
        .header {{ background-color: #4f46e5; color: white; padding: 30px; text-align: center; border-radius: 8px 8px 0 0; }}
        .content {{ background-color: #f9fafb; padding: 30px; border-radius: 0 0 8px 8px; }}
        .code-box {{ background-color: #e5e7eb; padding: 20px; margin: 20px 0; text-align: center; border-radius: 8px; }}
        .code {{ font-size: 32px; font-weight: bold; letter-spacing: 8px; color: #4f46e5; }}
        .footer {{ margin-top: 30px; text-align: center; color: #6b7280; font-size: 14px; }}
        .button {{ display: inline-block; padding: 12px 24px; background-color: #4f46e5; color: white; text-decoration: none; border-radius: 6px; margin-top: 20px; }}
    </style>
</head>
<body>
    <div class='container'>
        <div class='header'>
            <h1>WhimLab AI</h1>
            <p>邮箱验证</p>
        </div>
        <div class='content'>
            <h2>您好！</h2>
            <p>感谢您注册 WhimLab AI。请使用以下验证码完成邮箱验证：</p>
            <div class='code-box'>
                <div class='code'>{verificationCode}</div>
            </div>
            <p>验证码有效期为 <strong>{expirationMinutes} 分钟</strong>，请尽快完成验证。</p>
            <p>如果您没有注册 WhimLab AI 账户，请忽略此邮件。</p>
            <div class='footer'>
                <p>此邮件由系统自动发送，请勿回复。</p>
                <p>&copy; {DateTime.Now.Year} WhimLab AI. All rights reserved.</p>
            </div>
        </div>
    </div>
</body>
</html>";
        
        return (subject, body);
    }
    
    /// <summary>
    /// 获取密码重置邮件模板
    /// </summary>
    public static (string subject, string body) GetPasswordResetEmailTemplate(string resetToken, string resetLink, int expirationHours = 24)
    {
        var subject = "重置您的 WhimLab AI 密码";
        var body = $@"
<!DOCTYPE html>
<html>
<head>
    <style>
        body {{ font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif; line-height: 1.6; color: #333; }}
        .container {{ max-width: 600px; margin: 0 auto; padding: 20px; }}
        .header {{ background-color: #dc2626; color: white; padding: 30px; text-align: center; border-radius: 8px 8px 0 0; }}
        .content {{ background-color: #f9fafb; padding: 30px; border-radius: 0 0 8px 8px; }}
        .button {{ display: inline-block; padding: 14px 28px; background-color: #dc2626; color: white; text-decoration: none; border-radius: 6px; margin: 20px 0; font-weight: 500; }}
        .security-notice {{ background-color: #fef3c7; padding: 15px; border-radius: 6px; margin: 20px 0; border-left: 4px solid #f59e0b; }}
        .footer {{ margin-top: 30px; text-align: center; color: #6b7280; font-size: 14px; }}
        .code {{ font-family: monospace; background-color: #e5e7eb; padding: 2px 6px; border-radius: 3px; }}
    </style>
</head>
<body>
    <div class='container'>
        <div class='header'>
            <h1>WhimLab AI</h1>
            <p>密码重置请求</p>
        </div>
        <div class='content'>
            <h2>密码重置请求</h2>
            <p>我们收到了您的密码重置请求。点击下面的按钮设置新密码：</p>
            <div style='text-align: center;'>
                <a href='{resetLink}' class='button'>重置密码</a>
            </div>
            <p>或者，您也可以复制以下链接到浏览器中访问：</p>
            <p style='word-break: break-all; background-color: #f3f4f6; padding: 10px; border-radius: 4px;'>{resetLink}</p>
            <div class='security-notice'>
                <strong>安全提示：</strong>
                <ul style='margin: 10px 0; padding-left: 20px;'>
                    <li>此链接将在 <strong>{expirationHours} 小时</strong>后失效</li>
                    <li>如果您没有请求重置密码，请忽略此邮件，您的密码不会被更改</li>
                    <li>请不要将此链接分享给任何人</li>
                </ul>
            </div>
            <p>如需帮助，请联系我们的客服团队。</p>
            <div class='footer'>
                <p>此邮件由系统自动发送，请勿回复。</p>
                <p>&copy; {DateTime.Now.Year} WhimLab AI. All rights reserved.</p>
            </div>
        </div>
    </div>
</body>
</html>";
        
        return (subject, body);
    }
    
    /// <summary>
    /// 获取欢迎邮件模板
    /// </summary>
    public static (string subject, string body) GetWelcomeEmailTemplate(string userName, Dictionary<string, string>? additionalData = null)
    {
        var subject = "欢迎加入 WhimLab AI！";
        var subscriptionInfo = additionalData?.GetValueOrDefault("subscriptionPlan", "免费版");
        var body = $@"
<!DOCTYPE html>
<html>
<head>
    <style>
        body {{ font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif; line-height: 1.6; color: #333; }}
        .container {{ max-width: 600px; margin: 0 auto; padding: 20px; }}
        .header {{ background-color: #10b981; color: white; padding: 40px; text-align: center; border-radius: 8px 8px 0 0; }}
        .content {{ background-color: #f9fafb; padding: 30px; border-radius: 0 0 8px 8px; }}
        .feature-box {{ background-color: white; padding: 20px; margin: 20px 0; border-radius: 8px; box-shadow: 0 1px 3px rgba(0,0,0,0.1); }}
        .button {{ display: inline-block; padding: 14px 28px; background-color: #10b981; color: white; text-decoration: none; border-radius: 6px; margin: 20px 0; font-weight: 500; }}
        .footer {{ margin-top: 30px; text-align: center; color: #6b7280; font-size: 14px; }}
        .feature-list {{ list-style: none; padding: 0; }}
        .feature-list li {{ padding: 8px 0; padding-left: 24px; position: relative; }}
        .feature-list li:before {{ content: '✓'; position: absolute; left: 0; color: #10b981; font-weight: bold; }}
    </style>
</head>
<body>
    <div class='container'>
        <div class='header'>
            <h1>欢迎来到 WhimLab AI！</h1>
            <p>开启您的AI智能体之旅</p>
        </div>
        <div class='content'>
            <h2>您好，{userName}！</h2>
            <p>恭喜您成功注册 WhimLab AI！我们很高兴您加入我们的AI社区。</p>
            
            <div class='feature-box'>
                <h3>🚀 快速开始</h3>
                <p>您的账户已经准备就绪。现在您可以：</p>
                <ul class='feature-list'>
                    <li>探索丰富的AI智能体市场</li>
                    <li>创建您自己的AI智能体</li>
                    <li>与AI进行智能对话</li>
                    <li>管理您的知识库</li>
                </ul>
            </div>
            
            <div class='feature-box'>
                <h3>📊 您的订阅计划</h3>
                <p>当前计划：<strong>{subscriptionInfo}</strong></p>
                <p>您可以随时在账户设置中升级您的订阅计划，解锁更多功能。</p>
            </div>
            
            <div style='text-align: center;'>
                <a href='https://app.whimlabai.com/dashboard' class='button'>进入控制台</a>
            </div>
            
            <div class='feature-box' style='background-color: #e0e7ff;'>
                <h3>💡 新手提示</h3>
                <p>推荐您从以下步骤开始：</p>
                <ol style='margin: 10px 0; padding-left: 20px;'>
                    <li>完善您的个人资料</li>
                    <li>浏览热门AI智能体</li>
                    <li>尝试与AI对话</li>
                    <li>查看帮助文档了解更多功能</li>
                </ol>
            </div>
            
            <p>如有任何问题，我们的客服团队随时为您提供帮助。</p>
            <p>再次欢迎您的加入！</p>
            
            <div class='footer'>
                <p>关注我们：<a href='#'>微信公众号</a> | <a href='#'>官方网站</a> | <a href='#'>帮助中心</a></p>
                <p>&copy; {DateTime.Now.Year} WhimLab AI. All rights reserved.</p>
            </div>
        </div>
    </div>
</body>
</html>";
        
        return (subject, body);
    }
    
    /// <summary>
    /// 获取发票邮件模板
    /// </summary>
    public static (string subject, string body) GetInvoiceEmailTemplate(string invoiceNumber, decimal amount, string planName)
    {
        var subject = $"您的 WhimLab AI 发票 - {invoiceNumber}";
        var body = $@"
<!DOCTYPE html>
<html>
<head>
    <style>
        body {{ font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif; line-height: 1.6; color: #333; }}
        .container {{ max-width: 600px; margin: 0 auto; padding: 20px; }}
        .header {{ background-color: #6366f1; color: white; padding: 30px; text-align: center; border-radius: 8px 8px 0 0; }}
        .content {{ background-color: #f9fafb; padding: 30px; border-radius: 0 0 8px 8px; }}
        .invoice-info {{ background-color: white; padding: 20px; margin: 20px 0; border-radius: 8px; border: 1px solid #e5e7eb; }}
        .footer {{ margin-top: 30px; text-align: center; color: #6b7280; font-size: 14px; }}
        .amount {{ font-size: 24px; font-weight: bold; color: #6366f1; }}
    </style>
</head>
<body>
    <div class='container'>
        <div class='header'>
            <h1>WhimLab AI</h1>
            <p>电子发票</p>
        </div>
        <div class='content'>
            <h2>发票已准备就绪</h2>
            <p>尊敬的客户，您的电子发票已经生成。</p>
            
            <div class='invoice-info'>
                <h3>发票详情</h3>
                <table style='width: 100%; border-collapse: collapse;'>
                    <tr>
                        <td style='padding: 10px 0; border-bottom: 1px solid #e5e7eb;'>发票号码：</td>
                        <td style='padding: 10px 0; border-bottom: 1px solid #e5e7eb; text-align: right;'><strong>{invoiceNumber}</strong></td>
                    </tr>
                    <tr>
                        <td style='padding: 10px 0; border-bottom: 1px solid #e5e7eb;'>开票日期：</td>
                        <td style='padding: 10px 0; border-bottom: 1px solid #e5e7eb; text-align: right;'>{DateTime.Now:yyyy年MM月dd日}</td>
                    </tr>
                    <tr>
                        <td style='padding: 10px 0; border-bottom: 1px solid #e5e7eb;'>服务内容：</td>
                        <td style='padding: 10px 0; border-bottom: 1px solid #e5e7eb; text-align: right;'>{planName}</td>
                    </tr>
                    <tr>
                        <td style='padding: 15px 0;'>发票金额：</td>
                        <td style='padding: 15px 0; text-align: right;' class='amount'>¥{amount:F2}</td>
                    </tr>
                </table>
            </div>
            
            <p>发票PDF文件已作为附件发送，请查收。</p>
            <p>如需要纸质发票或有其他问题，请联系我们的客服团队。</p>
            
            <div class='footer'>
                <p>感谢您使用 WhimLab AI！</p>
                <p>&copy; {DateTime.Now.Year} WhimLab AI. All rights reserved.</p>
            </div>
        </div>
    </div>
</body>
</html>";
        
        return (subject, body);
    }
}