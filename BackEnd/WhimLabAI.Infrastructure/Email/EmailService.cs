using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Net;
using System.Net.Mail;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Polly;
using Polly.Extensions.Http;
using WhimLabAI.Abstractions.Infrastructure;

namespace WhimLabAI.Infrastructure.Email;

/// <summary>
/// 邮件服务实现
/// </summary>
public class EmailService : IEmailService
{
    private readonly ILogger<EmailService> _logger;
    private readonly IConfiguration _configuration;
    private readonly string _smtpHost;
    private readonly int _smtpPort;
    private readonly bool _useSsl;
    private readonly string _smtpUsername;
    private readonly string _smtpPassword;
    private readonly string _fromName;
    private readonly string _fromAddress;
    private readonly int _maxRetryAttempts;
    private readonly int _retryDelay;
    private readonly IAsyncPolicy<bool> _retryPolicy;

    public EmailService(ILogger<EmailService> logger, IConfiguration configuration)
    {
        _logger = logger;
        _configuration = configuration;
        
        // 从配置读取SMTP设置
        _smtpHost = _configuration["Email:Smtp:Host"] ?? Environment.GetEnvironmentVariable("SMTP_HOST") ?? "smtp.gmail.com";
        _smtpPort = int.Parse(_configuration["Email:Smtp:Port"] ?? Environment.GetEnvironmentVariable("SMTP_PORT") ?? "587");
        _useSsl = bool.Parse(_configuration["Email:Smtp:UseSsl"] ?? Environment.GetEnvironmentVariable("SMTP_USE_SSL") ?? "true");
        _smtpUsername = _configuration["Email:Smtp:Username"] ?? Environment.GetEnvironmentVariable("SMTP_USERNAME") ?? "<EMAIL>";
        _smtpPassword = _configuration["Email:Smtp:Password"] ?? Environment.GetEnvironmentVariable("SMTP_PASSWORD") ?? "";
        _fromName = _configuration["Email:From:Name"] ?? Environment.GetEnvironmentVariable("EMAIL_FROM_NAME") ?? "WhimLab AI";
        _fromAddress = _configuration["Email:From:Address"] ?? Environment.GetEnvironmentVariable("EMAIL_FROM_ADDRESS") ?? _smtpUsername;
        
        // 重试配置
        _maxRetryAttempts = int.Parse(_configuration["Email:MaxRetryAttempts"] ?? "3");
        _retryDelay = int.Parse(_configuration["Email:RetryDelaySeconds"] ?? "2");
        
        // 配置重试策略
        _retryPolicy = Policy<bool>
            .HandleResult(success => !success)
            .WaitAndRetryAsync(
                _maxRetryAttempts,
                retryAttempt => TimeSpan.FromSeconds(Math.Pow(_retryDelay, retryAttempt)),
                onRetry: (outcome, timespan, retryCount, context) =>
                {
                    _logger.LogWarning(
                        "Email send attempt {RetryCount} failed. Retrying after {RetryDelay}s...",
                        retryCount, 
                        timespan.TotalSeconds);
                });
    }

    public async Task<bool> SendEmailAsync(string to, string subject, string body, bool isHtml = true, CancellationToken cancellationToken = default)
    {
        return await SendEmailAsync(new List<string> { to }, subject, body, isHtml, cancellationToken);
    }

    public async Task<bool> SendEmailAsync(List<string> to, string subject, string body, bool isHtml = true, CancellationToken cancellationToken = default)
    {
        return await _retryPolicy.ExecuteAsync(async () =>
        {
            try
            {
                using var message = new MailMessage
                {
                    From = new MailAddress(_fromAddress, _fromName),
                    Subject = subject,
                    Body = body,
                    IsBodyHtml = isHtml,
                    Priority = MailPriority.Normal
                };

                // 添加收件人
                foreach (var recipient in to)
                {
                    if (IsValidEmail(recipient))
                    {
                        message.To.Add(recipient);
                    }
                    else
                    {
                        _logger.LogWarning("Invalid email address skipped: {Email}", recipient);
                    }
                }

                if (!message.To.Any())
                {
                    _logger.LogError("No valid recipients for email");
                    return false;
                }

                // 添加邮件头部信息
                message.Headers.Add("X-Mailer", "WhimLab AI Email Service");
                message.Headers.Add("X-Priority", "3");

                using var client = new SmtpClient(_smtpHost, _smtpPort)
                {
                    Credentials = new NetworkCredential(_smtpUsername, _smtpPassword),
                    EnableSsl = _useSsl,
                    DeliveryMethod = SmtpDeliveryMethod.Network,
                    Timeout = 30000 // 30秒超时
                };

                await client.SendMailAsync(message, cancellationToken);
                
                _logger.LogInformation("Email sent successfully to {Recipients} with subject: {Subject}", 
                    string.Join(", ", to), subject);
                
                return true;
            }
            catch (SmtpException smtpEx)
            {
                _logger.LogError(smtpEx, "SMTP error while sending email to {Recipients}. Status: {StatusCode}", 
                    string.Join(", ", to), smtpEx.StatusCode);
                return false;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to send email to {Recipients} with subject: {Subject}", 
                    string.Join(", ", to), subject);
                return false;
            }
        });
    }

    public async Task<bool> SendEmailWithAttachmentsAsync(string to, string subject, string body, List<string> attachments, bool isHtml = true, CancellationToken cancellationToken = default)
    {
        try
        {
            using var message = new MailMessage
            {
                From = new MailAddress(_fromAddress, _fromName),
                Subject = subject,
                Body = body,
                IsBodyHtml = isHtml
            };

            message.To.Add(to);

            // 添加附件
            foreach (var attachmentPath in attachments)
            {
                if (System.IO.File.Exists(attachmentPath))
                {
                    var attachment = new Attachment(attachmentPath);
                    message.Attachments.Add(attachment);
                }
                else
                {
                    _logger.LogWarning("Attachment file not found: {Path}", attachmentPath);
                }
            }

            using var client = new SmtpClient(_smtpHost, _smtpPort)
            {
                Credentials = new NetworkCredential(_smtpUsername, _smtpPassword),
                EnableSsl = _useSsl,
                DeliveryMethod = SmtpDeliveryMethod.Network,
                Timeout = 60000 // 60秒超时（因为有附件）
            };

            await client.SendMailAsync(message, cancellationToken);
            
            _logger.LogInformation("Email with {AttachmentCount} attachments sent successfully to {Recipient}", 
                attachments.Count, to);
            
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to send email with attachments to {Recipient}", to);
            return false;
        }
    }

    public async Task<bool> SendTemplateEmailAsync(string to, string templateId, Dictionary<string, object> templateData, CancellationToken cancellationToken = default)
    {
        try
        {
            // 根据模板ID获取模板内容
            var (subject, body) = await GetEmailTemplateAsync(templateId, templateData, cancellationToken);
            
            if (string.IsNullOrEmpty(subject) || string.IsNullOrEmpty(body))
            {
                _logger.LogError("Email template {TemplateId} not found or empty", templateId);
                return false;
            }
            
            // 发送邮件
            return await SendEmailAsync(to, subject, body, true, cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to send template email {TemplateId} to {Recipient}", templateId, to);
            return false;
        }
    }

    private async Task<(string subject, string body)> GetEmailTemplateAsync(string templateId, Dictionary<string, object> templateData, CancellationToken cancellationToken)
    {
        // 这里可以从数据库或文件系统读取模板
        // 简单实现：基于templateId返回预定义模板
        
        await Task.CompletedTask; // 模拟异步操作
        
        return templateId switch
        {
            "welcome" => (
                "欢迎加入WhimLab AI！",
                $@"<h2>亲爱的用户，</h2>
                   <p>欢迎您注册WhimLab AI平台！</p>
                   <p>您的账号已经成功创建，现在可以开始探索我们的AI智能体了。</p>
                   <p>如有任何问题，请随时联系我们的客服团队。</p>
                   <p>祝您使用愉快！</p>
                   <p>WhimLab AI团队</p>"
            ),
            "password-reset" => (
                "重置您的WhimLab AI密码",
                $@"<h2>密码重置请求</h2>
                   <p>我们收到了您的密码重置请求。</p>
                   <p>请点击以下链接重置您的密码：</p>
                   <p><a href='{templateData.GetValueOrDefault("resetLink", "")}'>重置密码</a></p>
                   <p>此链接将在24小时后失效。</p>
                   <p>如果您没有请求重置密码，请忽略此邮件。</p>"
            ),
            "subscription-renewed" => (
                "您的WhimLab AI订阅已续费",
                $@"<h2>订阅续费成功</h2>
                   <p>您的订阅已成功续费。</p>
                   <p>套餐类型：{templateData.GetValueOrDefault("planName", "")}</p>
                   <p>有效期至：{templateData.GetValueOrDefault("expiryDate", "")}</p>
                   <p>感谢您的信任和支持！</p>"
            ),
            _ => ("", "")
        };
    }
    
    public async Task<bool> SendInvoiceEmailAsync(string to, string invoiceNumber, byte[] pdfBytes, CancellationToken cancellationToken = default)
    {
        try
        {
            var subject = $"您的WhimLab AI发票 - {invoiceNumber}";
            var body = $@"<h2>发票已准备就绪</h2>
                         <p>尊敬的客户，</p>
                         <p>您的发票 {invoiceNumber} 已经生成，请查看附件。</p>
                         <p>如有任何问题，请联系我们的客服团队。</p>
                         <p>感谢您使用WhimLab AI！</p>
                         <p>WhimLab AI团队</p>";
            
            // Create email message
            using var message = new MailMessage();
            message.From = new MailAddress(_fromAddress, _fromName);
            message.To.Add(new MailAddress(to));
            message.Subject = subject;
            message.Body = body;
            message.IsBodyHtml = true;
            
            // Attach the PDF
            using var stream = new MemoryStream(pdfBytes);
            var attachment = new Attachment(stream, $"invoice_{invoiceNumber}.pdf", "application/pdf");
            message.Attachments.Add(attachment);
            
            // Send email
            using var client = new SmtpClient(_smtpHost, _smtpPort);
            client.UseDefaultCredentials = false;
            client.Credentials = new NetworkCredential(_smtpUsername, _smtpPassword);
            client.EnableSsl = _useSsl;
            
            await client.SendMailAsync(message, cancellationToken);
            
            _logger.LogInformation("Invoice email sent successfully to {Email} for invoice {InvoiceNumber}", to, invoiceNumber);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to send invoice email to {Email} for invoice {InvoiceNumber}", to, invoiceNumber);
            return false;
        }
    }
    
    /// <summary>
    /// 发送验证邮件
    /// </summary>
    public async Task<bool> SendVerificationEmailAsync(string to, string verificationCode, int expirationMinutes = 30, CancellationToken cancellationToken = default)
    {
        try
        {
            var (subject, body) = EmailTemplates.GetVerificationEmailTemplate(verificationCode, expirationMinutes);
            return await SendEmailAsync(to, subject, body, true, cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to send verification email to {Email}", to);
            return false;
        }
    }
    
    /// <summary>
    /// 发送密码重置邮件
    /// </summary>
    public async Task<bool> SendPasswordResetEmailAsync(string to, string resetToken, string resetLink, int expirationHours = 24, CancellationToken cancellationToken = default)
    {
        try
        {
            var (subject, body) = EmailTemplates.GetPasswordResetEmailTemplate(resetToken, resetLink, expirationHours);
            return await SendEmailAsync(to, subject, body, true, cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to send password reset email to {Email}", to);
            return false;
        }
    }
    
    /// <summary>
    /// 发送欢迎邮件
    /// </summary>
    public async Task<bool> SendWelcomeEmailAsync(string to, string userName, Dictionary<string, string>? additionalData = null, CancellationToken cancellationToken = default)
    {
        try
        {
            var (subject, body) = EmailTemplates.GetWelcomeEmailTemplate(userName, additionalData);
            return await SendEmailAsync(to, subject, body, true, cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to send welcome email to {Email}", to);
            return false;
        }
    }
    
    /// <summary>
    /// 发送账号注销验证邮件
    /// </summary>
    public async Task<bool> SendAccountDeletionVerificationEmailAsync(string to, string userName, string verificationCode, CancellationToken cancellationToken = default)
    {
        try
        {
            var subject = "确认账号注销请求 - WhimLab AI";
            var body = $@"<h2>确认账号注销请求</h2>
                         <p>亲爱的 {userName}，</p>
                         <p>我们收到了您的账号注销请求。为了确保账号安全，请使用以下验证码确认您的请求：</p>
                         <p style='font-size: 24px; font-weight: bold; color: #FF6347; padding: 10px; background-color: #f0f0f0; text-align: center;'>{verificationCode}</p>
                         <p>验证码有效期为10分钟。</p>
                         <p><strong>重要提示：</strong></p>
                         <ul>
                           <li>账号注销后，您的所有数据将被永久删除且无法恢复</li>
                           <li>正在进行的订阅将被取消，剩余服务将无法使用</li>
                           <li>您将有7天的冷静期，期间可以随时取消注销请求</li>
                         </ul>
                         <p>如果这不是您本人的操作，请立即登录账号并修改密码。</p>
                         <p>WhimLab AI团队</p>";
            
            return await SendEmailAsync(to, subject, body, true, cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to send account deletion verification email to {Email}", to);
            return false;
        }
    }
    
    /// <summary>
    /// 发送账号注销提醒邮件
    /// </summary>
    public async Task<bool> SendAccountDeletionReminderEmailAsync(string to, string userName, DateTime coolingOffEndDate, CancellationToken cancellationToken = default)
    {
        try
        {
            var remainingDays = (coolingOffEndDate - DateTime.UtcNow).Days;
            var subject = $"账号注销提醒 - 还有{remainingDays}天 - WhimLab AI";
            var body = $@"<h2>账号注销提醒</h2>
                         <p>亲爱的 {userName}，</p>
                         <p>您的账号注销请求正在处理中。</p>
                         <p><strong>冷静期将于 {coolingOffEndDate:yyyy年MM月dd日} 结束</strong></p>
                         <p>在冷静期结束前，您可以随时：</p>
                         <ul>
                           <li>登录账号并取消注销请求</li>
                           <li>导出您的重要数据</li>
                           <li>处理未完成的事务</li>
                         </ul>
                         <p>冷静期结束后：</p>
                         <ul>
                           <li>账号将被冻结，无法登录</li>
                           <li>30天后，所有数据将被永久删除</li>
                           <li>删除后的数据无法恢复</li>
                         </ul>
                         <p>如需取消注销请求，请尽快登录您的账号。</p>
                         <p>WhimLab AI团队</p>";
            
            return await SendEmailAsync(to, subject, body, true, cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to send account deletion reminder email to {Email}", to);
            return false;
        }
    }
    
    /// <summary>
    /// 发送账号注销确认邮件
    /// </summary>
    public async Task<bool> SendAccountDeletionConfirmedEmailAsync(string to, string userName, DateTime coolingOffEndDate, CancellationToken cancellationToken = default)
    {
        try
        {
            var subject = "账号注销请求已确认 - WhimLab AI";
            var body = $@"<h2>账号注销请求已确认</h2>
                         <p>亲爱的 {userName}，</p>
                         <p>您的账号注销请求已成功验证并确认。</p>
                         <p><strong>重要日期：</strong></p>
                         <ul>
                           <li>冷静期结束日期：{coolingOffEndDate:yyyy年MM月dd日}</li>
                           <li>账号冻结日期：{coolingOffEndDate:yyyy年MM月dd日}</li>
                           <li>数据永久删除日期：{coolingOffEndDate.AddDays(30):yyyy年MM月dd日}</li>
                         </ul>
                         <p><strong>接下来会发生什么：</strong></p>
                         <ol>
                           <li>在接下来的7天冷静期内，您可以随时登录并取消注销请求</li>
                           <li>冷静期结束后，账号将被冻结，无法再登录</li>
                           <li>账号冻结30天后，所有数据将被永久删除</li>
                         </ol>
                         <p>如果您改变主意，请在冷静期内登录账号并取消注销请求。</p>
                         <p>感谢您使用WhimLab AI的服务。</p>
                         <p>WhimLab AI团队</p>";
            
            return await SendEmailAsync(to, subject, body, true, cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to send account deletion confirmed email to {Email}", to);
            return false;
        }
    }
    
    /// <summary>
    /// 发送账号注销取消邮件
    /// </summary>
    public async Task<bool> SendAccountDeletionCancelledEmailAsync(string to, string userName, CancellationToken cancellationToken = default)
    {
        try
        {
            var subject = "账号注销请求已取消 - WhimLab AI";
            var body = $@"<h2>账号注销请求已取消</h2>
                         <p>亲爱的 {userName}，</p>
                         <p>您的账号注销请求已成功取消。</p>
                         <p>您的账号现已恢复正常状态，可以继续使用WhimLab AI的所有服务。</p>
                         <p>如果这不是您本人的操作，请立即：</p>
                         <ul>
                           <li>修改您的账号密码</li>
                           <li>启用双重身份验证</li>
                           <li>检查账号的登录记录</li>
                         </ul>
                         <p>感谢您继续使用WhimLab AI的服务！</p>
                         <p>WhimLab AI团队</p>";
            
            return await SendEmailAsync(to, subject, body, true, cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to send account deletion cancelled email to {Email}", to);
            return false;
        }
    }
    
    /// <summary>
    /// 发送账号已冻结邮件
    /// </summary>
    public async Task<bool> SendAccountFrozenEmailAsync(string to, string userName, DateTime plannedDeletionDate, CancellationToken cancellationToken = default)
    {
        try
        {
            var subject = "账号已冻结 - WhimLab AI";
            var body = $@"<h2>您的账号已被冻结</h2>
                         <p>亲爱的 {userName}，</p>
                         <p>根据您的账号注销请求，冷静期已结束，您的账号现已被冻结。</p>
                         <p><strong>重要信息：</strong></p>
                         <ul>
                           <li>您已无法登录该账号</li>
                           <li>所有服务已停止</li>
                           <li>数据将保留至：{plannedDeletionDate:yyyy年MM月dd日}</li>
                         </ul>
                         <p><strong>如需恢复账号：</strong></p>
                         <p>在数据永久删除前，您仍可以通过以下方式恢复账号：</p>
                         <ol>
                           <li>联系客服：<EMAIL></li>
                           <li>提供身份验证信息</li>
                           <li>说明恢复账号的原因</li>
                         </ol>
                         <p>请注意：{plannedDeletionDate:yyyy年MM月dd日}之后，所有数据将被永久删除，无法恢复。</p>
                         <p>WhimLab AI团队</p>";
            
            return await SendEmailAsync(to, subject, body, true, cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to send account frozen email to {Email}", to);
            return false;
        }
    }
    
    /// <summary>
    /// 发送账号已删除邮件
    /// </summary>
    public async Task<bool> SendAccountDeletedEmailAsync(string to, string userName, CancellationToken cancellationToken = default)
    {
        try
        {
            var subject = "账号已永久删除 - WhimLab AI";
            var body = $@"<h2>账号删除完成</h2>
                         <p>亲爱的 {userName}，</p>
                         <p>根据您的请求，您的WhimLab AI账号及所有相关数据已被永久删除。</p>
                         <p><strong>已删除的内容：</strong></p>
                         <ul>
                           <li>个人资料和账号信息</li>
                           <li>所有对话记录</li>
                           <li>自定义AI代理</li>
                           <li>订阅和支付记录</li>
                           <li>所有其他用户数据</li>
                         </ul>
                         <p>此操作不可逆转，数据无法恢复。</p>
                         <p>如果您将来想要再次使用WhimLab AI，欢迎您创建新账号。</p>
                         <p>感谢您曾经使用WhimLab AI的服务。</p>
                         <p>WhimLab AI团队</p>";
            
            return await SendEmailAsync(to, subject, body, true, cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to send account deleted email to {Email}", to);
            return false;
        }
    }
    
    /// <summary>
    /// 验证邮箱地址格式
    /// </summary>
    private static bool IsValidEmail(string email)
    {
        if (string.IsNullOrWhiteSpace(email))
            return false;
        
        try
        {
            var addr = new MailAddress(email);
            return addr.Address == email;
        }
        catch
        {
            return false;
        }
    }
}