# Email Service Implementation

This directory contains the production-ready email service implementation for WhimLab AI.

## Overview

The email service provides a robust, retry-enabled email sending capability with support for:
- Basic email sending
- Template-based emails
- Verification emails
- Password reset emails
- Welcome emails
- Invoice emails with PDF attachments
- Multiple recipients
- HTML and plain text content

## Key Features

1. **Retry Logic**: Uses Polly for automatic retry with exponential backoff
2. **Configuration Flexibility**: Supports both configuration files and environment variables
3. **Email Validation**: Validates email addresses before sending
4. **Template Management**: Pre-built templates for common scenarios
5. **Comprehensive Logging**: Detailed logging for debugging and monitoring
6. **SMTP Support**: Compatible with major SMTP providers (Gmail, SendGrid, AWS SES, etc.)

## Configuration

### Using appsettings.json

```json
{
  "Email": {
    "Enabled": true,
    "LogEmailContent": false,
    "MaxRetryAttempts": 3,
    "RetryDelaySeconds": 2,
    "Smtp": {
      "Host": "smtp.gmail.com",
      "Port": 587,
      "UseSsl": true,
      "Username": "<EMAIL>",
      "Password": "your-app-password",
      "Timeout": 30000
    },
    "From": {
      "Name": "WhimLab AI",
      "Address": "<EMAIL>"
    }
  }
}
```

### Using Environment Variables

```bash
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USE_SSL=true
SMTP_USERNAME=<EMAIL>
SMTP_PASSWORD=your-app-password
EMAIL_FROM_NAME=WhimLab AI
EMAIL_FROM_ADDRESS=<EMAIL>
```

## SMTP Provider Configuration Examples

### Gmail
```bash
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USE_SSL=true
# Use App Password: https://support.google.com/accounts/answer/185833
SMTP_USERNAME=<EMAIL>
SMTP_PASSWORD=your-app-password
```

### SendGrid
```bash
SMTP_HOST=smtp.sendgrid.net
SMTP_PORT=587
SMTP_USE_SSL=true
SMTP_USERNAME=apikey
SMTP_PASSWORD=your-sendgrid-api-key
```

### Amazon SES
```bash
SMTP_HOST=email-smtp.us-west-2.amazonaws.com
SMTP_PORT=587
SMTP_USE_SSL=true
SMTP_USERNAME=your-ses-smtp-username
SMTP_PASSWORD=your-ses-smtp-password
```

### Outlook/Office365
```bash
SMTP_HOST=smtp.office365.com
SMTP_PORT=587
SMTP_USE_SSL=true
SMTP_USERNAME=<EMAIL>
SMTP_PASSWORD=your-password
```

## Usage Examples

### Basic Email
```csharp
await _emailService.SendEmailAsync(
    "<EMAIL>",
    "Subject",
    "<h1>Hello</h1><p>This is a test email.</p>",
    isHtml: true
);
```

### Verification Email
```csharp
await _emailService.SendVerificationEmailAsync(
    "<EMAIL>",
    "123456",
    expirationMinutes: 30
);
```

### Password Reset Email
```csharp
await _emailService.SendPasswordResetEmailAsync(
    "<EMAIL>",
    "reset-token",
    "https://app.whimlabai.com/reset-password?token=reset-token",
    expirationHours: 24
);
```

### Welcome Email
```csharp
await _emailService.SendWelcomeEmailAsync(
    "<EMAIL>",
    "John Doe",
    new Dictionary<string, string> { ["subscriptionPlan"] = "Pro" }
);
```

## Email Templates

Templates are defined in `EmailTemplates.cs` and include:

1. **Verification Email**: Used for email address verification during registration
2. **Password Reset Email**: Secure password reset with expiring links
3. **Welcome Email**: Onboarding email for new users
4. **Invoice Email**: Transaction receipts with PDF attachments

All templates are responsive and tested across major email clients.

## Security Considerations

1. **Password Storage**: Never store SMTP passwords in source code
2. **Environment Variables**: Use environment variables for sensitive configuration
3. **Rate Limiting**: Implement rate limiting to prevent email bombing
4. **Validation**: Always validate email addresses and content
5. **SPF/DKIM**: Configure SPF and DKIM records for your domain

## Monitoring and Debugging

1. **Logging**: All email operations are logged with appropriate levels
2. **Retry Attempts**: Failed attempts are logged with details
3. **Development Mode**: Set `LogEmailContent: true` in development to log email content
4. **Health Checks**: Monitor SMTP connectivity via health endpoints

## Testing

### Unit Tests
```csharp
// See Tests/WhimLabAI.IntegrationTests/Email/EmailServiceTests.cs
```

### Manual Testing
1. Set up environment variables or update appsettings.Development.json
2. Run integration tests with real SMTP configuration
3. Use tools like MailHog or Mailtrap for local testing

## Troubleshooting

### Common Issues

1. **Authentication Failed**
   - Check SMTP credentials
   - For Gmail, ensure you're using an App Password
   - Verify 2FA is enabled if required

2. **Connection Timeout**
   - Check firewall settings
   - Verify SMTP host and port
   - Ensure SSL/TLS settings match provider requirements

3. **Email Not Delivered**
   - Check spam folders
   - Verify sender domain reputation
   - Ensure SPF/DKIM records are configured

## Future Enhancements

1. **Email Queue**: Implement background job queue for email sending
2. **Analytics**: Track email open rates and click-through rates
3. **A/B Testing**: Support for template A/B testing
4. **Internationalization**: Multi-language email templates
5. **Webhook Support**: Handle bounce and complaint notifications