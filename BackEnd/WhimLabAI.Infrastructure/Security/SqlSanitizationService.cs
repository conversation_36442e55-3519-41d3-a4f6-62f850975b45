using System;
using System.Collections.Generic;
using System.Linq;
using System.Text.RegularExpressions;
using Microsoft.Extensions.Logging;
using WhimLabAI.Abstractions.Infrastructure;

namespace WhimLabAI.Infrastructure.Security
{
    /// <summary>
    /// Implementation of SQL injection prevention and query sanitization service
    /// </summary>
    public class SqlSanitizationService : ISqlSanitizationService
    {
        private readonly ILogger<SqlSanitizationService> _logger;
        
        // SQL injection patterns
        private static readonly Regex SqlInjectionPattern = new Regex(
            @"(\b(ALTER|CREATE|DELETE|DROP|EXEC(UTE)?|INSERT|SELECT|TRUNCATE|UPDATE|UNION)\b)|" +
            @"(--)|(/\*)|(\*/)|(@)|(')|(;)|(\|\|)|" +
            @"(CHAR|NCHAR|VARCHAR|NVARCHAR|CAST|CONVERT)|" +
            @"(xp_cmdshell|sp_executesql|OPENROWSET|OPENDATASOURCE)",
            RegexOptions.IgnoreCase | RegexOptions.Compiled);

        // Valid identifier pattern (alphanumeric and underscore only)
        private static readonly Regex ValidIdentifierPattern = new Regex(
            @"^[a-zA-Z_][a-zA-Z0-9_]*$",
            RegexOptions.Compiled);

        // LIKE pattern special characters
        private static readonly Dictionary<char, string> LikeEscapeChars = new Dictionary<char, string>
        {
            { '%', @"\%" },
            { '_', @"\_" },
            { '[', @"\[" },
            { ']', @"\]" },
            { '^', @"\^" },
            { '-', @"\-" }
        };

        public SqlSanitizationService(ILogger<SqlSanitizationService> logger)
        {
            _logger = logger;
        }

        public string SanitizeParameter(string value)
        {
            if (string.IsNullOrEmpty(value))
            {
                return value;
            }

            // Remove any potential SQL injection patterns
            if (SqlInjectionPattern.IsMatch(value))
            {
                _logger.LogWarning("Potential SQL injection attempt detected and sanitized: {Value}", value);
                // Remove dangerous characters and keywords
                value = SqlInjectionPattern.Replace(value, string.Empty);
            }

            // Escape single quotes
            value = value.Replace("'", "''");

            // Remove null bytes
            value = value.Replace("\0", string.Empty);

            // Trim whitespace
            value = value.Trim();

            return value;
        }

        public bool IsValidColumnName(string columnName, IEnumerable<string> allowedColumns)
        {
            if (string.IsNullOrWhiteSpace(columnName))
            {
                return false;
            }

            // Check against allowed list
            if (!allowedColumns.Contains(columnName, StringComparer.OrdinalIgnoreCase))
            {
                _logger.LogWarning("Invalid column name attempted: {ColumnName}", columnName);
                return false;
            }

            // Verify it matches valid identifier pattern
            if (!ValidIdentifierPattern.IsMatch(columnName))
            {
                _logger.LogWarning("Column name contains invalid characters: {ColumnName}", columnName);
                return false;
            }

            return true;
        }

        public bool IsValidTableName(string tableName, IEnumerable<string> allowedTables)
        {
            if (string.IsNullOrWhiteSpace(tableName))
            {
                return false;
            }

            // Check against allowed list
            if (!allowedTables.Contains(tableName, StringComparer.OrdinalIgnoreCase))
            {
                _logger.LogWarning("Invalid table name attempted: {TableName}", tableName);
                return false;
            }

            // Verify it matches valid identifier pattern
            if (!ValidIdentifierPattern.IsMatch(tableName))
            {
                _logger.LogWarning("Table name contains invalid characters: {TableName}", tableName);
                return false;
            }

            return true;
        }

        public bool IsSafeQuery(string query)
        {
            if (string.IsNullOrWhiteSpace(query))
            {
                return true;
            }

            // Check for multiple statements
            if (query.Contains(';') && !IsInQuotes(query, query.IndexOf(';')))
            {
                _logger.LogWarning("Query contains multiple statements");
                return false;
            }

            // Check for dangerous patterns
            if (SqlInjectionPattern.IsMatch(query))
            {
                // Allow SELECT statements if they're the primary command
                var trimmedQuery = query.Trim();
                if (!trimmedQuery.StartsWith("SELECT", StringComparison.OrdinalIgnoreCase))
                {
                    _logger.LogWarning("Query contains potentially dangerous SQL patterns");
                    return false;
                }
            }

            // Check for comment indicators outside of strings
            if (ContainsUnquotedComments(query))
            {
                _logger.LogWarning("Query contains SQL comments");
                return false;
            }

            return true;
        }

        public string EscapeLikePattern(string pattern)
        {
            if (string.IsNullOrEmpty(pattern))
            {
                return pattern;
            }

            var escaped = pattern;
            foreach (var escapeChar in LikeEscapeChars)
            {
                escaped = escaped.Replace(escapeChar.Key.ToString(), escapeChar.Value);
            }

            return escaped;
        }

        public string BuildSafeOrderByClause(string column, string direction, IEnumerable<string> allowedColumns)
        {
            // Validate column
            if (!IsValidColumnName(column, allowedColumns))
            {
                _logger.LogWarning("Invalid ORDER BY column: {Column}", column);
                return string.Empty;
            }

            // Validate direction
            direction = direction?.ToUpperInvariant() ?? "ASC";
            if (direction != "ASC" && direction != "DESC")
            {
                _logger.LogWarning("Invalid ORDER BY direction: {Direction}", direction);
                return string.Empty;
            }

            return $"{column} {direction}";
        }

        private bool IsInQuotes(string query, int position)
        {
            var quoteCount = 0;
            for (int i = 0; i < position; i++)
            {
                if (query[i] == '\'' && (i == 0 || query[i - 1] != '\\'))
                {
                    quoteCount++;
                }
            }
            return quoteCount % 2 == 1;
        }

        private bool ContainsUnquotedComments(string query)
        {
            // Check for -- comments
            var dashIndex = query.IndexOf("--");
            if (dashIndex >= 0 && !IsInQuotes(query, dashIndex))
            {
                return true;
            }

            // Check for /* */ comments
            var slashStarIndex = query.IndexOf("/*");
            if (slashStarIndex >= 0 && !IsInQuotes(query, slashStarIndex))
            {
                return true;
            }

            return false;
        }
    }
}