using System;
using System.IO;
using System.Linq;
using System.Text.Json;
using Serilog.Events;
using Serilog.Formatting;
using Serilog.Formatting.Json;

namespace WhimLabAI.Infrastructure.Logging;

/// <summary>
/// 带脱敏功能的JSON格式化器
/// </summary>
public class SanitizingJsonFormatter : ITextFormatter
{
    private readonly ILogSanitizer _sanitizer;
    private readonly JsonFormatter _innerFormatter;
    private readonly LogSanitizationOptions _options;

    public SanitizingJsonFormatter(ILogSanitizer sanitizer, LogSanitizationOptions options)
    {
        _sanitizer = sanitizer ?? throw new ArgumentNullException(nameof(sanitizer));
        _options = options ?? throw new ArgumentNullException(nameof(options));
        _innerFormatter = new JsonFormatter();
    }

    public void Format(LogEvent logEvent, TextWriter output)
    {
        if (!_options.Enabled)
        {
            _innerFormatter.Format(logEvent, output);
            return;
        }

        using var buffer = new StringWriter();
        _innerFormatter.Format(logEvent, buffer);
        
        var jsonLog = buffer.ToString();
        var sanitizedLog = SanitizeJsonLog(jsonLog);
        
        output.Write(sanitizedLog);
    }

    private string SanitizeJsonLog(string jsonLog)
    {
        try
        {
            using var doc = JsonDocument.Parse(jsonLog);
            using var stream = new MemoryStream();
            using var writer = new Utf8JsonWriter(stream, new JsonWriterOptions { Indented = false });
            
            writer.WriteStartObject();
            
            foreach (var property in doc.RootElement.EnumerateObject())
            {
                writer.WritePropertyName(property.Name);
                
                // 特殊处理某些字段
                switch (property.Name.ToLowerInvariant())
                {
                    case "message":
                    case "messagetemplate":
                        if (property.Value.ValueKind == JsonValueKind.String)
                        {
                            var message = property.Value.GetString() ?? "";
                            writer.WriteStringValue(_sanitizer.Sanitize(message));
                        }
                        else
                        {
                            property.Value.WriteTo(writer);
                        }
                        break;
                        
                    case "exception":
                        if (property.Value.ValueKind == JsonValueKind.String)
                        {
                            var exception = property.Value.GetString() ?? "";
                            writer.WriteStringValue(_sanitizer.Sanitize(exception));
                        }
                        else
                        {
                            property.Value.WriteTo(writer);
                        }
                        break;
                        
                    case "properties":
                        SanitizeProperties(property.Value, writer);
                        break;
                        
                    default:
                        // 检查是否为敏感字段
                        if (IsSensitivePropertyName(property.Name) && property.Value.ValueKind == JsonValueKind.String)
                        {
                            var value = property.Value.GetString() ?? "";
                            writer.WriteStringValue(_sanitizer.SanitizeByKey(property.Name, value));
                        }
                        else
                        {
                            property.Value.WriteTo(writer);
                        }
                        break;
                }
            }
            
            writer.WriteEndObject();
            writer.Flush();
            
            return System.Text.Encoding.UTF8.GetString(stream.ToArray());
        }
        catch
        {
            // 如果解析失败，返回原始日志
            return jsonLog;
        }
    }

    private void SanitizeProperties(JsonElement properties, Utf8JsonWriter writer)
    {
        writer.WriteStartObject();
        
        foreach (var property in properties.EnumerateObject())
        {
            writer.WritePropertyName(property.Name);
            SanitizePropertyValue(property.Name, property.Value, writer);
        }
        
        writer.WriteEndObject();
    }

    private void SanitizePropertyValue(string propertyName, JsonElement value, Utf8JsonWriter writer)
    {
        switch (value.ValueKind)
        {
            case JsonValueKind.String:
                var stringValue = value.GetString() ?? "";
                if (IsSensitivePropertyName(propertyName))
                {
                    writer.WriteStringValue(_sanitizer.SanitizeByKey(propertyName, stringValue));
                }
                else if (_sanitizer.ContainsSensitiveData(stringValue))
                {
                    writer.WriteStringValue(_sanitizer.Sanitize(stringValue));
                }
                else
                {
                    writer.WriteStringValue(stringValue);
                }
                break;
                
            case JsonValueKind.Object:
                writer.WriteStartObject();
                foreach (var prop in value.EnumerateObject())
                {
                    writer.WritePropertyName(prop.Name);
                    SanitizePropertyValue(prop.Name, prop.Value, writer);
                }
                writer.WriteEndObject();
                break;
                
            case JsonValueKind.Array:
                writer.WriteStartArray();
                foreach (var item in value.EnumerateArray())
                {
                    SanitizePropertyValue(propertyName, item, writer);
                }
                writer.WriteEndArray();
                break;
                
            default:
                value.WriteTo(writer);
                break;
        }
    }

    private bool IsSensitivePropertyName(string propertyName)
    {
        return _options.SensitiveFields.Any(field => 
            propertyName.Contains(field, StringComparison.OrdinalIgnoreCase));
    }
}