using System;
using System.IO;
using System.Linq;
using Serilog.Events;
using Serilog.Formatting;
using Serilog.Formatting.Display;

namespace WhimLabAI.Infrastructure.Logging;

/// <summary>
/// 带脱敏功能的文本格式化器
/// </summary>
public class SanitizingTextFormatter : ITextFormatter
{
    private readonly ILogSanitizer _sanitizer;
    private readonly LogSanitizationOptions _options;
    private readonly MessageTemplateTextFormatter _innerFormatter;

    public SanitizingTextFormatter(
        ILogSanitizer sanitizer, 
        LogSanitizationOptions options,
        string outputTemplate = "{Timestamp:yyyy-MM-dd HH:mm:ss.fff zzz} [{Level:u3}] {Message:lj}{NewLine}{Exception}")
    {
        _sanitizer = sanitizer ?? throw new ArgumentNullException(nameof(sanitizer));
        _options = options ?? throw new ArgumentNullException(nameof(options));
        _innerFormatter = new MessageTemplateTextFormatter(outputTemplate);
    }

    public void Format(LogEvent logEvent, TextWriter output)
    {
        if (!_options.Enabled)
        {
            _innerFormatter.Format(logEvent, output);
            return;
        }

        // 创建脱敏后的日志事件
        var sanitizedEvent = CreateSanitizedEvent(logEvent);
        
        // 使用内部格式化器格式化脱敏后的事件
        _innerFormatter.Format(sanitizedEvent, output);
    }

    private LogEvent CreateSanitizedEvent(LogEvent original)
    {
        // 脱敏消息模板
        var sanitizedMessageTemplate = _sanitizer.Sanitize(original.MessageTemplate.Text);
        var messageTemplate = new MessageTemplate(sanitizedMessageTemplate, original.MessageTemplate.Tokens);

        // 脱敏属性
        var sanitizedProperties = original.Properties
            .Select(kvp => new LogEventProperty(
                kvp.Key,
                SanitizePropertyValue(kvp.Key, kvp.Value)))
            .ToList();

        // 脱敏异常
        Exception? sanitizedException = null;
        if (original.Exception != null)
        {
            sanitizedException = CreateSanitizedException(original.Exception);
        }

        return new LogEvent(
            original.Timestamp,
            original.Level,
            sanitizedException,
            messageTemplate,
            sanitizedProperties);
    }

    private LogEventPropertyValue SanitizePropertyValue(string key, LogEventPropertyValue value)
    {
        if (value is ScalarValue scalarValue && scalarValue.Value != null)
        {
            var stringValue = scalarValue.Value.ToString();
            if (!string.IsNullOrEmpty(stringValue))
            {
                var sanitized = IsSensitivePropertyName(key)
                    ? _sanitizer.SanitizeByKey(key, stringValue)
                    : _sanitizer.Sanitize(stringValue);

                if (sanitized != stringValue)
                {
                    return new ScalarValue(sanitized);
                }
            }
        }
        
        return value;
    }

    private Exception CreateSanitizedException(Exception original)
    {
        var sanitizedMessage = _sanitizer.Sanitize(original.Message);
        var sanitizedStackTrace = original.StackTrace != null ? _sanitizer.Sanitize(original.StackTrace) : null;
        
        // 创建一个包含脱敏信息的新异常
        var sanitizedException = new SanitizedException(
            sanitizedMessage,
            original.GetType().FullName,
            sanitizedStackTrace,
            original.InnerException != null ? CreateSanitizedException(original.InnerException) : null);
        
        return sanitizedException;
    }

    private bool IsSensitivePropertyName(string propertyName)
    {
        return _options.SensitiveFields.Any(field => 
            propertyName.Contains(field, StringComparison.OrdinalIgnoreCase));
    }

    private class SanitizedException : Exception
    {
        private readonly string? _stackTrace;
        
        public SanitizedException(string message, string? originalType, string? stackTrace, Exception? innerException) 
            : base($"[{originalType}] {message}", innerException)
        {
            _stackTrace = stackTrace;
        }

        public override string? StackTrace => _stackTrace;
    }
}