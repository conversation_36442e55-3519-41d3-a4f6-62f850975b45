using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Text;
using System.Text.Json;
using System.Text.RegularExpressions;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;

namespace WhimLabAI.Infrastructure.Logging;

/// <summary>
/// 日志脱敏器实现
/// </summary>
public class LogSanitizer : ILogSanitizer
{
    private readonly LogSanitizationOptions _options;
    private readonly ILogger<LogSanitizer> _logger;
    private readonly ConcurrentDictionary<string, Regex> _compiledPatterns;
    private readonly ConcurrentDictionary<string, string> _cache;
    private readonly List<(Regex Pattern, string Replacement)> _customRules;
    private readonly object _customRulesLock = new();

    // 预编译的常用模式
    private static readonly Regex EmailPattern = new(@"\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b", RegexOptions.Compiled | RegexOptions.IgnoreCase);
    private static readonly Regex ChinesePhonePattern = new(@"\b1[3-9]\d{9}\b", RegexOptions.Compiled);
    private static readonly Regex InternationalPhonePattern = new(@"\b\+?\d{1,4}[\s.-]?\(?\d{1,4}\)?[\s.-]?\d{1,4}[\s.-]?\d{1,9}\b", RegexOptions.Compiled);
    private static readonly Regex CreditCardPattern = new(@"\b(?:\d{4}[\s\-]?){3}\d{4}\b", RegexOptions.Compiled);
    private static readonly Regex ChineseIdCardPattern = new(@"\b\d{6}(19|20)\d{2}(0[1-9]|1[0-2])(0[1-9]|[12]\d|3[01])\d{3}[\dXx]\b", RegexOptions.Compiled);
    private static readonly Regex JwtPattern = new(@"Bearer\s+[A-Za-z0-9\-._~\+\/]+=*", RegexOptions.Compiled | RegexOptions.IgnoreCase);
    private static readonly Regex ApiKeyPattern = new(@"(?i)(api[_-]?key|apikey|x-api-key)\s*[:=]\s*['\""]*([a-zA-Z0-9\-._]+)['\""]*", RegexOptions.Compiled);
    private static readonly Regex ConnectionStringPasswordPattern = new(@"(Password|Pwd|User ID|UID)\s*=\s*([^;]+)(;|$)", RegexOptions.Compiled | RegexOptions.IgnoreCase);
    private static readonly Regex Base64Pattern = new(@"(?:[A-Za-z0-9+\/]{4})*(?:[A-Za-z0-9+\/]{2}==|[A-Za-z0-9+\/]{3}=|[A-Za-z0-9+\/]{4})", RegexOptions.Compiled);

    public LogSanitizer(IOptions<LogSanitizationOptions> options, ILogger<LogSanitizer> logger)
    {
        _options = options.Value;
        _logger = logger;
        _compiledPatterns = new ConcurrentDictionary<string, Regex>();
        _cache = new ConcurrentDictionary<string, string>();
        _customRules = new List<(Regex, string)>();
        
        InitializePatterns();
    }

    private void InitializePatterns()
    {
        foreach (var pattern in _options.CustomPatterns)
        {
            try
            {
                var regex = new Regex(pattern.Key, RegexOptions.Compiled | RegexOptions.Multiline);
                _compiledPatterns[pattern.Key] = regex;
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Failed to compile regex pattern: {Pattern}", pattern.Key);
            }
        }
    }

    public string Sanitize(string text)
    {
        if (!_options.Enabled || string.IsNullOrEmpty(text))
            return text;

        // 检查缓存
        if (_options.EnableCaching && _cache.Count < _options.CacheSize)
        {
            if (_cache.TryGetValue(text, out var cachedResult))
                return cachedResult;
        }

        // 性能监控
        var stopwatch = Stopwatch.StartNew();
        
        try
        {
            // 限制处理长度
            var processText = text.Length > _options.MaxProcessingLength 
                ? text.Substring(0, _options.MaxProcessingLength) 
                : text;

            var result = SanitizeInternal(processText);
            
            // 如果截断了，添加标记
            if (text.Length > _options.MaxProcessingLength)
            {
                result += "...[TRUNCATED]";
            }

            // 缓存结果
            if (_options.EnableCaching && _cache.Count < _options.CacheSize)
            {
                _cache.TryAdd(text, result);
            }

            return result;
        }
        finally
        {
            stopwatch.Stop();
            if (stopwatch.ElapsedMilliseconds > _options.PerformanceThresholdMs)
            {
                _logger.LogWarning("Log sanitization took {ElapsedMs}ms for text of length {Length}", 
                    stopwatch.ElapsedMilliseconds, text.Length);
            }
        }
    }

    private string SanitizeInternal(string text)
    {
        var result = text;

        // 1. 应用预定义的模式
        result = SanitizeEmails(result);
        result = SanitizePhoneNumbers(result);
        result = SanitizeCreditCards(result);
        result = SanitizeChineseIdCards(result);
        result = SanitizeJwtTokens(result);
        result = SanitizeApiKeys(result);
        result = SanitizeConnectionStrings(result);
        
        // 2. 应用自定义模式
        foreach (var kvp in _options.CustomPatterns)
        {
            if (_compiledPatterns.TryGetValue(kvp.Key, out var regex))
            {
                result = regex.Replace(result, kvp.Value);
            }
        }
        
        // 3. 应用用户自定义规则
        lock (_customRulesLock)
        {
            foreach (var (pattern, replacement) in _customRules)
            {
                result = pattern.Replace(result, replacement);
            }
        }

        return result;
    }

    private string SanitizeEmails(string text)
    {
        return EmailPattern.Replace(text, match =>
        {
            var email = match.Value;
            var parts = email.Split('@');
            if (parts.Length != 2) return "***@***.***";
            
            var localPart = parts[0];
            var domain = parts[1];
            
            // 保留前两个字符和域名的第一部分
            var maskedLocal = localPart.Length > 2 
                ? localPart.Substring(0, 2) + new string('*', Math.Min(localPart.Length - 2, 5))
                : new string('*', localPart.Length);
                
            var domainParts = domain.Split('.');
            var maskedDomain = domainParts[0].Length > 2
                ? domainParts[0].Substring(0, 2) + "***"
                : "***";
                
            if (domainParts.Length > 1)
                maskedDomain += "." + domainParts[domainParts.Length - 1];
                
            return $"{maskedLocal}@{maskedDomain}";
        });
    }

    private string SanitizePhoneNumbers(string text)
    {
        // 中国手机号
        text = ChinesePhonePattern.Replace(text, match =>
        {
            var phone = match.Value;
            return phone.Substring(0, 3) + "****" + phone.Substring(7);
        });
        
        // 国际电话号码
        text = InternationalPhonePattern.Replace(text, match =>
        {
            var phone = match.Value;
            var digits = new string(phone.Where(char.IsDigit).ToArray());
            if (digits.Length < 7) return new string('*', phone.Length);
            
            var visibleStart = Math.Min(3, digits.Length / 3);
            var visibleEnd = Math.Min(2, digits.Length / 4);
            var masked = digits.Substring(0, visibleStart) + 
                        new string('*', digits.Length - visibleStart - visibleEnd) +
                        digits.Substring(digits.Length - visibleEnd);
            
            return masked;
        });
        
        return text;
    }

    private string SanitizeCreditCards(string text)
    {
        return CreditCardPattern.Replace(text, match =>
        {
            var card = new string(match.Value.Where(char.IsDigit).ToArray());
            if (card.Length < 12) return "****-****-****-****";
            
            // 显示前4位和后4位
            return card.Substring(0, 4) + "-****-****-" + card.Substring(card.Length - 4);
        });
    }

    private string SanitizeChineseIdCards(string text)
    {
        return ChineseIdCardPattern.Replace(text, match =>
        {
            var id = match.Value;
            // 显示前6位（地区码）和后4位
            return id.Substring(0, 6) + "********" + id.Substring(14);
        });
    }

    private string SanitizeJwtTokens(string text)
    {
        return JwtPattern.Replace(text, "Bearer [REDACTED_JWT_TOKEN]");
    }

    private string SanitizeApiKeys(string text)
    {
        return ApiKeyPattern.Replace(text, "$1=[REDACTED_API_KEY]");
    }

    private string SanitizeConnectionStrings(string text)
    {
        return ConnectionStringPasswordPattern.Replace(text, "$1=****$3");
    }

    public object? SanitizeObject(object? obj)
    {
        if (obj == null || !_options.Enabled)
            return obj;

        try
        {
            var json = JsonSerializer.Serialize(obj);
            var sanitizedJson = SanitizeJson(json);
            return JsonSerializer.Deserialize<object>(sanitizedJson);
        }
        catch
        {
            // 如果序列化失败，返回原对象
            return obj;
        }
    }

    private string SanitizeJson(string json)
    {
        try
        {
            using var doc = JsonDocument.Parse(json);
            using var stream = new System.IO.MemoryStream();
            using var writer = new Utf8JsonWriter(stream, new JsonWriterOptions { Indented = true });
            
            SanitizeJsonElement(doc.RootElement, writer);
            
            writer.Flush();
            return Encoding.UTF8.GetString(stream.ToArray());
        }
        catch
        {
            // 如果解析失败，按普通文本处理
            return Sanitize(json);
        }
    }

    private void SanitizeJsonElement(JsonElement element, Utf8JsonWriter writer)
    {
        switch (element.ValueKind)
        {
            case JsonValueKind.Object:
                writer.WriteStartObject();
                foreach (var property in element.EnumerateObject())
                {
                    writer.WritePropertyName(property.Name);
                    
                    // 检查是否为敏感字段
                    if (IsSensitiveField(property.Name) && property.Value.ValueKind == JsonValueKind.String)
                    {
                        var value = property.Value.GetString() ?? "";
                        writer.WriteStringValue(SanitizeByKey(property.Name, value));
                    }
                    else
                    {
                        SanitizeJsonElement(property.Value, writer);
                    }
                }
                writer.WriteEndObject();
                break;
                
            case JsonValueKind.Array:
                writer.WriteStartArray();
                foreach (var item in element.EnumerateArray())
                {
                    SanitizeJsonElement(item, writer);
                }
                writer.WriteEndArray();
                break;
                
            case JsonValueKind.String:
                var strValue = element.GetString() ?? "";
                writer.WriteStringValue(Sanitize(strValue));
                break;
                
            case JsonValueKind.Number:
                if (element.TryGetInt64(out var longValue))
                    writer.WriteNumberValue(longValue);
                else if (element.TryGetDouble(out var doubleValue))
                    writer.WriteNumberValue(doubleValue);
                break;
                
            case JsonValueKind.True:
                writer.WriteBooleanValue(true);
                break;
                
            case JsonValueKind.False:
                writer.WriteBooleanValue(false);
                break;
                
            case JsonValueKind.Null:
                writer.WriteNullValue();
                break;
        }
    }

    public string SanitizeByKey(string key, string value)
    {
        if (!_options.Enabled || string.IsNullOrEmpty(value))
            return value;

        // 检查是否为部分脱敏字段
        if (_options.PartialMaskFields.Any(f => key.Contains(f, StringComparison.OrdinalIgnoreCase)))
        {
            if (key.Contains("email", StringComparison.OrdinalIgnoreCase))
            {
                return SanitizeEmails(value);
            }
            if (key.Contains("phone", StringComparison.OrdinalIgnoreCase) || 
                key.Contains("mobile", StringComparison.OrdinalIgnoreCase))
            {
                return SanitizePhoneNumbers(value);
            }
        }

        // 完全脱敏的字段
        if (IsSensitiveField(key))
        {
            // 对于特殊字段保留格式提示
            if (key.Contains("token", StringComparison.OrdinalIgnoreCase))
                return "[REDACTED_TOKEN]";
            if (key.Contains("password", StringComparison.OrdinalIgnoreCase))
                return "[REDACTED_PASSWORD]";
            if (key.Contains("key", StringComparison.OrdinalIgnoreCase))
                return "[REDACTED_KEY]";
            if (key.Contains("secret", StringComparison.OrdinalIgnoreCase))
                return "[REDACTED_SECRET]";
                
            return new string('*', Math.Min(value.Length, 20));
        }

        // 其他情况，进行常规脱敏
        return Sanitize(value);
    }

    private bool IsSensitiveField(string fieldName)
    {
        return _options.SensitiveFields.Any(f => 
            fieldName.Contains(f, StringComparison.OrdinalIgnoreCase));
    }

    public bool ContainsSensitiveData(string text)
    {
        if (string.IsNullOrEmpty(text))
            return false;

        // 检查各种敏感数据模式
        return EmailPattern.IsMatch(text) ||
               ChinesePhonePattern.IsMatch(text) ||
               InternationalPhonePattern.IsMatch(text) ||
               CreditCardPattern.IsMatch(text) ||
               ChineseIdCardPattern.IsMatch(text) ||
               JwtPattern.IsMatch(text) ||
               ApiKeyPattern.IsMatch(text) ||
               ConnectionStringPasswordPattern.IsMatch(text) ||
               ContainsBase64EncodedSecrets(text);
    }

    private bool ContainsBase64EncodedSecrets(string text)
    {
        // 检查可能的 Base64 编码的敏感数据
        var matches = Base64Pattern.Matches(text);
        foreach (Match match in matches)
        {
            if (match.Value.Length >= 20) // 只检查较长的 Base64 字符串
            {
                try
                {
                    var decoded = Convert.FromBase64String(match.Value);
                    var decodedText = Encoding.UTF8.GetString(decoded);
                    
                    // 递归检查解码后的内容
                    if (ContainsSensitiveData(decodedText))
                        return true;
                }
                catch
                {
                    // 不是有效的 Base64，继续
                }
            }
        }
        
        return false;
    }

    public void AddCustomRule(string pattern, string replacement)
    {
        try
        {
            var regex = new Regex(pattern, RegexOptions.Compiled | RegexOptions.Multiline);
            lock (_customRulesLock)
            {
                _customRules.Add((regex, replacement));
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to add custom sanitization rule: {Pattern}", pattern);
            throw;
        }
    }
}