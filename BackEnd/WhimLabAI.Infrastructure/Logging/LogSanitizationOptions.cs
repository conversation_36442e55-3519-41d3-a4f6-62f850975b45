using System.Collections.Generic;

namespace WhimLabAI.Infrastructure.Logging;

/// <summary>
/// 日志脱敏配置选项
/// </summary>
public class LogSanitizationOptions
{
    public const string SectionName = "LogSanitization";
    
    /// <summary>
    /// 是否启用日志脱敏
    /// </summary>
    public bool Enabled { get; set; } = true;
    
    /// <summary>
    /// 是否在开发环境下启用脱敏
    /// </summary>
    public bool EnabledInDevelopment { get; set; } = false;
    
    /// <summary>
    /// 敏感字段名称列表（不区分大小写）
    /// </summary>
    public List<string> SensitiveFields { get; set; } = new()
    {
        "Password", "Pwd", "Secret", "Token", "ApiKey", "Key",
        "Authorization", "Bearer", "RefreshToken", "AccessToken",
        "ConnectionString", "ConnString", "DatabasePassword",
        "ClientSecret", "PrivateKey", "Certificate",
        "CreditCard", "CardNumber", "CVV", "SecurityCode",
        "SSN", "SocialSecurityNumber", "TaxId",
        "Email", "EmailAddress", "Phone", "PhoneNumber", "Mobile",
        "IdCard", "IdentityCard", "PassportNumber",
        "BankAccount", "AccountNumber", "IBAN"
    };
    
    /// <summary>
    /// 部分脱敏的字段（如邮箱、手机号）
    /// </summary>
    public List<string> PartialMaskFields { get; set; } = new()
    {
        "Email", "EmailAddress", "Phone", "PhoneNumber", "Mobile"
    };
    
    /// <summary>
    /// 自定义脱敏规则
    /// </summary>
    public Dictionary<string, string> CustomPatterns { get; set; } = new()
    {
        // JWT Token
        [@"Bearer\s+[A-Za-z0-9\-._~\+\/]+=*"] = "Bearer [REDACTED_JWT]",
        
        // API Keys (various formats)
        [@"(?i)(api[_-]?key|apikey)\s*[:=]\s*['\""]*([a-zA-Z0-9\-._]+)['\""]*"] = "$1=REDACTED_API_KEY",
        
        // Connection Strings
        [@"(Password|Pwd|User ID|UID)=([^;]+)(;|$)"] = "$1=***;",
        
        // Credit Card Numbers (simplified pattern)
        [@"\b(?:\d{4}[\s\-]?){3}\d{4}\b"] = "****-****-****-****",
        
        // Chinese ID Card
        [@"\b\d{6}(19|20)\d{2}(0[1-9]|1[0-2])(0[1-9]|[12]\d|3[01])\d{3}[\dXx]\b"] = "******************",
        
        // Chinese Mobile Phone
        [@"\b1[3-9]\d{9}\b"] = "1**********",
        
        // URLs with credentials
        [@"(https?|ftp):\/\/[^:]+:[^@]+@"] = "$1://[REDACTED_CREDENTIALS]@"
    };
    
    /// <summary>
    /// 不同日志级别的脱敏策略
    /// </summary>
    public Dictionary<string, SanitizationLevel> LogLevelStrategies { get; set; } = new()
    {
        ["Verbose"] = SanitizationLevel.Full,
        ["Debug"] = SanitizationLevel.Full,
        ["Information"] = SanitizationLevel.Partial,
        ["Warning"] = SanitizationLevel.Partial,
        ["Error"] = SanitizationLevel.Minimal,
        ["Fatal"] = SanitizationLevel.Minimal
    };
    
    /// <summary>
    /// 最大处理长度（避免处理超长文本影响性能）
    /// </summary>
    public int MaxProcessingLength { get; set; } = 10000;
    
    /// <summary>
    /// 是否缓存脱敏结果
    /// </summary>
    public bool EnableCaching { get; set; } = true;
    
    /// <summary>
    /// 缓存大小
    /// </summary>
    public int CacheSize { get; set; } = 1000;
    
    /// <summary>
    /// 性能监控阈值（毫秒）
    /// </summary>
    public int PerformanceThresholdMs { get; set; } = 50;
}

/// <summary>
/// 脱敏级别
/// </summary>
public enum SanitizationLevel
{
    /// <summary>
    /// 不脱敏
    /// </summary>
    None,
    
    /// <summary>
    /// 最小脱敏（仅脱敏最敏感的信息）
    /// </summary>
    Minimal,
    
    /// <summary>
    /// 部分脱敏（保留部分信息用于识别）
    /// </summary>
    Partial,
    
    /// <summary>
    /// 完全脱敏（所有敏感信息都脱敏）
    /// </summary>
    Full
}