using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using Serilog.Core;
using Serilog.Events;

namespace WhimLabAI.Infrastructure.Logging;

/// <summary>
/// Serilog日志事件脱敏富集器
/// </summary>
public class SanitizingLogEventEnricher : ILogEventEnricher
{
    private readonly ILogSanitizer _sanitizer;
    private readonly LogSanitizationOptions _options;

    public SanitizingLogEventEnricher(ILogSanitizer sanitizer, LogSanitizationOptions options)
    {
        _sanitizer = sanitizer ?? throw new ArgumentNullException(nameof(sanitizer));
        _options = options ?? throw new ArgumentNullException(nameof(options));
    }

    public void Enrich(LogEvent logEvent, ILogEventPropertyFactory propertyFactory)
    {
        if (!_options.Enabled)
            return;

        // 获取当前日志级别的脱敏策略
        var sanitizationLevel = GetSanitizationLevel(logEvent.Level);
        if (sanitizationLevel == SanitizationLevel.None)
            return;

        try
        {
            // 脱敏消息模板
            if (logEvent.MessageTemplate != null)
            {
                var sanitizedMessage = _sanitizer.Sanitize(logEvent.MessageTemplate.Text);
                if (sanitizedMessage != logEvent.MessageTemplate.Text)
                {
                    logEvent.AddOrUpdateProperty(propertyFactory.CreateProperty(
                        "OriginalMessage", logEvent.MessageTemplate.Text));
                }
            }

            // 脱敏属性
            var propertiesToUpdate = new List<(string Key, LogEventPropertyValue Value)>();
            
            foreach (var property in logEvent.Properties)
            {
                var sanitizedValue = SanitizePropertyValue(property.Key, property.Value, sanitizationLevel);
                if (!ReferenceEquals(sanitizedValue, property.Value))
                {
                    propertiesToUpdate.Add((property.Key, sanitizedValue));
                }
            }

            // 更新脱敏后的属性
            foreach (var (key, value) in propertiesToUpdate)
            {
                logEvent.AddOrUpdateProperty(new LogEventProperty(key, value));
            }

            // 如果异常信息包含敏感数据，也进行脱敏
            if (logEvent.Exception != null)
            {
                var sanitizedException = SanitizeException(logEvent.Exception);
                if (sanitizedException != null)
                {
                    logEvent.AddOrUpdateProperty(propertyFactory.CreateProperty(
                        "SanitizedException", sanitizedException));
                }
            }
        }
        catch (Exception ex)
        {
            // 脱敏过程中的错误不应该影响日志记录
            logEvent.AddOrUpdateProperty(propertyFactory.CreateProperty(
                "SanitizationError", ex.Message));
        }
    }

    private SanitizationLevel GetSanitizationLevel(LogEventLevel level)
    {
        var levelName = level.ToString();
        return _options.LogLevelStrategies.TryGetValue(levelName, out var sanitizationLevel) 
            ? sanitizationLevel 
            : SanitizationLevel.Full;
    }

    private LogEventPropertyValue SanitizePropertyValue(string key, LogEventPropertyValue value, SanitizationLevel level)
    {
        switch (value)
        {
            case ScalarValue scalarValue:
                return SanitizeScalarValue(key, scalarValue, level);
                
            case SequenceValue sequenceValue:
                return SanitizeSequenceValue(key, sequenceValue, level);
                
            case StructureValue structureValue:
                return SanitizeStructureValue(structureValue, level);
                
            case DictionaryValue dictionaryValue:
                return SanitizeDictionaryValue(dictionaryValue, level);
                
            default:
                return value;
        }
    }

    private ScalarValue SanitizeScalarValue(string key, ScalarValue value, SanitizationLevel level)
    {
        if (value.Value == null)
            return value;

        var stringValue = value.Value.ToString();
        if (string.IsNullOrEmpty(stringValue))
            return value;

        // 根据级别决定脱敏策略
        string sanitized = level switch
        {
            SanitizationLevel.Minimal => IsCriticalSensitiveField(key) ? _sanitizer.SanitizeByKey(key, stringValue) : stringValue,
            SanitizationLevel.Partial => _sanitizer.SanitizeByKey(key, stringValue),
            SanitizationLevel.Full => _sanitizer.Sanitize(stringValue),
            _ => stringValue
        };

        return sanitized == stringValue ? value : new ScalarValue(sanitized);
    }

    private SequenceValue SanitizeSequenceValue(string key, SequenceValue value, SanitizationLevel level)
    {
        var sanitizedElements = value.Elements
            .Select(element => SanitizePropertyValue(key, element, level))
            .ToList();

        // 如果没有元素被修改，返回原值
        if (sanitizedElements.SequenceEqual(value.Elements))
            return value;

        return new SequenceValue(sanitizedElements);
    }

    private StructureValue SanitizeStructureValue(StructureValue value, SanitizationLevel level)
    {
        var sanitizedProperties = new List<LogEventProperty>();
        var hasChanges = false;

        foreach (var property in value.Properties)
        {
            var sanitizedValue = SanitizePropertyValue(property.Name, property.Value, level);
            if (!ReferenceEquals(sanitizedValue, property.Value))
            {
                hasChanges = true;
                sanitizedProperties.Add(new LogEventProperty(property.Name, sanitizedValue));
            }
            else
            {
                sanitizedProperties.Add(property);
            }
        }

        return hasChanges 
            ? new StructureValue(sanitizedProperties, value.TypeTag) 
            : value;
    }

    private DictionaryValue SanitizeDictionaryValue(DictionaryValue value, SanitizationLevel level)
    {
        var sanitizedElements = new List<KeyValuePair<ScalarValue, LogEventPropertyValue>>();
        var hasChanges = false;

        foreach (var element in value.Elements)
        {
            var keyString = element.Key.Value?.ToString() ?? "";
            var sanitizedValue = SanitizePropertyValue(keyString, element.Value, level);
            
            if (!ReferenceEquals(sanitizedValue, element.Value))
            {
                hasChanges = true;
                sanitizedElements.Add(new KeyValuePair<ScalarValue, LogEventPropertyValue>(element.Key, sanitizedValue));
            }
            else
            {
                sanitizedElements.Add(element);
            }
        }

        return hasChanges 
            ? new DictionaryValue(sanitizedElements) 
            : value;
    }

    private string? SanitizeException(Exception exception)
    {
        if (exception == null)
            return null;

        using var writer = new StringWriter();
        writer.WriteLine($"Exception Type: {exception.GetType().FullName}");
        writer.WriteLine($"Message: {_sanitizer.Sanitize(exception.Message)}");
        
        if (!string.IsNullOrEmpty(exception.StackTrace))
        {
            writer.WriteLine("Stack Trace:");
            writer.WriteLine(_sanitizer.Sanitize(exception.StackTrace));
        }

        if (exception.InnerException != null)
        {
            writer.WriteLine();
            writer.WriteLine("Inner Exception:");
            writer.Write(SanitizeException(exception.InnerException));
        }

        return writer.ToString();
    }

    private bool IsCriticalSensitiveField(string fieldName)
    {
        var criticalFields = new[] { "Password", "Token", "Secret", "ApiKey", "CreditCard", "SSN" };
        return criticalFields.Any(f => fieldName.Contains(f, StringComparison.OrdinalIgnoreCase));
    }
}