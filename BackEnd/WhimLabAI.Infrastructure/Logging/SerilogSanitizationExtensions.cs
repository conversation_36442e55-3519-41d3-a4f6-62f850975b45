using System;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Serilog;
using Serilog.Configuration;
using Serilog.Core;
using Serilog.Events;

namespace WhimLabAI.Infrastructure.Logging;

/// <summary>
/// Serilog日志脱敏扩展方法
/// </summary>
public static class SerilogSanitizationExtensions
{
    /// <summary>
    /// 添加日志脱敏服务
    /// </summary>
    public static IServiceCollection AddLogSanitization(this IServiceCollection services, IConfiguration configuration)
    {
        // 注册配置
        services.Configure<LogSanitizationOptions>(
            configuration.GetSection(LogSanitizationOptions.SectionName));
        
        // 注册脱敏服务
        services.AddSingleton<ILogSanitizer, LogSanitizer>();
        
        return services;
    }

    /// <summary>
    /// 配置带脱敏功能的Serilog
    /// </summary>
    public static IHostBuilder UseSerilogWithSanitization(
        this IHostBuilder hostBuilder,
        Action<LoggerConfiguration>? configureLogger = null)
    {
        return hostBuilder.UseSerilog((context, services, configuration) =>
        {
            var sanitizer = services.GetService<ILogSanitizer>();
            var options = context.Configuration.GetSection(LogSanitizationOptions.SectionName)
                .Get<LogSanitizationOptions>() ?? new LogSanitizationOptions();

            // 基础配置
            configuration
                .ReadFrom.Configuration(context.Configuration)
                .ReadFrom.Services(services);

            // 如果启用了脱敏且在生产环境或配置了在开发环境也启用
            if (sanitizer != null && options.Enabled && 
                (context.HostingEnvironment.IsProduction() || options.EnabledInDevelopment))
            {
                configuration.Enrich.With(new SanitizingLogEventEnricher(sanitizer, options));
            }

            // 应用自定义配置
            configureLogger?.Invoke(configuration);
        });
    }

    /// <summary>
    /// 为LoggerConfiguration添加脱敏富集器
    /// </summary>
    public static LoggerConfiguration WithSanitization(
        this LoggerEnrichmentConfiguration enrichmentConfiguration,
        ILogSanitizer sanitizer,
        LogSanitizationOptions? options = null)
    {
        if (enrichmentConfiguration == null)
            throw new ArgumentNullException(nameof(enrichmentConfiguration));
        
        if (sanitizer == null)
            throw new ArgumentNullException(nameof(sanitizer));

        options ??= new LogSanitizationOptions();
        
        return enrichmentConfiguration.With(new SanitizingLogEventEnricher(sanitizer, options));
    }

    /// <summary>
    /// 配置带脱敏的控制台输出
    /// </summary>
    public static LoggerConfiguration SanitizedConsole(
        this LoggerSinkConfiguration sinkConfiguration,
        ILogSanitizer sanitizer,
        LogSanitizationOptions? options = null,
        LogEventLevel restrictedToMinimumLevel = LogEventLevel.Verbose,
        string outputTemplate = "{Timestamp:yyyy-MM-dd HH:mm:ss.fff zzz} [{Level:u3}] {Message:lj}{NewLine}{Exception}")
    {
        if (sinkConfiguration == null)
            throw new ArgumentNullException(nameof(sinkConfiguration));

        options ??= new LogSanitizationOptions();
        var formatter = new SanitizingTextFormatter(sanitizer, options, outputTemplate);
        
        return sinkConfiguration.Console(formatter, restrictedToMinimumLevel);
    }

    /// <summary>
    /// 配置带脱敏的文件输出
    /// </summary>
    public static LoggerConfiguration SanitizedFile(
        this LoggerSinkConfiguration sinkConfiguration,
        ILogSanitizer sanitizer,
        string path,
        LogSanitizationOptions? options = null,
        LogEventLevel restrictedToMinimumLevel = LogEventLevel.Verbose,
        string outputTemplate = "{Timestamp:yyyy-MM-dd HH:mm:ss.fff zzz} [{Level:u3}] {Message:lj}{NewLine}{Exception}",
        long? fileSizeLimitBytes = 1073741824, // 1GB
        RollingInterval rollingInterval = RollingInterval.Day,
        bool rollOnFileSizeLimit = true,
        int? retainedFileCountLimit = 31)
    {
        if (sinkConfiguration == null)
            throw new ArgumentNullException(nameof(sinkConfiguration));

        options ??= new LogSanitizationOptions();
        var formatter = new SanitizingTextFormatter(sanitizer, options, outputTemplate);
        
        return sinkConfiguration.File(
            formatter,
            path,
            restrictedToMinimumLevel,
            fileSizeLimitBytes,
            levelSwitch: null,
            buffered: false,
            shared: false,
            flushToDiskInterval: null,
            rollingInterval,
            rollOnFileSizeLimit,
            retainedFileCountLimit);
    }

    /// <summary>
    /// 配置带脱敏的JSON文件输出
    /// </summary>
    public static LoggerConfiguration SanitizedJsonFile(
        this LoggerSinkConfiguration sinkConfiguration,
        ILogSanitizer sanitizer,
        string path,
        LogSanitizationOptions? options = null,
        LogEventLevel restrictedToMinimumLevel = LogEventLevel.Verbose,
        long? fileSizeLimitBytes = 1073741824, // 1GB
        RollingInterval rollingInterval = RollingInterval.Day,
        bool rollOnFileSizeLimit = true,
        int? retainedFileCountLimit = 31)
    {
        if (sinkConfiguration == null)
            throw new ArgumentNullException(nameof(sinkConfiguration));

        options ??= new LogSanitizationOptions();
        var formatter = new SanitizingJsonFormatter(sanitizer, options);
        
        return sinkConfiguration.File(
            formatter,
            path,
            restrictedToMinimumLevel,
            fileSizeLimitBytes,
            levelSwitch: null,
            buffered: false,
            shared: false,
            flushToDiskInterval: null,
            rollingInterval,
            rollOnFileSizeLimit,
            retainedFileCountLimit);
    }

    /// <summary>
    /// 创建带脱敏功能的Serilog Logger
    /// </summary>
    public static Logger CreateSanitizedLogger(
        IConfiguration configuration,
        ILogSanitizer? sanitizer = null,
        IHostEnvironment? environment = null)
    {
        var options = configuration.GetSection(LogSanitizationOptions.SectionName)
            .Get<LogSanitizationOptions>() ?? new LogSanitizationOptions();

        // 如果没有提供sanitizer，创建一个
        if (sanitizer == null)
        {
            var serviceProvider = new ServiceCollection()
                .AddLogging()
                .Configure<LogSanitizationOptions>(configuration.GetSection(LogSanitizationOptions.SectionName))
                .AddSingleton<ILogSanitizer, LogSanitizer>()
                .BuildServiceProvider();
                
            sanitizer = serviceProvider.GetRequiredService<ILogSanitizer>();
        }

        var loggerConfig = new LoggerConfiguration()
            .MinimumLevel.Override("Microsoft", LogEventLevel.Information)
            .MinimumLevel.Override("Microsoft.AspNetCore", LogEventLevel.Warning)
            .Enrich.FromLogContext();

        // 在生产环境或明确配置的情况下启用脱敏
        if (options.Enabled && (environment?.IsProduction() == true || options.EnabledInDevelopment))
        {
            loggerConfig.Enrich.With(new SanitizingLogEventEnricher(sanitizer, options));
            
            // 使用脱敏的输出
            loggerConfig.WriteTo.SanitizedConsole(sanitizer, options);
            loggerConfig.WriteTo.SanitizedFile(
                sanitizer, 
                "logs/whimlabai-.txt", 
                options,
                rollingInterval: RollingInterval.Day);
        }
        else
        {
            // 不脱敏的输出
            loggerConfig.WriteTo.Console();
            loggerConfig.WriteTo.File("logs/whimlabai-.txt", rollingInterval: RollingInterval.Day);
        }

        return loggerConfig.CreateLogger();
    }
}