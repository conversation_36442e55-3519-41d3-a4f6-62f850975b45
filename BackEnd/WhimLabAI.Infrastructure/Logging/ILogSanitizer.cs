using System.Collections.Generic;

namespace WhimLabAI.Infrastructure.Logging;

/// <summary>
/// 日志脱敏器接口
/// </summary>
public interface ILogSanitizer
{
    /// <summary>
    /// 对文本进行脱敏处理
    /// </summary>
    /// <param name="text">原始文本</param>
    /// <returns>脱敏后的文本</returns>
    string Sanitize(string text);
    
    /// <summary>
    /// 对对象进行脱敏处理
    /// </summary>
    /// <param name="obj">原始对象</param>
    /// <returns>脱敏后的对象</returns>
    object? SanitizeObject(object? obj);
    
    /// <summary>
    /// 对键值对进行脱敏处理
    /// </summary>
    /// <param name="key">键名</param>
    /// <param name="value">原始值</param>
    /// <returns>脱敏后的值</returns>
    string SanitizeByKey(string key, string value);
    
    /// <summary>
    /// 检查文本是否包含敏感信息
    /// </summary>
    /// <param name="text">待检查的文本</param>
    /// <returns>是否包含敏感信息</returns>
    bool ContainsSensitiveData(string text);
    
    /// <summary>
    /// 添加自定义脱敏规则
    /// </summary>
    /// <param name="pattern">正则表达式模式</param>
    /// <param name="replacement">替换文本</param>
    void AddCustomRule(string pattern, string replacement);
}