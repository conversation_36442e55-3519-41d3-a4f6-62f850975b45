using MassTransit;
using MediatR;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using WhimLabAI.Abstractions.Infrastructure;
using WhimLabAI.Domain.Repositories;
using WhimLabAI.Infrastructure.Data.Repositories;
using WhimLabAI.Infrastructure.EventSourcing;
using WhimLabAI.Infrastructure.Messaging;
using WhimLabAI.Infrastructure.Messaging.Consumers;

namespace WhimLabAI.Infrastructure.Extensions;

public static class EventDrivenExtensions
{
    public static IServiceCollection AddEventDrivenArchitecture(
        this IServiceCollection services, 
        IConfiguration configuration)
    {
        // Add MediatR for domain events
        services.AddMediatR(cfg =>
        {
            // Register event handlers from Application assembly
            cfg.RegisterServicesFromAssembly(AppDomain.CurrentDomain.GetAssemblies()
                .FirstOrDefault(a => a.GetName().Name == "WhimLabAI.Application") 
                ?? typeof(EventDrivenExtensions).Assembly);
            
            // Also register from Infrastructure assembly for any infrastructure-specific handlers
            cfg.RegisterServicesFromAssembly(typeof(EventDrivenExtensions).Assembly);
        });

        // Add Event Store
        services.AddScoped<IEventStoreRepository, EventStoreRepository>();
        // services.AddScoped<IEventStore, EventStore>(); // TODO: Fix IEventStore interface implementation

        // Add MassTransit for RabbitMQ
        services.AddMassTransit(x =>
        {
            // Register consumers
            x.AddConsumer<SendEmailConsumer>();

            // Configure RabbitMQ
            x.UsingRabbitMq((context, cfg) =>
            {
                var rabbitMqConfig = configuration.GetSection("RabbitMQ");
                var host = rabbitMqConfig["Host"] ?? "localhost";
                var port = rabbitMqConfig.GetValue<int?>("Port") ?? 5672;
                var username = rabbitMqConfig["Username"] ?? "guest";
                var password = rabbitMqConfig["Password"] ?? "guest";
                var virtualHost = rabbitMqConfig["VirtualHost"] ?? "/";

                cfg.Host(host, port, virtualHost, h =>
                {
                    h.Username(username);
                    h.Password(password);

                    // 添加连接容错配置
                    h.RequestedConnectionTimeout(TimeSpan.FromSeconds(10));
                    h.Heartbeat(TimeSpan.FromSeconds(30));
                });

                // Configure endpoints
                cfg.ConfigureEndpoints(context);

                // Configure retry policy
                cfg.UseMessageRetry(r => r.Intervals(
                    TimeSpan.FromSeconds(5),
                    TimeSpan.FromSeconds(15),
                    TimeSpan.FromSeconds(30)
                ));

                // Configure error handling - 使用新的API
                // cfg.UseInMemoryOutbox(); // 已过时，移除此配置
            });
        });
        
        // Configure MassTransit hosted service options
        services.Configure<MassTransit.MassTransitHostOptions>(options =>
        {
            // Don't wait for started - this prevents startup hang
            options.WaitUntilStarted = false;
            options.StartTimeout = TimeSpan.FromSeconds(30);
            options.StopTimeout = TimeSpan.FromSeconds(30);
        });

        // Add Message Queue Service
        services.AddScoped<IMessageQueueService, RabbitMQMessageBus>();

        return services;
    }

    public static IServiceCollection AddDomainEventHandlers(this IServiceCollection services)
    {
        // Domain event handlers are automatically registered by MediatR in AddEventDrivenArchitecture
        // through assembly scanning of the Application project
        
        return services;
    }
}