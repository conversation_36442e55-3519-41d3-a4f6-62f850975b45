using System;
using System.Linq;
using System.Security.Cryptography;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using WhimLabAI.Abstractions.Infrastructure;
using WhimLabAI.Infrastructure.Data;

namespace WhimLabAI.Infrastructure.Compliance;

/// <summary>
/// 数据匿名化服务实现
/// </summary>
public class DataAnonymizationService : IDataAnonymizationService
{
    private readonly WhimLabAIDbContext _dbContext;
    private readonly ILogger<DataAnonymizationService> _logger;

    public DataAnonymizationService(
        WhimLabAIDbContext dbContext,
        ILogger<DataAnonymizationService> logger)
    {
        _dbContext = dbContext;
        _logger = logger;
    }

    public async Task AnonymizeUserAsync(Guid userId, CancellationToken cancellationToken = default)
    {
        using var transaction = await _dbContext.Database.BeginTransactionAsync(cancellationToken);

        try
        {
            // 1. 匿名化用户基本信息
            var user = await _dbContext.CustomerUsers
                .Include(u => u.Profile)
                .FirstOrDefaultAsync(u => u.Id == userId, cancellationToken);

            if (user == null)
            {
                _logger.LogWarning("User {UserId} not found for anonymization", userId);
                return;
            }

            // 生成匿名标识
            var anonymousId = GenerateAnonymousId(userId);
            
            // 匿名化用户数据
            // Note: Username cannot be updated after creation, it's immutable
            // We'll update what we can
            if (user.Email != null)
            {
                user.UpdateEmail($"deleted_{anonymousId}@anonymized.local");
            }
            if (user.Phone != null)
            {
                user.UpdatePhone($"+00000{anonymousId.Substring(0, 6)}");
            }
            
            user.UpdateAvatar(string.Empty);
            user.UpdateProfile(
                nickname: $"匿名用户_{anonymousId.Substring(0, 8)}",
                bio: "此用户已请求删除个人信息",
                region: null,
                industry: null,
                position: null
            );

            // Note: LastLoginIp cannot be updated directly, it's set internally
            
            // 匿名化用户扩展资料
            if (user.Profile != null)
            {
                user.Profile.UpdateWorkInfo(
                    company: string.Empty,
                    department: string.Empty,
                    jobTitle: string.Empty,
                    workEmail: string.Empty,
                    workPhone: string.Empty
                );
                
                user.Profile.UpdateSocialLinks(
                    website: string.Empty,
                    linkedIn: string.Empty,
                    twitter: string.Empty,
                    gitHub: string.Empty
                );
                
                user.Profile.UpdateAddress(
                    address: string.Empty,
                    city: string.Empty,
                    province: string.Empty,
                    postalCode: string.Empty,
                    country: string.Empty
                );
                
                // Clear custom fields
                foreach (var key in user.Profile.CustomFields.Keys.ToList())
                {
                    user.Profile.RemoveCustomField(key);
                }
            }

            // 2. 匿名化对话内容中的个人信息
            var conversations = await _dbContext.Conversations
                .Include(c => c.Messages)
                .Where(c => c.CustomerUserId == userId)
                .ToListAsync(cancellationToken);

            foreach (var conversation in conversations)
            {
                conversation.UpdateTitle($"匿名对话_{conversation.Id.ToString().Substring(0, 8)}");
                
                // 注意：这里只是简单处理，实际应该使用更复杂的算法来检测和替换个人信息
                foreach (var message in conversation.Messages.Where(m => m.Role == "user"))
                {
                    if (!string.IsNullOrEmpty(message.Content))
                    {
                        // 替换可能的邮箱
                        var anonymizedContent = System.Text.RegularExpressions.Regex.Replace(
                            message.Content,
                            @"\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b",
                            "[已删除邮箱]"
                        );
                        
                        // 替换可能的电话号码
                        anonymizedContent = System.Text.RegularExpressions.Regex.Replace(
                            anonymizedContent,
                            @"\b(?:\+?86)?1[3-9]\d{9}\b",
                            "[已删除电话]"
                        );
                        
                        message.UpdateContent(anonymizedContent);
                    }
                }
            }

            // 3. 匿名化API密钥
            var apiKeys = await _dbContext.ApiKeys
                .Where(k => k.CustomerUserId == userId)
                .ToListAsync(cancellationToken);

            foreach (var apiKey in apiKeys)
            {
                apiKey.UpdateInfo(
                    name: $"匿名API密钥_{apiKey.Id.ToString().Substring(0, 8)}",
                    description: "用户数据已匿名化"
                );
                apiKey.Deactivate();
            }

            // 4. 匿名化设备信息
            var userWithDevices = await _dbContext.CustomerUsers
                .Include(u => u.DeviceAuthorizations)
                .FirstOrDefaultAsync(u => u.Id == userId, cancellationToken);

            if (userWithDevices?.DeviceAuthorizations != null)
            {
                foreach (var device in userWithDevices.DeviceAuthorizations)
                {
                    device.UpdateDeviceName($"匿名设备_{device.Id.ToString().Substring(0, 8)}");
                }
            }

            // 5. 匿名化OAuth绑定
            var userWithOAuth = await _dbContext.CustomerUsers
                .Include(u => u.OAuthBindings)
                .FirstOrDefaultAsync(u => u.Id == userId, cancellationToken);

            if (userWithOAuth?.OAuthBindings != null)
            {
                // OAuth绑定通常需要完全删除，因为无法匿名化第三方平台的数据
                _dbContext.RemoveRange(userWithOAuth.OAuthBindings);
            }

            // 6. 匿名化审计日志中的用户信息
            var auditLogs = await _dbContext.AuditLogs
                .Where(a => a.UserId == userId)
                .ToListAsync(cancellationToken);

            foreach (var log in auditLogs)
            {
                log.UserName = $"匿名用户_{anonymousId.Substring(0, 8)}";
                log.IpAddress = "0.0.0.0";
                log.UserAgent = "Anonymized";
                
                // 清除可能包含个人信息的数据
                log.OldValues = null;
                log.NewValues = null;
                log.RequestParameters = null;
                log.ClientInfo = null;
                log.GeoLocation = null;
            }

            await _dbContext.SaveChangesAsync(cancellationToken);
            await transaction.CommitAsync(cancellationToken);

            _logger.LogInformation("Successfully anonymized user {UserId} with anonymous ID {AnonymousId}", 
                userId, anonymousId);
        }
        catch (Exception ex)
        {
            await transaction.RollbackAsync(cancellationToken);
            _logger.LogError(ex, "Failed to anonymize user {UserId}", userId);
            throw;
        }
    }

    private string GenerateAnonymousId(Guid userId)
    {
        // 生成基于用户ID的匿名标识，确保同一用户多次匿名化得到相同的ID
        using var sha256 = SHA256.Create();
        var hashBytes = sha256.ComputeHash(Encoding.UTF8.GetBytes(userId.ToString()));
        return BitConverter.ToString(hashBytes).Replace("-", "").ToLower().Substring(0, 16);
    }
}