# WhimLabAI Compiled Queries

This directory contains pre-compiled queries for frequently executed database operations in the WhimLabAI application. Compiled queries provide better performance by pre-compiling the LINQ expression trees.

## Benefits of Compiled Queries

1. **Better Performance**: The query is compiled once and reused multiple times, avoiding the overhead of expression tree compilation on each execution
2. **Type Safety**: Compile-time checking of query syntax and types
3. **Consistent Execution Plans**: The same optimized execution plan is used for each query execution
4. **Reduced Memory Allocation**: Less garbage collection pressure from avoiding repeated expression tree compilation

## Usage in Repositories

### Example 1: Using Compiled Query in CustomerUserRepository

```csharp
public class CustomerUserRepository : Repository<CustomerUser>, ICustomerUserRepository
{
    public async Task<CustomerUser?> GetByUsernameAsync(string username, CancellationToken cancellationToken = default)
    {
        // Use compiled query instead of building LINQ expression
        return await WhimLabAICompiledQueries.GetCustomerByUsernameAsync(_context, username);
    }
    
    public async Task<bool> IsUsernameExistsAsync(string username, CancellationToken cancellationToken = default)
    {
        // Use compiled query for existence check
        return await Whim<PERSON>abAICompiledQueries.IsUsernameExistsAsync(_context, username);
    }
}
```

### Example 2: Using Compiled Query in SubscriptionRepository

```csharp
public class SubscriptionRepository : Repository<Subscription>, ISubscriptionRepository
{
    public async Task<Subscription?> GetActiveSubscriptionAsync(Guid userId, CancellationToken cancellationToken = default)
    {
        // Use compiled query for active subscription lookup
        return await WhimLabAICompiledQueries.GetActiveSubscriptionAsync(_context, userId);
    }
    
    public async Task<int> GetRemainingTokensAsync(Guid subscriptionId, CancellationToken cancellationToken = default)
    {
        // Use compiled query for token count
        return await WhimLabAICompiledQueries.GetRemainingTokensAsync(_context, subscriptionId);
    }
}
```

### Example 3: Using Compiled Query in ConversationRepository

```csharp
public class ConversationRepository : Repository<Conversation>, IConversationRepository
{
    public async Task<bool> UserOwnsConversationAsync(Guid userId, Guid conversationId, CancellationToken cancellationToken = default)
    {
        // Use compiled query for ownership check
        return await WhimLabAICompiledQueries.UserOwnsConversationAsync(_context, userId, conversationId);
    }
}
```

## Available Compiled Queries

### Customer User Queries
- `GetCustomerByUsernameAsync` - Gets customer by username with profile
- `GetCustomerByEmailAsync` - Gets customer by email with profile
- `GetCustomerByPhoneAsync` - Gets customer by phone with profile
- `IsUsernameExistsAsync` - Checks if username exists
- `IsEmailExistsAsync` - Checks if email exists
- `GetCustomerByIdAsync` - Gets customer by ID with profile
- `GetCustomerByRefreshTokenAsync` - Gets customer by refresh token

### Subscription Queries
- `GetActiveSubscriptionAsync` - Gets active subscription for a user
- `HasActiveSubscriptionAsync` - Checks if user has active subscription
- `GetRemainingTokensAsync` - Gets remaining tokens for subscription
- `GetExpiringSubscriptionsAsync` - Gets subscriptions expiring soon

### Agent Queries
- `GetAgentByUniqueKeyAsync` - Gets agent by unique key with full details
- `IsAgentUniqueKeyAvailableAsync` - Checks if agent unique key is available
- `GetPublishedAgentsCountAsync` - Gets count of published agents
- `GetAgentCountByCategoryAsync` - Gets agent count by category

### Token Usage Queries
- `GetTotalTokenUsageAsync` - Gets total token usage for subscription
- `GetTotalTokenUsageSinceDateAsync` - Gets token usage since date
- `GetCurrentMonthTokenUsageAsync` - Gets current month token usage

### Conversation Queries
- `UserOwnsConversationAsync` - Checks conversation ownership
- `GetActiveConversationCountAsync` - Gets active conversation count
- `GetUserAgentConversationCountAsync` - Gets conversation count by user and agent
- `HasUserUsedAgentAsync` - Checks if user has used an agent
- `GetPinnedConversationCountAsync` - Gets pinned conversation count
- `GetConversationMessageCountAsync` - Gets message count for conversation
- `GetConversationTokenCountAsync` - Gets token count for conversation

### Statistics Queries
- `GetUserRegistrationCountAsync` - Gets registration count by date range
- `GetActiveUserCountAsync` - Gets active user count
- `GetSubscriptionCountByStatusAsync` - Gets subscription count by status
- `GetConversationCountByDateRangeAsync` - Gets conversation count by date range
- `GetTotalRevenueAsync` - Gets total revenue by date range

### Admin User Queries
- `GetAdminByUsernameAsync` - Gets admin by username
- `GetAdminByEmailAsync` - Gets admin by email
- `IsAdminActiveAsync` - Checks if admin is active

### Security Queries
- `GetFailedLoginCountAsync` - Gets failed login count within time window
- `IsSecureTokenValidAsync` - Checks if secure token is valid

### Payment Queries
- `GetPendingOrderCountAsync` - Gets pending order count for user
- `GetUserPaymentTotalAsync` - Gets total successful payments for user
- `GetPendingTransactionCountAsync` - Gets pending transaction count for order

### Notification Queries
- `GetUnreadNotificationCountAsync` - Gets unread notification count
- `HasUnreadCriticalNotificationsAsync` - Checks for unread critical notifications

## Best Practices

1. **When to Use Compiled Queries**:
   - Queries executed frequently (e.g., authentication checks, permission validation)
   - Simple queries with minimal parameters
   - Queries used in hot paths or performance-critical sections

2. **When NOT to Use Compiled Queries**:
   - Complex queries with many conditional branches
   - Queries with dynamic filtering or sorting
   - One-time or rarely executed queries

3. **Adding New Compiled Queries**:
   - Follow the existing naming convention
   - Include XML documentation
   - Keep queries focused and simple
   - Test performance improvement before adding

4. **Performance Monitoring**:
   - Use Application Insights or similar tools to monitor query performance
   - Compare execution times before and after using compiled queries
   - Focus optimization efforts on the most frequently executed queries

## Migration Guide

To migrate existing repositories to use compiled queries:

1. Identify frequently executed queries in your repository
2. Check if a compiled query already exists in `WhimLabAICompiledQueries`
3. Replace the LINQ expression with the compiled query call
4. Test to ensure the same results are returned
5. Monitor performance improvements

## Performance Tips

1. **Avoid Complex Includes**: Split queries with multiple includes into separate compiled queries
2. **Use AsNoTracking**: For read-only queries, compiled queries already use `AsNoTracking` where appropriate
3. **Parameter Types**: Use exact types in parameters to avoid boxing/unboxing overhead
4. **Null Checks**: Compiled queries handle null parameters appropriately

## Contributing

When adding new compiled queries:

1. Analyze query execution frequency using database monitoring tools
2. Write the compiled query following the existing patterns
3. Add comprehensive XML documentation
4. Update this README with usage examples
5. Test both functionality and performance improvement