using Microsoft.EntityFrameworkCore;
using WhimLabAI.Domain.Entities.Agent;
using WhimLabAI.Domain.Entities.Conversation;
using WhimLabAI.Domain.Entities.Subscription;
using WhimLabAI.Domain.Entities.User;
using WhimLabAI.Shared.Enums;

namespace WhimLabAI.Infrastructure.Data.CompiledQueries;

/// <summary>
/// Compiled queries for frequently executed database operations.
/// Compiled queries provide better performance by pre-compiling the LINQ expression trees.
/// </summary>
public static class WhimLabAICompiledQueries
{
    #region CustomerUser Queries

    /// <summary>
    /// Gets a customer user by username with profile and notification settings
    /// </summary>
    public static readonly Func<WhimLabAIDbContext, string, Task<CustomerUser?>> GetCustomerByUsernameAsync =
        EF.CompileAsyncQuery((WhimLabAIDbContext context, string username) =>
            context.CustomerUsers
                .Include(u => u.Profile)
                .Include(u => u.NotificationSetting)
                .FirstOrDefault(u => u.Username == username));

    /// <summary>
    /// Gets a customer user by email with profile and notification settings
    /// </summary>
    public static readonly Func<WhimLabAIDbContext, string, Task<CustomerUser?>> GetCustomerByEmailAsync =
        EF.CompileAsyncQuery((WhimLabAIDbContext context, string email) =>
            context.CustomerUsers
                .Include(u => u.Profile)
                .Include(u => u.NotificationSetting)
                .FirstOrDefault(u => u.Email != null && u.Email.Value == email));

    /// <summary>
    /// Gets a customer user by phone number with profile and notification settings
    /// </summary>
    public static readonly Func<WhimLabAIDbContext, string, Task<CustomerUser?>> GetCustomerByPhoneAsync =
        EF.CompileAsyncQuery((WhimLabAIDbContext context, string phoneNumber) =>
            context.CustomerUsers
                .Include(u => u.Profile)
                .Include(u => u.NotificationSetting)
                .FirstOrDefault(u => u.Phone != null && u.Phone.Value == phoneNumber));

    /// <summary>
    /// Checks if a username exists
    /// </summary>
    public static readonly Func<WhimLabAIDbContext, string, Task<bool>> IsUsernameExistsAsync =
        EF.CompileAsyncQuery((WhimLabAIDbContext context, string username) =>
            context.CustomerUsers.Any(u => u.Username == username));

    /// <summary>
    /// Checks if an email exists
    /// </summary>
    public static readonly Func<WhimLabAIDbContext, string, Task<bool>> IsEmailExistsAsync =
        EF.CompileAsyncQuery((WhimLabAIDbContext context, string email) =>
            context.CustomerUsers.Any(u => u.Email != null && u.Email.Value == email));

    /// <summary>
    /// Gets a customer user by ID with profile
    /// </summary>
    public static readonly Func<WhimLabAIDbContext, Guid, Task<CustomerUser?>> GetCustomerByIdAsync =
        EF.CompileAsyncQuery((WhimLabAIDbContext context, Guid id) =>
            context.CustomerUsers
                .Include(u => u.Profile)
                .FirstOrDefault(u => u.Id == id));

    /// <summary>
    /// Gets a customer user by refresh token
    /// </summary>
    public static readonly Func<WhimLabAIDbContext, string, Task<CustomerUser?>> GetCustomerByRefreshTokenAsync =
        EF.CompileAsyncQuery((WhimLabAIDbContext context, string refreshToken) =>
            context.CustomerUsers
                .Include(u => u.Profile)
                .FirstOrDefault(u => EF.Property<string>(u, "_refreshToken") == refreshToken));

    #endregion

    #region Subscription Queries

    /// <summary>
    /// Gets the active subscription for a user
    /// </summary>
    public static readonly Func<WhimLabAIDbContext, Guid, Task<Subscription?>> GetActiveSubscriptionAsync =
        EF.CompileAsyncQuery((WhimLabAIDbContext context, Guid userId) =>
            context.Subscriptions
                .Include(s => s.Plan)
                .FirstOrDefault(s => s.CustomerUserId == userId && s.Status == SubscriptionStatus.Active));

    /// <summary>
    /// Checks if a user has an active subscription
    /// </summary>
    public static readonly Func<WhimLabAIDbContext, Guid, Task<bool>> HasActiveSubscriptionAsync =
        EF.CompileAsyncQuery((WhimLabAIDbContext context, Guid userId) =>
            context.Subscriptions.Any(s => 
                s.CustomerUserId == userId && 
                s.Status == SubscriptionStatus.Active));

    /// <summary>
    /// Gets remaining tokens for a subscription
    /// </summary>
    public static readonly Func<WhimLabAIDbContext, Guid, Task<int>> GetRemainingTokensAsync =
        EF.CompileAsyncQuery((WhimLabAIDbContext context, Guid subscriptionId) =>
            context.Subscriptions
                .Where(s => s.Id == subscriptionId)
                .Select(s => s.RemainingTokens)
                .FirstOrDefault());

    /// <summary>
    /// Gets expiring subscriptions within specified days
    /// </summary>
    public static readonly Func<WhimLabAIDbContext, DateTime, IAsyncEnumerable<Subscription>> GetExpiringSubscriptionsAsync =
        EF.CompileAsyncQuery((WhimLabAIDbContext context, DateTime beforeDate) =>
            context.Subscriptions
                .Include(s => s.Plan)
                .Where(s => s.Status == SubscriptionStatus.Active && 
                           s.EndDate != null && 
                           s.EndDate <= beforeDate));

    #endregion

    #region Agent Queries

    /// <summary>
    /// Gets an agent by unique key with full details
    /// </summary>
    public static readonly Func<WhimLabAIDbContext, string, Task<Agent?>> GetAgentByUniqueKeyAsync =
        EF.CompileAsyncQuery((WhimLabAIDbContext context, string uniqueKey) =>
            context.Agents
                .Include(a => a.Category)
                .Include(a => a.Tags)
                .Include(a => a.Versions)
                .Include(a => a.Ratings)
                .AsSplitQuery()
                .FirstOrDefault(a => a.UniqueKey == uniqueKey));

    /// <summary>
    /// Checks if an agent unique key is available
    /// </summary>
    public static readonly Func<WhimLabAIDbContext, string, Task<bool>> IsAgentUniqueKeyAvailableAsync =
        EF.CompileAsyncQuery((WhimLabAIDbContext context, string uniqueKey) =>
            !context.Agents.Any(a => a.UniqueKey == uniqueKey));

    /// <summary>
    /// Checks if an agent unique key is available excluding a specific agent
    /// </summary>
    public static readonly Func<WhimLabAIDbContext, string, Guid, Task<bool>> IsAgentUniqueKeyAvailableExcludingAsync =
        EF.CompileAsyncQuery((WhimLabAIDbContext context, string uniqueKey, Guid excludeAgentId) =>
            !context.Agents.Any(a => a.UniqueKey == uniqueKey && a.Id != excludeAgentId));

    /// <summary>
    /// Gets published agents count
    /// </summary>
    public static readonly Func<WhimLabAIDbContext, Task<int>> GetPublishedAgentsCountAsync =
        EF.CompileAsyncQuery((WhimLabAIDbContext context) =>
            context.Agents.Count(a => a.Status == AgentStatus.Published));

    /// <summary>
    /// Gets agent count by category
    /// </summary>
    public static readonly Func<WhimLabAIDbContext, Guid, Task<int>> GetAgentCountByCategoryAsync =
        EF.CompileAsyncQuery((WhimLabAIDbContext context, Guid categoryId) =>
            context.Agents.Count(a => a.CategoryId == categoryId));

    #endregion

    #region Token Usage Queries

    /// <summary>
    /// Gets total token usage for a subscription
    /// </summary>
    public static readonly Func<WhimLabAIDbContext, Guid, Task<int>> GetTotalTokenUsageAsync =
        EF.CompileAsyncQuery((WhimLabAIDbContext context, Guid subscriptionId) =>
            context.TokenUsages
                .Where(x => x.SubscriptionId == subscriptionId)
                .Sum(x => x.Tokens));

    /// <summary>
    /// Gets total token usage for a subscription since a specific date
    /// </summary>
    public static readonly Func<WhimLabAIDbContext, Guid, DateTime, Task<int>> GetTotalTokenUsageSinceDateAsync =
        EF.CompileAsyncQuery((WhimLabAIDbContext context, Guid subscriptionId, DateTime startDate) =>
            context.TokenUsages
                .Where(x => x.SubscriptionId == subscriptionId && x.UsedAt >= startDate)
                .Sum(x => x.Tokens));

    /// <summary>
    /// Gets current month token usage for a subscription
    /// </summary>
    public static readonly Func<WhimLabAIDbContext, Guid, DateTime, Task<int>> GetCurrentMonthTokenUsageAsync =
        EF.CompileAsyncQuery((WhimLabAIDbContext context, Guid subscriptionId, DateTime monthStart) =>
            context.TokenUsages
                .Where(x => x.SubscriptionId == subscriptionId && x.UsedAt >= monthStart)
                .Sum(x => x.Tokens));

    #endregion

    #region Conversation Queries

    /// <summary>
    /// Checks if a user owns a conversation
    /// </summary>
    public static readonly Func<WhimLabAIDbContext, Guid, Guid, Task<bool>> UserOwnsConversationAsync =
        EF.CompileAsyncQuery((WhimLabAIDbContext context, Guid userId, Guid conversationId) =>
            context.Conversations.Any(c => c.Id == conversationId && c.CustomerUserId == userId));

    /// <summary>
    /// Gets user's active conversation count
    /// </summary>
    public static readonly Func<WhimLabAIDbContext, Guid, Task<int>> GetActiveConversationCountAsync =
        EF.CompileAsyncQuery((WhimLabAIDbContext context, Guid userId) =>
            context.Conversations.Count(c => c.CustomerUserId == userId && !c.IsArchived));

    /// <summary>
    /// Gets conversation count for a user and agent
    /// </summary>
    public static readonly Func<WhimLabAIDbContext, Guid, Guid, Task<int>> GetUserAgentConversationCountAsync =
        EF.CompileAsyncQuery((WhimLabAIDbContext context, Guid userId, Guid agentId) =>
            context.Conversations.Count(c => c.CustomerUserId == userId && c.AgentId == agentId));

    /// <summary>
    /// Checks if a user has used a specific agent
    /// </summary>
    public static readonly Func<WhimLabAIDbContext, Guid, Guid, Task<bool>> HasUserUsedAgentAsync =
        EF.CompileAsyncQuery((WhimLabAIDbContext context, Guid userId, Guid agentId) =>
            context.Conversations.Any(c => c.CustomerUserId == userId && c.AgentId == agentId));

    /// <summary>
    /// Gets the number of pinned conversations for a user
    /// </summary>
    public static readonly Func<WhimLabAIDbContext, Guid, Task<int>> GetPinnedConversationCountAsync =
        EF.CompileAsyncQuery((WhimLabAIDbContext context, Guid userId) =>
            context.Conversations.Count(c => 
                c.CustomerUserId == userId && 
                EF.Functions.JsonContains(c.Metadata, "{\"IsPinned\": true}")));

    /// <summary>
    /// Gets total message count for a conversation
    /// </summary>
    public static readonly Func<WhimLabAIDbContext, Guid, Task<int>> GetConversationMessageCountAsync =
        EF.CompileAsyncQuery((WhimLabAIDbContext context, Guid conversationId) =>
            context.ConversationMessages.Count(m => m.ConversationId == conversationId));

    /// <summary>
    /// Gets total token count for a conversation
    /// </summary>
    public static readonly Func<WhimLabAIDbContext, Guid, Task<int>> GetConversationTokenCountAsync =
        EF.CompileAsyncQuery((WhimLabAIDbContext context, Guid conversationId) =>
            context.ConversationMessages
                .Where(m => m.ConversationId == conversationId)
                .Sum(m => m.TokenCount));

    #endregion

    #region Statistics Queries

    /// <summary>
    /// Gets user registration count for a date range
    /// </summary>
    public static readonly Func<WhimLabAIDbContext, DateTime, DateTime, Task<int>> GetUserRegistrationCountAsync =
        EF.CompileAsyncQuery((WhimLabAIDbContext context, DateTime startDate, DateTime endDate) =>
            context.CustomerUsers.Count(u => u.CreatedAt >= startDate && u.CreatedAt <= endDate));

    /// <summary>
    /// Gets active user count (users who logged in within last N days)
    /// </summary>
    public static readonly Func<WhimLabAIDbContext, DateTime, Task<int>> GetActiveUserCountAsync =
        EF.CompileAsyncQuery((WhimLabAIDbContext context, DateTime afterDate) =>
            context.CustomerUsers.Count(u => u.LastLoginAt != null && u.LastLoginAt >= afterDate));

    /// <summary>
    /// Gets subscription count by status
    /// </summary>
    public static readonly Func<WhimLabAIDbContext, SubscriptionStatus, Task<int>> GetSubscriptionCountByStatusAsync =
        EF.CompileAsyncQuery((WhimLabAIDbContext context, SubscriptionStatus status) =>
            context.Subscriptions.Count(s => s.Status == status));

    /// <summary>
    /// Gets conversation count created within a date range
    /// </summary>
    public static readonly Func<WhimLabAIDbContext, DateTime, DateTime, Task<int>> GetConversationCountByDateRangeAsync =
        EF.CompileAsyncQuery((WhimLabAIDbContext context, DateTime startDate, DateTime endDate) =>
            context.Conversations.Count(c => c.CreatedAt >= startDate && c.CreatedAt <= endDate));

    /// <summary>
    /// Gets total revenue for a date range
    /// </summary>
    public static readonly Func<WhimLabAIDbContext, DateTime, DateTime, Task<decimal>> GetTotalRevenueAsync =
        EF.CompileAsyncQuery((WhimLabAIDbContext context, DateTime startDate, DateTime endDate) =>
            context.Orders
                .Where(o => o.Status == OrderStatus.Paid && 
                           o.PaidAt != null && 
                           o.PaidAt >= startDate && 
                           o.PaidAt <= endDate)
                .Sum(o => o.FinalAmount.Amount));

    #endregion

    #region AdminUser Queries

    /// <summary>
    /// Gets an admin user by username
    /// </summary>
    public static readonly Func<WhimLabAIDbContext, string, Task<AdminUser?>> GetAdminByUsernameAsync =
        EF.CompileAsyncQuery((WhimLabAIDbContext context, string username) =>
            context.AdminUsers.FirstOrDefault(u => u.Username == username));

    /// <summary>
    /// Gets an admin user by email
    /// </summary>
    public static readonly Func<WhimLabAIDbContext, string, Task<AdminUser?>> GetAdminByEmailAsync =
        EF.CompileAsyncQuery((WhimLabAIDbContext context, string email) =>
            context.AdminUsers.FirstOrDefault(u => u.Email == email));

    /// <summary>
    /// Checks if an admin user is active
    /// </summary>
    public static readonly Func<WhimLabAIDbContext, Guid, Task<bool>> IsAdminActiveAsync =
        EF.CompileAsyncQuery((WhimLabAIDbContext context, Guid adminId) =>
            context.AdminUsers.Any(u => u.Id == adminId && u.Status == UserStatus.Active));

    #endregion

    #region Security Queries

    /// <summary>
    /// Gets failed login count for a user within a time window
    /// </summary>
    public static readonly Func<WhimLabAIDbContext, Guid, DateTime, Task<int>> GetFailedLoginCountAsync =
        EF.CompileAsyncQuery((WhimLabAIDbContext context, Guid userId, DateTime afterTime) =>
            context.AuditLogs
                .Count(a => a.UserId == userId && 
                           a.Action == "LoginFailed" && 
                           a.CreatedAt >= afterTime));

    /// <summary>
    /// Checks if a secure token is valid
    /// </summary>
    public static readonly Func<WhimLabAIDbContext, string, string, Task<bool>> IsSecureTokenValidAsync =
        EF.CompileAsyncQuery((WhimLabAIDbContext context, string tokenHash, string tokenType) =>
            context.SecureTokens.Any(t => 
                t.TokenHash == tokenHash && 
                t.TokenType == tokenType && 
                !t.IsRevoked && 
                t.ExpiresAt > DateTime.UtcNow));

    #endregion

    #region Payment Queries

    /// <summary>
    /// Gets pending order count for a user
    /// </summary>
    public static readonly Func<WhimLabAIDbContext, Guid, Task<int>> GetPendingOrderCountAsync =
        EF.CompileAsyncQuery((WhimLabAIDbContext context, Guid userId) =>
            context.Orders.Count(o => 
                o.CustomerUserId == userId && 
                o.Status == OrderStatus.Pending));

    /// <summary>
    /// Gets successful payment total for a user
    /// </summary>
    public static readonly Func<WhimLabAIDbContext, Guid, Task<decimal>> GetUserPaymentTotalAsync =
        EF.CompileAsyncQuery((WhimLabAIDbContext context, Guid userId) =>
            context.Orders
                .Where(o => o.CustomerUserId == userId && o.Status == OrderStatus.Paid)
                .Sum(o => o.FinalAmount.Amount));

    /// <summary>
    /// Gets pending payment transaction count for an order
    /// </summary>
    public static readonly Func<WhimLabAIDbContext, Guid, Task<int>> GetPendingTransactionCountAsync =
        EF.CompileAsyncQuery((WhimLabAIDbContext context, Guid orderId) =>
            context.PaymentTransactions.Count(t => 
                t.OrderId == orderId && 
                t.Status == TransactionStatus.Pending));

    #endregion

    #region Notification Queries

    /// <summary>
    /// Gets unread notification count for a user
    /// </summary>
    public static readonly Func<WhimLabAIDbContext, Guid, Task<int>> GetUnreadNotificationCountAsync =
        EF.CompileAsyncQuery((WhimLabAIDbContext context, Guid userId) =>
            context.Notifications.Count(n => 
                n.UserId == userId && 
                !n.IsRead));

    /// <summary>
    /// Checks if a user has unread critical notifications
    /// </summary>
    public static readonly Func<WhimLabAIDbContext, Guid, Task<bool>> HasUnreadCriticalNotificationsAsync =
        EF.CompileAsyncQuery((WhimLabAIDbContext context, Guid userId) =>
            context.Notifications.Any(n => 
                n.UserId == userId && 
                !n.IsRead && 
                n.Level == "Critical"));

    #endregion
}