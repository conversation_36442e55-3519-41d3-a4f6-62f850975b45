using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using WhimLabAI.Domain.Entities.Notification;
using WhimLabAI.Domain.Repositories;

namespace WhimLabAI.Infrastructure.Data.Repositories;

/// <summary>
/// 短信日志仓储实现
/// </summary>
public class SmsLogRepository : Repository<SmsLog>, ISmsLogRepository
{
    public SmsLogRepository(WhimLabAIDbContext context) : base(context)
    {
    }

    public async Task<int> GetSendCountAsync(string phoneNumber, int minutes, CancellationToken cancellationToken = default)
    {
        var cutoffTime = DateTime.UtcNow.AddMinutes(-minutes);
        
        return await _context.Set<SmsLog>()
            .Where(s => s.PhoneNumber == phoneNumber && 
                       s.SendTime >= cutoffTime &&
                       s.Status != "Failed")
            .CountAsync(cancellationToken);
    }

    public async Task<int> GetIpSendCountAsync(string ipAddress, int minutes, CancellationToken cancellationToken = default)
    {
        var cutoffTime = DateTime.UtcNow.AddMinutes(-minutes);
        
        return await _context.Set<SmsLog>()
            .Where(s => s.IpAddress == ipAddress && 
                       s.SendTime >= cutoffTime &&
                       s.Status != "Failed")
            .CountAsync(cancellationToken);
    }

    public async Task<SmsLog?> GetLatestVerificationCodeAsync(string phoneNumber, string purpose, CancellationToken cancellationToken = default)
    {
        return await _context.Set<SmsLog>()
            .Where(s => s.PhoneNumber == phoneNumber && 
                       s.Purpose == purpose &&
                       s.Type == "VerificationCode" &&
                       !s.IsVerified &&
                       s.CodeExpiryTime > DateTime.UtcNow)
            .OrderByDescending(s => s.SendTime)
            .FirstOrDefaultAsync(cancellationToken);
    }

    public async Task<bool> IsPhoneBlockedAsync(string phoneNumber, CancellationToken cancellationToken = default)
    {
        return await _context.Set<SmsLog>()
            .AnyAsync(s => s.PhoneNumber == phoneNumber && s.IsBlocked, cancellationToken);
    }

    public async Task<List<SmsLog>> GetPendingRetryAsync(int maxRetryCount, int limit, CancellationToken cancellationToken = default)
    {
        var cutoffTime = DateTime.UtcNow.AddMinutes(-5); // 5分钟前的才重试
        
        return await _context.Set<SmsLog>()
            .Where(s => s.Status == "Failed" && 
                       s.RetryCount < maxRetryCount &&
                       s.SendTime < cutoffTime &&
                       !s.IsBlocked)
            .OrderBy(s => s.SendTime)
            .Take(limit)
            .ToListAsync(cancellationToken);
    }

    public async Task<SmsLog?> GetByProviderMessageIdAsync(string providerMessageId, CancellationToken cancellationToken = default)
    {
        return await _context.Set<SmsLog>()
            .FirstOrDefaultAsync(s => s.ProviderMessageId == providerMessageId, cancellationToken);
    }
}