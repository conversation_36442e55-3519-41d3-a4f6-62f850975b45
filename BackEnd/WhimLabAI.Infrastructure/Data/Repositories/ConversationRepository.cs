using Microsoft.EntityFrameworkCore;
using WhimLabAI.Domain.Repositories;
using WhimLabAI.Domain.Entities.Conversation;

namespace WhimLabAI.Infrastructure.Data.Repositories;

public class ConversationRepository : Repository<Conversation>, IConversationRepository
{
    public ConversationRepository(WhimLabAIDbContext context) : base(context)
    {
    }

    public async Task<IEnumerable<Conversation>> GetUserConversationsAsync(Guid userId, bool includeArchived = false, CancellationToken cancellationToken = default)
    {
        var query = _dbSet
            .Where(c => c.CustomerUserId == userId);

        if (!includeArchived)
        {
            query = query.Where(c => !c.IsArchived);
        }

        // Use AsSplitQuery to avoid cartesian explosion when including messages
        return await query
            .Include(c => c.Messages.OrderByDescending(m => m.CreatedAt).Take(1)) // Include only the last message
            .AsSplitQuery() // Split the query to avoid performance issues
            .OrderByDescending(c => c.LastMessageAt)
            .AsNoTracking() // Read-only query
            .ToListAsync(cancellationToken);
    }

    public async Task<IEnumerable<ConversationMessage>> GetConversationMessagesAsync(Guid conversationId, int pageNumber, int pageSize, CancellationToken cancellationToken = default)
    {
        // Direct query on messages table with pagination at database level
        var messages = await _context.Set<ConversationMessage>()
            .Include(m => m.Attachments)
            .Include(m => m.Rating)
            .Where(m => m.ConversationId == conversationId)
            .OrderByDescending(m => m.CreatedAt)
            .Skip((pageNumber - 1) * pageSize)
            .Take(pageSize)
            .AsNoTracking()
            .ToListAsync(cancellationToken);

        // Return in chronological order
        messages.Reverse();
        return messages;
    }
    
    public async Task<IEnumerable<Conversation>> GetOldConversationsAsync(int daysOld, CancellationToken cancellationToken = default)
    {
        var cutoffDate = DateTime.UtcNow.AddDays(-daysOld);
        return await _dbSet
            .Where(c => c.LastMessageAt < cutoffDate && c.IsArchived)
            .ToListAsync(cancellationToken);
    }

    public async Task<ConversationMessage?> GetLastMessageAsync(Guid conversationId, CancellationToken cancellationToken = default)
    {
        // Direct query to get the last message without loading entire conversation
        return await _context.Set<ConversationMessage>()
            .Where(m => m.ConversationId == conversationId)
            .OrderByDescending(m => m.CreatedAt)
            .FirstOrDefaultAsync(cancellationToken);
    }

    public async Task<int> GetUserConversationCountAsync(Guid userId, Guid? agentId = null, CancellationToken cancellationToken = default)
    {
        var query = _dbSet.Where(c => c.CustomerUserId == userId);

        if (agentId.HasValue)
        {
            query = query.Where(c => c.AgentId == agentId.Value);
        }

        return await query.CountAsync(cancellationToken);
    }

    public async Task<int> GetUserTokenUsageAsync(Guid userId, DateTime startDate, DateTime endDate, CancellationToken cancellationToken = default)
    {
        // Use database aggregation with proper date filtering on messages
        return await _context.Set<ConversationMessage>()
            .Where(m => m.Conversation.CustomerUserId == userId && 
                       m.CreatedAt >= startDate && 
                       m.CreatedAt <= endDate)
            .SumAsync(m => m.TokenCount, cancellationToken);
    }

    public async Task<bool> UserOwnsConversationAsync(Guid userId, Guid conversationId, CancellationToken cancellationToken = default)
    {
        return await _dbSet
            .AnyAsync(c => c.Id == conversationId && c.CustomerUserId == userId, cancellationToken);
    }

    // Additional methods for extended functionality
    public async Task<Conversation?> GetWithMessagesAsync(Guid conversationId, CancellationToken cancellationToken = default)
    {
        return await _dbSet
            .Include(c => c.Messages)
                .ThenInclude(m => m.Attachments)
            .FirstOrDefaultAsync(c => c.Id == conversationId, cancellationToken);
    }

    public async Task<Conversation?> GetWithFullDetailsAsync(Guid conversationId, CancellationToken cancellationToken = default)
    {
        return await _dbSet
            .Include(c => c.Messages)
                .ThenInclude(m => m.Attachments)
            .Include(c => c.Messages)
                .ThenInclude(m => m.Rating)
            .AsSplitQuery() // Avoid cartesian explosion with multiple includes
            .FirstOrDefaultAsync(c => c.Id == conversationId, cancellationToken);
    }

    public async Task<IReadOnlyList<Conversation>> GetActiveConversationsAsync(Guid userId, int count = 10, CancellationToken cancellationToken = default)
    {
        return await _dbSet
            .Where(c => c.CustomerUserId == userId && !c.IsArchived)
            .Include(c => c.Messages.OrderByDescending(m => m.CreatedAt).Take(1))
            .AsSplitQuery() // Avoid cartesian explosion
            .OrderByDescending(c => c.LastMessageAt)
            .Take(count)
            .AsNoTracking() // Read-only query
            .ToListAsync(cancellationToken);
    }

    public async Task<IReadOnlyList<Conversation>> GetConversationsByAgentAsync(Guid agentId, int pageNumber, int pageSize, CancellationToken cancellationToken = default)
    {
        return await _dbSet
            .Where(c => c.AgentId == agentId)
            .OrderByDescending(c => c.CreatedAt)
            .Skip((pageNumber - 1) * pageSize)
            .Take(pageSize)
            .ToListAsync(cancellationToken);
    }

    public async Task<Dictionary<Guid, int>> GetAgentUsageStatsAsync(Guid agentId, DateTime startDate, DateTime endDate, CancellationToken cancellationToken = default)
    {
        // Project anonymous type first, then convert to dictionary
        var stats = await _dbSet
            .Where(c => c.AgentId == agentId && 
                       c.CreatedAt >= startDate && 
                       c.CreatedAt <= endDate)
            .GroupBy(c => c.CustomerUserId)
            .Select(g => new { UserId = g.Key, Count = g.Count() })
            .ToListAsync(cancellationToken);

        return stats.ToDictionary(x => x.UserId, x => x.Count);
    }

    public async Task<int> GetTotalMessageCountAsync(Guid conversationId, CancellationToken cancellationToken = default)
    {
        return await _context.Set<ConversationMessage>()
            .CountAsync(m => m.ConversationId == conversationId, cancellationToken);
    }

    public async Task<int> GetTotalTokenUsageAsync(Guid conversationId, CancellationToken cancellationToken = default)
    {
        return await _context.Set<ConversationMessage>()
            .Where(m => m.ConversationId == conversationId)
            .SumAsync(m => m.TokenCount, cancellationToken);
    }

    public async Task<ConversationMessage?> GetMessageByIdAsync(Guid messageId, CancellationToken cancellationToken = default)
    {
        return await _context.Set<ConversationMessage>()
            .Include(m => m.Attachments)
            .Include(m => m.Rating)
            .FirstOrDefaultAsync(m => m.Id == messageId, cancellationToken);
    }
    
    /// <summary>
    /// 检查用户是否使用过指定的Agent
    /// </summary>
    public async Task<bool> HasUserUsedAgentAsync(Guid userId, Guid agentId, CancellationToken cancellationToken = default)
    {
        return await _dbSet
            .AnyAsync(c => c.CustomerUserId == userId && c.AgentId == agentId, cancellationToken);
    }
    
    /// <summary>
    /// 获取用户置顶对话的数量
    /// </summary>
    public async Task<int> GetPinnedConversationCountAsync(Guid userId, CancellationToken cancellationToken = default)
    {
        // PostgreSQL jsonb query using EF Core
        // Note: EF Core 7.0+ supports JsonContains and direct JSON queries
        return await _dbSet
            .Where(c => c.CustomerUserId == userId && 
                       EF.Functions.JsonContains(c.Metadata, "{\"IsPinned\": true}"))
            .CountAsync(cancellationToken);
    }
    
    public async Task<(IEnumerable<Conversation> conversations, int totalCount)> SearchConversationsAsync(
        Guid userId,
        string? keyword = null,
        List<Guid>? agentIds = null,
        DateTime? startDate = null,
        DateTime? endDate = null,
        bool includeArchived = false,
        bool onlyPinned = false,
        bool searchInContent = false,
        string sortBy = "lastMessage",
        bool sortDescending = true,
        int skip = 0,
        int take = 20,
        CancellationToken cancellationToken = default)
    {
        var query = _dbSet
            .Where(c => c.CustomerUserId == userId);

        // Apply filters
        if (!includeArchived)
        {
            query = query.Where(c => !c.IsArchived);
        }

        if (onlyPinned)
        {
            query = query.Where(c => EF.Functions.JsonContains(c.Metadata, "{\"IsPinned\": true}"));
        }

        if (agentIds != null && agentIds.Any())
        {
            query = query.Where(c => agentIds.Contains(c.AgentId));
        }

        if (startDate.HasValue)
        {
            query = query.Where(c => c.CreatedAt >= startDate.Value);
        }

        if (endDate.HasValue)
        {
            query = query.Where(c => c.CreatedAt <= endDate.Value);
        }

        if (!string.IsNullOrWhiteSpace(keyword))
        {
            if (searchInContent)
            {
                // Use EF.Functions.ILike for better performance with indexes
                query = query.Where(c => 
                    EF.Functions.ILike(c.Title, $"%{keyword}%") || 
                    c.Messages.Any(m => EF.Functions.ILike(m.Content, $"%{keyword}%")));
            }
            else
            {
                // Search only in title with case-insensitive search
                query = query.Where(c => EF.Functions.ILike(c.Title, $"%{keyword}%"));
            }
        }

        // Get total count before pagination
        var totalCount = await query.CountAsync(cancellationToken);

        // Apply sorting
        query = sortBy switch
        {
            "created" => sortDescending ? query.OrderByDescending(c => c.CreatedAt) : query.OrderBy(c => c.CreatedAt),
            "messageCount" => sortDescending ? query.OrderByDescending(c => c.MessageCount) : query.OrderBy(c => c.MessageCount),
            "tokenUsage" => sortDescending ? query.OrderByDescending(c => c.TotalTokens) : query.OrderBy(c => c.TotalTokens),
            _ => sortDescending ? query.OrderByDescending(c => c.LastMessageAt) : query.OrderBy(c => c.LastMessageAt)
        };

        // Apply pagination and include messages with split query
        var conversations = await query
            .Skip(skip)
            .Take(take)
            .Include(c => c.Messages.OrderByDescending(m => m.CreatedAt).Take(1)) // Include only last message
            .AsSplitQuery() // Avoid cartesian explosion
            .AsNoTracking()
            .ToListAsync(cancellationToken);

        return (conversations, totalCount);
    }
    
    public async Task<Dictionary<string, object>> GetConversationStatisticsAsync(Guid conversationId, CancellationToken cancellationToken = default)
    {
        // Use database aggregation for statistics instead of loading all messages
        var stats = await _dbSet
            .Where(c => c.Id == conversationId)
            .Select(c => new
            {
                c.TotalTokens,
                c.InputTokens,
                c.OutputTokens,
                c.CreatedAt,
                c.LastMessageAt,
                TotalMessages = c.Messages.Count(m => !m.IsDeleted),
                UserMessages = c.Messages.Count(m => !m.IsDeleted && m.Role == "user"),
                AssistantMessages = c.Messages.Count(m => !m.IsDeleted && m.Role == "assistant"),
                AverageMessageLength = c.Messages.Where(m => !m.IsDeleted).Any() ? c.Messages.Where(m => !m.IsDeleted).Average(m => (double)m.Content.Length) : 0,
                AverageTokensPerMessage = c.Messages.Where(m => !m.IsDeleted).Any() ? c.Messages.Where(m => !m.IsDeleted).Average(m => (double)m.TokenCount) : 0,
                MessagesWithAttachments = c.Messages.Count(m => !m.IsDeleted && m.Attachments.Any()),
                MessagesWithRatings = c.Messages.Count(m => !m.IsDeleted && m.Rating != null),
                AverageRating = c.Messages
                    .Where(m => !m.IsDeleted && m.Role == "assistant" && m.Rating != null)
                    .Any() ? c.Messages
                    .Where(m => !m.IsDeleted && m.Role == "assistant" && m.Rating != null)
                    .Average(m => (double)m.Rating!.Score) : 0
            })
            .FirstOrDefaultAsync(cancellationToken);

        if (stats == null)
        {
            return new Dictionary<string, object>();
        }

        return new Dictionary<string, object>
        {
            ["totalMessages"] = stats.TotalMessages,
            ["userMessages"] = stats.UserMessages,
            ["assistantMessages"] = stats.AssistantMessages,
            ["totalTokens"] = stats.TotalTokens,
            ["inputTokens"] = stats.InputTokens,
            ["outputTokens"] = stats.OutputTokens,
            ["averageMessageLength"] = stats.AverageMessageLength,
            ["averageTokensPerMessage"] = stats.AverageTokensPerMessage,
            ["conversationDuration"] = (stats.LastMessageAt - stats.CreatedAt).TotalMinutes,
            ["messagesWithAttachments"] = stats.MessagesWithAttachments,
            ["messagesWithRatings"] = stats.MessagesWithRatings,
            ["averageRating"] = stats.AverageRating
        };
    }
    
    public async Task<Dictionary<string, object>> GetUserStatisticsAsync(Guid userId, DateTime? startDate = null, DateTime? endDate = null, CancellationToken cancellationToken = default)
    {
        var query = _dbSet.Where(c => c.CustomerUserId == userId);

        if (startDate.HasValue)
        {
            query = query.Where(c => c.CreatedAt >= startDate.Value);
        }

        if (endDate.HasValue)
        {
            query = query.Where(c => c.CreatedAt <= endDate.Value);
        }

        // Get basic statistics using database aggregation
        var basicStats = await query
            .GroupBy(c => c.CustomerUserId)
            .Select(g => new
            {
                TotalConversations = g.Count(),
                ActiveConversations = g.Count(c => !c.IsArchived),
                ArchivedConversations = g.Count(c => c.IsArchived),
                PinnedConversations = g.Count(c => EF.Functions.JsonContains(c.Metadata, "{\"IsPinned\": true}")),
                TotalMessages = g.Sum(c => c.MessageCount),
                TotalTokensUsed = g.Sum(c => c.TotalTokens),
                AverageMessagesPerConversation = g.Average(c => (double)c.MessageCount),
                AverageTokensPerConversation = g.Average(c => (double)c.TotalTokens),
                UniqueAgentsUsed = g.Select(c => c.AgentId).Distinct().Count()
            })
            .FirstOrDefaultAsync(cancellationToken);

        // Get conversations by agent
        var conversationsByAgent = await query
            .GroupBy(c => c.AgentId)
            .Select(g => new { AgentId = g.Key, Count = g.Count() })
            .ToDictionaryAsync(x => x.AgentId.ToString(), x => x.Count, cancellationToken);

        // Get conversations by day
        var conversationsByDay = await query
            .GroupBy(c => c.CreatedAt.Date)
            .Select(g => new { Date = g.Key, Count = g.Count() })
            .OrderBy(x => x.Date)
            .ToListAsync(cancellationToken);

        // Get peak hour
        var peakHour = await query
            .GroupBy(c => c.CreatedAt.Hour)
            .Select(g => new { Hour = g.Key, Count = g.Count() })
            .OrderByDescending(x => x.Count)
            .FirstOrDefaultAsync(cancellationToken);

        var stats = new Dictionary<string, object>
        {
            ["totalConversations"] = basicStats?.TotalConversations ?? 0,
            ["activeConversations"] = basicStats?.ActiveConversations ?? 0,
            ["archivedConversations"] = basicStats?.ArchivedConversations ?? 0,
            ["pinnedConversations"] = basicStats?.PinnedConversations ?? 0,
            ["totalMessages"] = basicStats?.TotalMessages ?? 0,
            ["totalTokensUsed"] = basicStats?.TotalTokensUsed ?? 0,
            ["averageMessagesPerConversation"] = basicStats?.AverageMessagesPerConversation ?? 0,
            ["averageTokensPerConversation"] = basicStats?.AverageTokensPerConversation ?? 0,
            ["uniqueAgentsUsed"] = basicStats?.UniqueAgentsUsed ?? 0,
            ["conversationsByAgent"] = conversationsByAgent,
            ["conversationsByDay"] = conversationsByDay.ToDictionary(x => x.Date.ToString("yyyy-MM-dd"), x => x.Count),
            ["peakHour"] = peakHour?.Hour ?? 0
        };

        return stats;
    }
    
    public async Task<IEnumerable<Conversation>> GetConversationsForCleanupAsync(
        int daysOld,
        bool excludePinned = true,
        bool excludeWithAttachments = true,
        int? limit = null,
        CancellationToken cancellationToken = default)
    {
        var cutoffDate = DateTime.UtcNow.AddDays(-daysOld);
        var query = _dbSet
            .Where(c => c.LastMessageAt < cutoffDate);

        if (excludePinned)
        {
            query = query.Where(c => !EF.Functions.JsonContains(c.Metadata, "{\"IsPinned\": true}"));
        }

        if (excludeWithAttachments)
        {
            // Check for attachments at database level
            query = query.Where(c => !c.Messages.Any(m => m.Attachments.Any()));
        }

        if (limit.HasValue)
        {
            query = query.Take(limit.Value);
        }

        // Include messages and attachments only for selected conversations
        return await query
            .Include(c => c.Messages)
                .ThenInclude(m => m.Attachments)
            .AsSplitQuery() // Avoid cartesian explosion
            .AsNoTracking()
            .ToListAsync(cancellationToken);
    }
    
    public async Task<int> GetUserStorageUsageAsync(Guid userId, CancellationToken cancellationToken = default)
    {
        // Calculate storage usage at database level to avoid loading all data
        var storageStats = await _dbSet
            .Where(c => c.CustomerUserId == userId)
            .Select(c => new
            {
                // Estimate title size (average 50 bytes per character)
                TitleSize = c.Title.Length * 2, // UTF-8 encoding
                // Count conversations for metadata overhead
                ConversationCount = 1,
                // Sum message content sizes
                MessageContentSize = c.Messages.Sum(m => m.Content.Length * 2),
                // Sum attachment sizes
                AttachmentSize = c.Messages.SelectMany(m => m.Attachments).Sum(a => (int)a.FileSize)
            })
            .GroupBy(x => 1) // Group all results
            .Select(g => new
            {
                TotalTitleSize = g.Sum(x => x.TitleSize),
                TotalConversations = g.Sum(x => x.ConversationCount),
                TotalMessageSize = g.Sum(x => x.MessageContentSize),
                TotalAttachmentSize = g.Sum(x => x.AttachmentSize)
            })
            .FirstOrDefaultAsync(cancellationToken);

        if (storageStats == null)
        {
            return 0;
        }

        // Calculate total with overhead
        var totalBytes = storageStats.TotalTitleSize +
                        (storageStats.TotalConversations * 200) + // Metadata overhead per conversation
                        storageStats.TotalMessageSize +
                        storageStats.TotalAttachmentSize;

        return totalBytes;
    }
    
    public async Task<IEnumerable<Conversation>> GetDeletedConversationsAsync(Guid userId, DateTime? since = null, CancellationToken cancellationToken = default)
    {
        var query = _dbSet.IgnoreQueryFilters()
            .Where(c => c.CustomerUserId == userId && c.IsDeleted);

        if (since.HasValue)
        {
            query = query.Where(c => c.UpdatedAt >= since.Value);
        }

        return await query
            .OrderByDescending(c => c.UpdatedAt)
            .AsNoTracking()
            .ToListAsync(cancellationToken);
    }
    
    public async Task<Dictionary<Guid, bool>> BatchUpdateMetadataAsync(List<Guid> conversationIds, string key, object value, CancellationToken cancellationToken = default)
    {
        var results = new Dictionary<Guid, bool>();
        
        var conversations = await _dbSet
            .Where(c => conversationIds.Contains(c.Id))
            .ToListAsync(cancellationToken);

        foreach (var conversation in conversations)
        {
            try
            {
                conversation.SetMetadata(key, value);
                results[conversation.Id] = true;
            }
            catch
            {
                results[conversation.Id] = false;
            }
        }

        await _context.SaveChangesAsync(cancellationToken);
        return results;
    }
    
    public async Task<int> BatchArchiveAsync(List<Guid> conversationIds, Guid userId, CancellationToken cancellationToken = default)
    {
        var conversations = await _dbSet
            .Where(c => conversationIds.Contains(c.Id) && c.CustomerUserId == userId && !c.IsArchived)
            .ToListAsync(cancellationToken);

        foreach (var conversation in conversations)
        {
            conversation.Archive();
        }

        await _context.SaveChangesAsync(cancellationToken);
        return conversations.Count;
    }
    
    public async Task<int> BatchDeleteAsync(List<Guid> conversationIds, Guid userId, CancellationToken cancellationToken = default)
    {
        var conversations = await _dbSet
            .Where(c => conversationIds.Contains(c.Id) && c.CustomerUserId == userId)
            .ToListAsync(cancellationToken);

        _dbSet.RemoveRange(conversations);
        await _context.SaveChangesAsync(cancellationToken);
        return conversations.Count;
    }
}