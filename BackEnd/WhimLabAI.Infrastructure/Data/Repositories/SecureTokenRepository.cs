using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using WhimLabAI.Domain.Entities.Security;
using WhimLabAI.Domain.Repositories;

namespace WhimLabAI.Infrastructure.Data.Repositories;

/// <summary>
/// Repository implementation for secure token operations
/// </summary>
public class SecureTokenRepository : ISecureTokenRepository
{
    private readonly WhimLabAIDbContext _context;

    public SecureTokenRepository(WhimLabAIDbContext context)
    {
        _context = context;
    }

    public async Task<SecureToken> AddAsync(SecureToken token)
    {
        await _context.SecureTokens.AddAsync(token);
        await _context.SaveChangesAsync();
        return token;
    }

    public async Task<SecureToken?> GetByTokenIdAsync(Guid tokenId)
    {
        return await _context.SecureTokens
            .FirstOrDefaultAsync(t => t.TokenId == tokenId);
    }

    public async Task<List<SecureToken>> GetByUserIdAsync(Guid userId, string? tokenType = null)
    {
        var query = _context.SecureTokens
            .Where(t => t.UserId == userId);

        if (!string.IsNullOrEmpty(tokenType))
        {
            query = query.Where(t => t.TokenType == tokenType);
        }

        return await query
            .OrderByDescending(t => t.CreatedAt)
            .ToListAsync();
    }

    public async Task UpdateAsync(SecureToken token)
    {
        _context.SecureTokens.Update(token);
        await _context.SaveChangesAsync();
    }

    public async Task<int> DeleteExpiredTokensAsync()
    {
        var expiredTokens = await _context.SecureTokens
            .Where(t => t.ExpiresAt.HasValue && t.ExpiresAt.Value <= DateTime.UtcNow)
            .ToListAsync();

        if (expiredTokens.Any())
        {
            _context.SecureTokens.RemoveRange(expiredTokens);
            return await _context.SaveChangesAsync();
        }

        return 0;
    }

    public async Task<List<SecureToken>> GetByTypeAsync(string tokenType, bool includeRevoked = false)
    {
        var query = _context.SecureTokens
            .Where(t => t.TokenType == tokenType);

        if (!includeRevoked)
        {
            query = query.Where(t => !t.IsRevoked);
        }

        return await query
            .OrderByDescending(t => t.CreatedAt)
            .ToListAsync();
    }

    public async Task<bool> ExistsAsync(Guid tokenId)
    {
        return await _context.SecureTokens
            .AnyAsync(t => t.TokenId == tokenId);
    }

    public async Task RevokeAllUserTokensAsync(Guid userId, string? tokenType = null)
    {
        var query = _context.SecureTokens
            .Where(t => t.UserId == userId && !t.IsRevoked);

        if (!string.IsNullOrEmpty(tokenType))
        {
            query = query.Where(t => t.TokenType == tokenType);
        }

        var tokens = await query.ToListAsync();
        
        foreach (var token in tokens)
        {
            token.Revoke("Bulk revocation");
        }

        if (tokens.Any())
        {
            await _context.SaveChangesAsync();
        }
    }
}