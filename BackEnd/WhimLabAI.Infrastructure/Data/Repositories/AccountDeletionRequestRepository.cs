using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using WhimLabAI.Domain.Entities.User;
using WhimLabAI.Domain.Repositories;
using WhimLabAI.Shared.Enums;

namespace WhimLabAI.Infrastructure.Data.Repositories;

/// <summary>
/// 账号注销请求仓储实现
/// </summary>
public class AccountDeletionRequestRepository : Repository<AccountDeletionRequest>, IAccountDeletionRequestRepository
{
    public AccountDeletionRequestRepository(WhimLabAIDbContext context) : base(context)
    {
    }
    
    public async Task<AccountDeletionRequest?> GetLatestByCustomerUserIdAsync(
        Guid customerUserId, 
        CancellationToken cancellationToken = default)
    {
        return await _dbSet
            .Include(x => x.CustomerUser)
            .Where(x => x.CustomerUserId == customerUserId)
            .OrderByDescending(x => x.RequestedAt)
            .FirstOrDefaultAsync(cancellationToken);
    }
    
    public async Task<List<AccountDeletionRequest>> GetByCustomerUserIdAsync(
        Guid customerUserId, 
        CancellationToken cancellationToken = default)
    {
        return await _dbSet
            .Include(x => x.CustomerUser)
            .Where(x => x.CustomerUserId == customerUserId)
            .OrderByDescending(x => x.RequestedAt)
            .ToListAsync(cancellationToken);
    }
    
    public async Task<List<AccountDeletionRequest>> GetPendingFreezingRequestsAsync(
        CancellationToken cancellationToken = default)
    {
        var now = DateTime.UtcNow;
        
        return await _dbSet
            .Include(x => x.CustomerUser)
            .Where(x => x.Status == DeletionRequestStatus.InCoolingOff && 
                       x.CoolingOffEndAt <= now)
            .ToListAsync(cancellationToken);
    }
    
    public async Task<List<AccountDeletionRequest>> GetPendingAnonymizationRequestsAsync(
        CancellationToken cancellationToken = default)
    {
        var now = DateTime.UtcNow;
        
        return await _dbSet
            .Include(x => x.CustomerUser)
            .Where(x => x.Status == DeletionRequestStatus.AccountFrozen && 
                       x.PlannedDeletionAt <= now)
            .ToListAsync(cancellationToken);
    }
    
    public async Task<List<AccountDeletionRequest>> GetRequestsNeedingCoolingOffReminderAsync(
        CancellationToken cancellationToken = default)
    {
        var tomorrow = DateTime.UtcNow.AddDays(1);
        
        return await _dbSet
            .Include(x => x.CustomerUser)
            .Where(x => x.Status == DeletionRequestStatus.InCoolingOff && 
                       !x.CoolingOffReminderSent &&
                       x.CoolingOffEndAt >= DateTime.UtcNow &&
                       x.CoolingOffEndAt <= tomorrow)
            .ToListAsync(cancellationToken);
    }
    
    public async Task<bool> HasActiveRequestAsync(
        Guid customerUserId, 
        CancellationToken cancellationToken = default)
    {
        return await _dbSet
            .AnyAsync(x => x.CustomerUserId == customerUserId && 
                          (x.Status == DeletionRequestStatus.PendingVerification ||
                           x.Status == DeletionRequestStatus.InCoolingOff ||
                           x.Status == DeletionRequestStatus.AccountFrozen), 
                      cancellationToken);
    }
    
    public async Task<AccountDeletionRequest?> GetByVerificationCodeAsync(
        string verificationCode, 
        CancellationToken cancellationToken = default)
    {
        return await _dbSet
            .Include(x => x.CustomerUser)
            .Where(x => x.VerificationCode == verificationCode && 
                       x.Status == DeletionRequestStatus.PendingVerification)
            .FirstOrDefaultAsync(cancellationToken);
    }
}