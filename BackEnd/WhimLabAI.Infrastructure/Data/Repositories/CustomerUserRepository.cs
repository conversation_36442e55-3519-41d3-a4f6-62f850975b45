using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using System.Diagnostics;
using WhimLabAI.Domain.Repositories;
using WhimLabAI.Domain.Entities.User;
using WhimLabAI.Domain.ValueObjects;
using WhimLabAI.Infrastructure.Data.CompiledQueries;

namespace WhimLabAI.Infrastructure.Data.Repositories;

public class CustomerUserRepository : Repository<CustomerUser>, ICustomerUserRepository
{
    private readonly ILogger<CustomerUserRepository> _logger;
    
    public CustomerUserRepository(
        WhimLabAIDbContext context,
        ILogger<CustomerUserRepository> logger) : base(context)
    {
        _logger = logger;
    }

    public IQueryable<CustomerUser> GetQueryable()
    {
        return _dbSet.AsQueryable();
    }
    
    private void LogQueryPerformance(string methodName, long elapsedMilliseconds, params object[] parameters)
    {
        const long slowQueryThreshold = 100; // milliseconds
        
        if (elapsedMilliseconds > slowQueryThreshold)
        {
            _logger.LogWarning("{MethodName} took {ElapsedMilliseconds}ms which exceeds the threshold of {Threshold}ms. Parameters: {@Parameters}",
                methodName, elapsedMilliseconds, slowQueryThreshold, parameters);
        }
        else
        {
            _logger.LogDebug("{MethodName} executed in {ElapsedMilliseconds}ms", methodName, elapsedMilliseconds);
        }
    }

    public async Task<CustomerUser?> GetByUsernameAsync(string username, CancellationToken cancellationToken = default)
    {
        var stopwatch = Stopwatch.StartNew();
        try
        {
            var result = await WhimLabAICompiledQueries.GetCustomerByUsernameAsync(_context, username);
            LogQueryPerformance(nameof(GetByUsernameAsync), stopwatch.ElapsedMilliseconds, username);
            return result;
        }
        finally
        {
            stopwatch.Stop();
        }
    }

    public async Task<CustomerUser?> GetByEmailAsync(string email, CancellationToken cancellationToken = default)
    {
        var stopwatch = Stopwatch.StartNew();
        try
        {
            var result = await WhimLabAICompiledQueries.GetCustomerByEmailAsync(_context, email);
            LogQueryPerformance(nameof(GetByEmailAsync), stopwatch.ElapsedMilliseconds, email);
            return result;
        }
        finally
        {
            stopwatch.Stop();
        }
    }

    public async Task<CustomerUser?> GetByPhoneNumberAsync(string phoneNumber, CancellationToken cancellationToken = default)
    {
        var stopwatch = Stopwatch.StartNew();
        try
        {
            var result = await WhimLabAICompiledQueries.GetCustomerByPhoneAsync(_context, phoneNumber);
            LogQueryPerformance(nameof(GetByPhoneNumberAsync), stopwatch.ElapsedMilliseconds, phoneNumber);
            return result;
        }
        finally
        {
            stopwatch.Stop();
        }
    }

    public async Task<bool> IsUsernameExistsAsync(string username, CancellationToken cancellationToken = default)
    {
        var stopwatch = Stopwatch.StartNew();
        try
        {
            var result = await WhimLabAICompiledQueries.IsUsernameExistsAsync(_context, username);
            LogQueryPerformance(nameof(IsUsernameExistsAsync), stopwatch.ElapsedMilliseconds, username);
            return result;
        }
        finally
        {
            stopwatch.Stop();
        }
    }

    public async Task<bool> IsEmailExistsAsync(string email, CancellationToken cancellationToken = default)
    {
        var stopwatch = Stopwatch.StartNew();
        try
        {
            var result = await WhimLabAICompiledQueries.IsEmailExistsAsync(_context, email);
            LogQueryPerformance(nameof(IsEmailExistsAsync), stopwatch.ElapsedMilliseconds, email);
            return result;
        }
        finally
        {
            stopwatch.Stop();
        }
    }

    public async Task<bool> IsPhoneNumberExistsAsync(string phoneNumber, CancellationToken cancellationToken = default)
    {
        var stopwatch = Stopwatch.StartNew();
        try
        {
            // Note: No compiled query available for this method yet
            var result = await _dbSet.AnyAsync(u => u.Phone != null && u.Phone.Value == phoneNumber, cancellationToken);
            LogQueryPerformance(nameof(IsPhoneNumberExistsAsync), stopwatch.ElapsedMilliseconds, phoneNumber);
            return result;
        }
        finally
        {
            stopwatch.Stop();
        }
    }

    public async Task<CustomerUser?> GetWithLoginHistoryAsync(Guid userId, int historyCount = 10, CancellationToken cancellationToken = default)
    {
        var stopwatch = Stopwatch.StartNew();
        try
        {
            // LoginHistories property doesn't exist on CustomerUser
            var result = await _dbSet
                .Include(u => u.Profile)
                .FirstOrDefaultAsync(u => u.Id == userId, cancellationToken);
            LogQueryPerformance(nameof(GetWithLoginHistoryAsync), stopwatch.ElapsedMilliseconds, userId, historyCount);
            return result;
        }
        finally
        {
            stopwatch.Stop();
        }
    }

    public async Task<CustomerUser?> GetWithOAuthBindingsAsync(Guid userId, CancellationToken cancellationToken = default)
    {
        var stopwatch = Stopwatch.StartNew();
        try
        {
            var result = await _dbSet
                .Include(u => u.Profile)
                .Include(u => u.OAuthBindings)
                .FirstOrDefaultAsync(u => u.Id == userId, cancellationToken);
            LogQueryPerformance(nameof(GetWithOAuthBindingsAsync), stopwatch.ElapsedMilliseconds, userId);
            return result;
        }
        finally
        {
            stopwatch.Stop();
        }
    }
    
    public async Task<CustomerUser?> GetByOAuthBindingAsync(string provider, string providerUserId, CancellationToken cancellationToken = default)
    {
        var stopwatch = Stopwatch.StartNew();
        try
        {
            var result = await _dbSet
                .Include(u => u.Profile)
                .Include(u => u.OAuthBindings)
                .Where(u => u.OAuthBindings.Any(b => b.Provider == provider && b.ProviderUserId == providerUserId))
                .FirstOrDefaultAsync(cancellationToken);
            LogQueryPerformance(nameof(GetByOAuthBindingAsync), stopwatch.ElapsedMilliseconds, provider, providerUserId);
            return result;
        }
        finally
        {
            stopwatch.Stop();
        }
    }

    public async Task<CustomerUser?> GetWithDevicesAsync(Guid userId, CancellationToken cancellationToken = default)
    {
        var stopwatch = Stopwatch.StartNew();
        try
        {
            var result = await _dbSet
                .Include(u => u.DeviceAuthorizations)
                .FirstOrDefaultAsync(u => u.Id == userId, cancellationToken);
            LogQueryPerformance(nameof(GetWithDevicesAsync), stopwatch.ElapsedMilliseconds, userId);
            return result;
        }
        finally
        {
            stopwatch.Stop();
        }
    }

    public async Task<IReadOnlyList<CustomerUser>> GetActiveUsersAsync(int pageNumber, int pageSize, CancellationToken cancellationToken = default)
    {
        var stopwatch = Stopwatch.StartNew();
        try
        {
            var result = await _dbSet
                .Where(u => u.IsActive && !u.IsBanned)
                .OrderByDescending(u => u.CreatedAt)
                .Skip((pageNumber - 1) * pageSize)
                .Take(pageSize)
                .ToListAsync(cancellationToken);
            LogQueryPerformance(nameof(GetActiveUsersAsync), stopwatch.ElapsedMilliseconds, pageNumber, pageSize);
            return result;
        }
        finally
        {
            stopwatch.Stop();
        }
    }

    public override async Task<CustomerUser?> GetByIdAsync(Guid id, CancellationToken cancellationToken = default)
    {
        var stopwatch = Stopwatch.StartNew();
        try
        {
            var result = await WhimLabAICompiledQueries.GetCustomerByIdAsync(_context, id);
            LogQueryPerformance(nameof(GetByIdAsync), stopwatch.ElapsedMilliseconds, id);
            return result;
        }
        finally
        {
            stopwatch.Stop();
        }
    }
    
    /// <summary>
    /// 根据ID列表批量获取用户
    /// </summary>
    public async Task<List<CustomerUser>> GetByIdsAsync(List<Guid> userIds, CancellationToken cancellationToken = default)
    {
        var stopwatch = Stopwatch.StartNew();
        try
        {
            var result = await _dbSet
                .Include(u => u.Profile)
                .Where(u => userIds.Contains(u.Id))
                .ToListAsync(cancellationToken);
            LogQueryPerformance(nameof(GetByIdsAsync), stopwatch.ElapsedMilliseconds, $"{userIds.Count} IDs");
            return result;
        }
        finally
        {
            stopwatch.Stop();
        }
    }
    
    /// <summary>
    /// 根据刷新令牌获取用户
    /// </summary>
    public async Task<CustomerUser?> GetByRefreshTokenAsync(string refreshToken, CancellationToken cancellationToken = default)
    {
        var stopwatch = Stopwatch.StartNew();
        try
        {
            var result = await WhimLabAICompiledQueries.GetCustomerByRefreshTokenAsync(_context, refreshToken);
            LogQueryPerformance(nameof(GetByRefreshTokenAsync), stopwatch.ElapsedMilliseconds, "[REDACTED]");
            return result;
        }
        finally
        {
            stopwatch.Stop();
        }
    }
}