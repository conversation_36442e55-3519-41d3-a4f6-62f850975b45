# CustomerUserRepository Performance Updates

## Overview
Updated the CustomerUserRepository to use compiled queries from WhimLabAICompiledQueries for improved performance and added comprehensive performance logging.

## Changes Made

### 1. Added Performance Dependencies
- Added `Microsoft.Extensions.Logging` for logging support
- Added `System.Diagnostics` for Stopwatch functionality
- Added reference to `WhimLabAI.Infrastructure.Data.CompiledQueries`

### 2. Constructor Updates
- Added `ILogger<CustomerUserRepository>` parameter to constructor for logging support

### 3. Methods Updated with Compiled Queries
The following methods now use pre-compiled queries for better performance:
- `GetByUsernameAsync` - Uses `WhimLabAICompiledQueries.GetCustomerByUsernameAsync`
- `GetByEmailAsync` - Uses `WhimLabAICompiledQueries.GetCustomerByEmailAsync`
- `GetByPhoneNumberAsync` - Uses `WhimLabAICompiledQueries.GetCustomerByPhoneAsync`
- `IsUsernameExistsAsync` - Uses `WhimLabAICompiledQueries.IsUsernameExistsAsync`
- `IsEmailExistsAsync` - Uses `WhimLabAICompiledQueries.IsEmailExistsAsync`
- `GetByIdAsync` - Uses `WhimLabAICompiledQueries.GetCustomerByIdAsync`
- `GetByRefreshTokenAsync` - Uses `WhimLabAICompiledQueries.GetCustomerByRefreshTokenAsync`

### 4. Methods with Performance Logging Only
The following methods don't have compiled queries available yet, but now include performance logging:
- `IsPhoneNumberExistsAsync` - No compiled query available (added comment noting this)
- `GetWithLoginHistoryAsync`
- `GetWithOAuthBindingsAsync`
- `GetByOAuthBindingAsync`
- `GetWithDevicesAsync`
- `GetActiveUsersAsync`
- `GetByIdsAsync`

### 5. Performance Monitoring
- Added `LogQueryPerformance` helper method that:
  - Logs query execution time as DEBUG level for queries under 100ms
  - Logs as WARNING level for slow queries (over 100ms) with parameter details
  - Redacts sensitive information (e.g., refresh tokens) in logs

### 6. Benefits
- **Compiled Queries**: Pre-compiled LINQ expressions eliminate query compilation overhead
- **Performance Visibility**: All queries now log execution time for monitoring
- **Slow Query Detection**: Automatic warnings for queries exceeding 100ms threshold
- **Parameter Tracking**: Logs include relevant parameters for troubleshooting slow queries

## Performance Expectations
- Compiled queries should show 10-20% performance improvement for frequently executed queries
- First execution may still be slower due to plan caching
- Subsequent executions will benefit from both compiled queries and database plan cache

## Future Improvements
Consider creating compiled queries for:
- `IsPhoneNumberExistsAsync`
- Complex queries in `GetWithOAuthBindingsAsync`
- Paginated queries in `GetActiveUsersAsync`