using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using System.Diagnostics;
using WhimLabAI.Domain.Repositories;
using WhimLabAI.Domain.Entities.Subscription;
using WhimLabAI.Shared.Enums;
using WhimLabAI.Infrastructure.Data.CompiledQueries;

namespace WhimLabAI.Infrastructure.Data.Repositories;

public class SubscriptionRepository : Repository<Subscription>, ISubscriptionRepository
{
    private readonly ILogger<SubscriptionRepository> _logger;

    public SubscriptionRepository(WhimLabAIDbContext context, ILogger<SubscriptionRepository> logger) 
        : base(context)
    {
        _logger = logger;
    }
    
    private void LogQueryPerformance(string methodName, long elapsedMilliseconds, params object[] parameters)
    {
        const long slowQueryThreshold = 100; // milliseconds
        
        if (elapsedMilliseconds > slowQueryThreshold)
        {
            _logger.LogWarning("{MethodName} took {ElapsedMilliseconds}ms which exceeds the threshold of {Threshold}ms. Parameters: {@Parameters}",
                methodName, elapsedMilliseconds, slowQueryThreshold, parameters);
        }
        else
        {
            _logger.LogDebug("{MethodName} executed in {ElapsedMilliseconds}ms", methodName, elapsedMilliseconds);
        }
    }

    public async Task<Subscription?> GetActiveSubscriptionAsync(Guid userId, CancellationToken cancellationToken = default)
    {
        var stopwatch = Stopwatch.StartNew();
        try
        {
            // Use compiled query for basic active subscription lookup
            var subscription = await WhimLabAICompiledQueries.GetActiveSubscriptionAsync(_context, userId);
            
            // If we need usage records, load them separately
            if (subscription != null)
            {
                await _context.Entry(subscription)
                    .Collection(s => s.UsageRecords)
                    .Query()
                    .Where(u => u.UsageTime >= DateTime.UtcNow.AddDays(-30))
                    .LoadAsync(cancellationToken);
            }
            
            LogQueryPerformance(nameof(GetActiveSubscriptionAsync), stopwatch.ElapsedMilliseconds, userId);
            return subscription;
        }
        finally
        {
            stopwatch.Stop();
        }
    }

    public async Task<Subscription?> GetActiveSubscriptionByUserIdAsync(Guid userId, CancellationToken cancellationToken = default)
    {
        return await GetActiveSubscriptionAsync(userId, cancellationToken);
    }

    public async Task<IEnumerable<Subscription>> GetUserSubscriptionHistoryAsync(Guid userId, CancellationToken cancellationToken = default)
    {
        var stopwatch = Stopwatch.StartNew();
        try
        {
            var result = await _dbSet
                .Include(s => s.Plan)
                .Where(s => s.CustomerUserId == userId)
                .OrderByDescending(s => s.StartDate)
                .ToListAsync(cancellationToken);
                
            LogQueryPerformance(nameof(GetUserSubscriptionHistoryAsync), stopwatch.ElapsedMilliseconds, userId);
            return result;
        }
        finally
        {
            stopwatch.Stop();
        }
    }

    public async Task<IEnumerable<Subscription>> GetExpiringSubscriptionsAsync(int days, CancellationToken cancellationToken = default)
    {
        var stopwatch = Stopwatch.StartNew();
        try
        {
            var beforeDate = DateTime.UtcNow.AddDays(days);
            var result = new List<Subscription>();
            
            await foreach (var subscription in WhimLabAICompiledQueries.GetExpiringSubscriptionsAsync(_context, beforeDate).WithCancellation(cancellationToken))
            {
                result.Add(subscription);
            }
            
            LogQueryPerformance(nameof(GetExpiringSubscriptionsAsync), stopwatch.ElapsedMilliseconds, days);
            return result;
        }
        finally
        {
            stopwatch.Stop();
        }
    }

    public async Task<IEnumerable<Subscription>> GetExpiredSubscriptionsAsync(CancellationToken cancellationToken = default)
    {
        return await _dbSet
            .Include(s => s.Plan)
            .Where(s => s.Status == SubscriptionStatus.Active && 
                       s.EndDate != null && 
                       s.EndDate < DateTime.UtcNow)
            .ToListAsync(cancellationToken);
    }

    public async Task<bool> HasActiveSubscriptionAsync(Guid userId, CancellationToken cancellationToken = default)
    {
        var stopwatch = Stopwatch.StartNew();
        try
        {
            var result = await WhimLabAICompiledQueries.HasActiveSubscriptionAsync(_context, userId);
            LogQueryPerformance(nameof(HasActiveSubscriptionAsync), stopwatch.ElapsedMilliseconds, userId);
            return result;
        }
        finally
        {
            stopwatch.Stop();
        }
    }

    public async Task<int> GetRemainingTokensAsync(Guid subscriptionId, CancellationToken cancellationToken = default)
    {
        var stopwatch = Stopwatch.StartNew();
        try
        {
            var result = await WhimLabAICompiledQueries.GetRemainingTokensAsync(_context, subscriptionId);
            LogQueryPerformance(nameof(GetRemainingTokensAsync), stopwatch.ElapsedMilliseconds, subscriptionId);
            return result;
        }
        finally
        {
            stopwatch.Stop();
        }
    }

    public async Task<bool> ConsumeTokensAsync(Guid subscriptionId, int tokens, CancellationToken cancellationToken = default)
    {
        var stopwatch = Stopwatch.StartNew();
        try
        {
            var subscription = await _dbSet.FindAsync([subscriptionId], cancellationToken);
            if (subscription == null || subscription.RemainingTokens < tokens)
            {
                LogQueryPerformance(nameof(ConsumeTokensAsync), stopwatch.ElapsedMilliseconds, subscriptionId, tokens);
                return false;
            }

            subscription.RemainingTokens -= tokens;
            await _context.SaveChangesAsync(cancellationToken);
            
            LogQueryPerformance(nameof(ConsumeTokensAsync), stopwatch.ElapsedMilliseconds, subscriptionId, tokens);
            return true;
        }
        finally
        {
            stopwatch.Stop();
        }
    }

    public async Task<bool> ResetMonthlyTokensAsync(Guid subscriptionId, CancellationToken cancellationToken = default)
    {
        var subscription = await _dbSet
            .Include(s => s.Plan)
            .FirstOrDefaultAsync(s => s.Id == subscriptionId, cancellationToken);
            
        if (subscription == null || subscription.Plan == null)
        {
            return false;
        }

        subscription.RemainingTokens = subscription.Plan.MonthlyTokens;
        subscription.LastResetDate = DateTime.UtcNow;
        await _context.SaveChangesAsync(cancellationToken);
        return true;
    }

    public async Task<IEnumerable<Subscription>> GetSubscriptionsNeedingTokenResetAsync(CancellationToken cancellationToken = default)
    {
        var oneMonthAgo = DateTime.UtcNow.AddMonths(-1);
        
        return await _dbSet
            .Include(s => s.Plan)
            .Where(s => s.Status == SubscriptionStatus.Active &&
                       (s.LastResetDate == null || s.LastResetDate <= oneMonthAgo))
            .ToListAsync(cancellationToken);
    }

    public async Task<object> GetSubscriptionStatisticsAsync(Guid subscriptionId, CancellationToken cancellationToken = default)
    {
        var stopwatch = Stopwatch.StartNew();
        try
        {
            // Check if subscription exists
            var subscriptionExists = await _dbSet
                .AnyAsync(s => s.Id == subscriptionId, cancellationToken);

            if (!subscriptionExists)
            {
                LogQueryPerformance(nameof(GetSubscriptionStatisticsAsync), stopwatch.ElapsedMilliseconds, subscriptionId);
                return new
                {
                    TotalTokensUsed = 0,
                    DailyUsage = new Dictionary<DateTime, int>(),
                    AverageDaily = 0,
                    PeakUsageDay = (DateTime?)null
                };
            }

            // Use database aggregation for statistics
            var thirtyDaysAgo = DateTime.UtcNow.AddDays(-30);
            
            var dailyUsage = await _context.Set<UsageRecord>()
                .Where(u => u.SubscriptionId == subscriptionId && u.UsageTime >= thirtyDaysAgo)
                .GroupBy(u => u.UsageTime.Date)
                .Select(g => new { Date = g.Key, Tokens = g.Sum(u => u.TokensUsed) })
                .OrderBy(x => x.Date)
                .ToDictionaryAsync(x => x.Date, x => x.Tokens, cancellationToken);

            var totalUsed = dailyUsage.Values.Sum();
            var peakDay = dailyUsage.Count > 0 
                ? dailyUsage.OrderByDescending(kvp => kvp.Value).First()
                : default(KeyValuePair<DateTime, int>);

            LogQueryPerformance(nameof(GetSubscriptionStatisticsAsync), stopwatch.ElapsedMilliseconds, subscriptionId);
            
            return new
            {
                TotalTokensUsed = totalUsed,
                DailyUsage = dailyUsage,
                AverageDaily = dailyUsage.Count > 0 ? totalUsed / dailyUsage.Count : 0,
                PeakUsageDay = peakDay.Key != default ? peakDay.Key : (DateTime?)null
            };
        }
        finally
        {
            stopwatch.Stop();
        }
    }

    public async Task<bool> UpgradeSubscriptionAsync(Guid subscriptionId, Guid newPlanId, CancellationToken cancellationToken = default)
    {
        var subscription = await _dbSet
            .Include(s => s.Plan)
            .FirstOrDefaultAsync(s => s.Id == subscriptionId, cancellationToken);

        if (subscription == null || subscription.Status != SubscriptionStatus.Active)
        {
            return false;
        }

        var newPlan = await _context.SubscriptionPlans.FindAsync([newPlanId], cancellationToken);
        if (newPlan == null)
        {
            return false;
        }

        // Calculate prorated tokens if upgrading
        if (newPlan.MonthlyTokens > subscription.Plan!.MonthlyTokens)
        {
            var daysInMonth = DateTime.DaysInMonth(DateTime.UtcNow.Year, DateTime.UtcNow.Month);
            var daysRemaining = daysInMonth - DateTime.UtcNow.Day + 1;
            var proratedTokens = (newPlan.MonthlyTokens - subscription.Plan.MonthlyTokens) * daysRemaining / daysInMonth;
            subscription.RemainingTokens += proratedTokens;
        }

        subscription.PlanId = newPlanId;
        subscription.Plan = newPlan;
        await _context.SaveChangesAsync(cancellationToken);
        
        _logger.LogInformation("Subscription {SubscriptionId} upgraded to plan {PlanId}", subscriptionId, newPlanId);
        return true;
    }

    public async Task<bool> CancelSubscriptionAsync(Guid subscriptionId, string reason, CancellationToken cancellationToken = default)
    {
        var subscription = await _dbSet.FindAsync([subscriptionId], cancellationToken);
        if (subscription == null)
        {
            return false;
        }

        subscription.Status = SubscriptionStatus.Cancelled;
        subscription.CancellationDate = DateTime.UtcNow;
        subscription.CancellationReason = reason;
        
        await _context.SaveChangesAsync(cancellationToken);
        
        _logger.LogInformation("Subscription {SubscriptionId} cancelled. Reason: {Reason}", subscriptionId, reason);
        return true;
    }

    public async Task<bool> PauseSubscriptionAsync(Guid subscriptionId, DateTime resumeDate, CancellationToken cancellationToken = default)
    {
        var stopwatch = Stopwatch.StartNew();
        try
        {
            var subscription = await _dbSet.FindAsync([subscriptionId], cancellationToken);
            if (subscription == null)
            {
                LogQueryPerformance(nameof(PauseSubscriptionAsync), stopwatch.ElapsedMilliseconds, subscriptionId);
                return false;
            }

            try
            {
                subscription.Pause(resumeDate);
                await _context.SaveChangesAsync(cancellationToken);
                
                _logger.LogInformation("Subscription {SubscriptionId} paused until {ResumeDate}", subscriptionId, resumeDate);
                LogQueryPerformance(nameof(PauseSubscriptionAsync), stopwatch.ElapsedMilliseconds, subscriptionId);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to pause subscription {SubscriptionId}", subscriptionId);
                return false;
            }
        }
        finally
        {
            stopwatch.Stop();
        }
    }

    public async Task<bool> ResumeSubscriptionAsync(Guid subscriptionId, CancellationToken cancellationToken = default)
    {
        var stopwatch = Stopwatch.StartNew();
        try
        {
            var subscription = await _dbSet.FindAsync([subscriptionId], cancellationToken);
            if (subscription == null)
            {
                LogQueryPerformance(nameof(ResumeSubscriptionAsync), stopwatch.ElapsedMilliseconds, subscriptionId);
                return false;
            }

            try
            {
                subscription.Resume();
                await _context.SaveChangesAsync(cancellationToken);
                
                _logger.LogInformation("Subscription {SubscriptionId} resumed", subscriptionId);
                LogQueryPerformance(nameof(ResumeSubscriptionAsync), stopwatch.ElapsedMilliseconds, subscriptionId);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to resume subscription {SubscriptionId}", subscriptionId);
                return false;
            }
        }
        finally
        {
            stopwatch.Stop();
        }
    }
    
    public async Task<IEnumerable<Subscription>> GetSubscriptionsToAutoResumeAsync(CancellationToken cancellationToken = default)
    {
        var stopwatch = Stopwatch.StartNew();
        try
        {
            var result = await _dbSet
                .Include(s => s.Plan)
                .Where(s => s.Status == SubscriptionStatus.Paused && 
                           s.ResumeDate != null && 
                           s.ResumeDate <= DateTime.UtcNow)
                .ToListAsync(cancellationToken);
                
            LogQueryPerformance(nameof(GetSubscriptionsToAutoResumeAsync), stopwatch.ElapsedMilliseconds);
            return result;
        }
        finally
        {
            stopwatch.Stop();
        }
    }
    
    public async Task<IEnumerable<Subscription>> GetActiveSubscriptionsAsync(CancellationToken cancellationToken = default)
    {
        return await _dbSet
            .Include(s => s.Plan)
            .Where(s => s.Status == SubscriptionStatus.Active)
            .ToListAsync(cancellationToken);
    }
}