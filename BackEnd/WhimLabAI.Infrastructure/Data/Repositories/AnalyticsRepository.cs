using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using WhimLabAI.Domain.Entities.Agent;
using WhimLabAI.Domain.Entities.Conversation;
using WhimLabAI.Domain.Entities.Organization;
using WhimLabAI.Domain.Entities.Payment;
using WhimLabAI.Domain.Entities.Subscription;
using WhimLabAI.Domain.Entities.User;
using WhimLabAI.Domain.Repositories;
using WhimLabAI.Shared.Enums;

namespace WhimLabAI.Infrastructure.Data.Repositories;

/// <summary>
/// 数据分析仓储实现
/// </summary>
public class AnalyticsRepository : IAnalyticsRepository
{
    private readonly WhimLabAIDbContext _context;

    public AnalyticsRepository(WhimLabAIDbContext context)
    {
        _context = context;
    }

    #region 用户分析查询

    public IQueryable<CustomerUser> GetCustomerUsersQuery()
    {
        return _context.CustomerUsers.AsNoTracking();
    }

    public async Task<int> GetActiveUsersCountAsync(DateTime startDate, DateTime endDate, CancellationToken cancellationToken = default)
    {
        return await _context.CustomerUsers
            .Where(u => u.LastLoginAt.HasValue && u.LastLoginAt.Value >= startDate && u.LastLoginAt.Value <= endDate)
            .CountAsync(cancellationToken);
    }

    public async Task<Dictionary<int, int>> GetUserRetentionAsync(DateTime cohortDate, List<Guid> userIds, int daysToTrack, CancellationToken cancellationToken = default)
    {
        var retentionData = new Dictionary<int, int>();

        for (int day = 0; day <= daysToTrack; day++)
        {
            var targetDate = cohortDate.AddDays(day);
            
            var activeUsers = await _context.Conversations
                .Where(c => userIds.Contains(c.CustomerUserId) && c.CreatedAt.Date == targetDate.Date)
                .Select(c => c.CustomerUserId)
                .Distinct()
                .CountAsync(cancellationToken);

            retentionData[day] = activeUsers;
        }

        return retentionData;
    }

    #endregion

    #region Agent分析查询

    public IQueryable<Agent> GetAgentsQuery()
    {
        return _context.Agents.AsNoTracking();
    }

    public IQueryable<Agent> GetPopularAgentsQuery(DateTime startDate, DateTime endDate)
    {
        return _context.Agents
            .Where(a => a.Status == AgentStatus.Published)
            .AsNoTracking();
    }

    #endregion

    #region 对话分析查询

    public IQueryable<Conversation> GetConversationsQuery()
    {
        return _context.Conversations.AsNoTracking();
    }

    public IQueryable<ConversationMessage> GetMessagesQuery()
    {
        return _context.ConversationMessages.AsNoTracking();
    }

    public async Task<(int totalConversations, int totalMessages, long totalTokens)> GetConversationStatsAsync(
        DateTime startDate, 
        DateTime endDate, 
        CancellationToken cancellationToken = default)
    {
        var stats = await _context.Conversations
            .Where(c => c.CreatedAt >= startDate && c.CreatedAt <= endDate)
            .Select(c => new
            {
                ConversationId = c.Id,
                MessageCount = c.Messages.Count(),
                TotalTokens = c.Messages.Sum(m => (long?)m.TokenCount) ?? 0
            })
            .GroupBy(x => 1)
            .Select(g => new
            {
                TotalConversations = g.Count(),
                TotalMessages = g.Sum(x => x.MessageCount),
                TotalTokens = g.Sum(x => x.TotalTokens)
            })
            .FirstOrDefaultAsync(cancellationToken);

        if (stats == null)
            return (0, 0, 0);

        return (stats.TotalConversations, stats.TotalMessages, stats.TotalTokens);
    }

    #endregion

    #region 财务分析查询

    public IQueryable<Subscription> GetSubscriptionsQuery()
    {
        return _context.Subscriptions.AsNoTracking();
    }

    public IQueryable<Order> GetOrdersQuery()
    {
        return _context.Orders.AsNoTracking();
    }

    public async Task<(decimal totalRevenue, decimal recurringRevenue, int orderCount)> GetRevenueStatsAsync(
        DateTime startDate, 
        DateTime endDate, 
        CancellationToken cancellationToken = default)
    {
        var stats = await _context.Orders
            .Where(o => o.Status == OrderStatus.Paid && o.CreatedAt >= startDate && o.CreatedAt <= endDate)
            .GroupBy(o => 1)
            .Select(g => new
            {
                TotalRevenue = g.Sum(o => o.Amount.Amount),
                OrderCount = g.Count()
            })
            .FirstOrDefaultAsync(cancellationToken);

        if (stats == null)
            return (0, 0, 0);

        // 计算订阅收入
        var recurringRevenue = await _context.Orders
            .Where(o => o.Status == OrderStatus.Paid && 
                       o.CreatedAt >= startDate && 
                       o.CreatedAt <= endDate &&
                       o.Type == OrderType.Subscription)
            .SumAsync(o => o.Amount.Amount, cancellationToken);

        return (stats.TotalRevenue, recurringRevenue, stats.OrderCount);
    }

    #endregion

    #region Token使用分析查询

    public IQueryable<TokenUsage> GetTokenUsagesQuery()
    {
        return _context.TokenUsages.AsNoTracking();
    }

    public async Task<Dictionary<string, long>> GetTokenUsageByModelAsync(
        DateTime startDate, 
        DateTime endDate, 
        CancellationToken cancellationToken = default)
    {
        // TODO: Implement when ConversationMessage has Model property
        var usageByModel = new Dictionary<string, long>();

        return usageByModel;
    }

    #endregion

    #region 评价分析查询

    public IQueryable<AgentRating> GetReviewsQuery()
    {
        return _context.AgentRatings.AsNoTracking();
    }

    public async Task<(double averageRating, int totalRatings)> GetRatingStatsAsync(
        Guid? agentId, 
        DateTime? startDate, 
        DateTime? endDate, 
        CancellationToken cancellationToken = default)
    {
        var query = _context.AgentRatings.AsQueryable();

        if (agentId.HasValue)
            query = query.Where(r => EF.Property<Guid>(r, "AgentId") == agentId.Value);

        if (startDate.HasValue)
            query = query.Where(r => r.CreatedAt >= startDate.Value);

        if (endDate.HasValue)
            query = query.Where(r => r.CreatedAt <= endDate.Value);

        var stats = await query
            .GroupBy(r => 1)
            .Select(g => new
            {
                AverageRating = g.Average(r => r.Score),
                TotalRatings = g.Count()
            })
            .FirstOrDefaultAsync(cancellationToken);

        if (stats == null)
            return (0, 0);

        return (stats.AverageRating, stats.TotalRatings);
    }

    #endregion

    #region Additional Analytics Queries

    public IQueryable<UserLoginHistory> GetUserLoginHistoriesQuery()
    {
        return _context.Set<UserLoginHistory>();
    }

    public IQueryable<Department> GetDepartmentsQuery()
    {
        return _context.Set<Department>();
    }

    public IQueryable<UserAcquisition> GetUserAcquisitionsQuery()
    {
        return _context.Set<UserAcquisition>();
    }

    public IQueryable<ConversationMetrics> GetConversationMetricsQuery()
    {
        return _context.Set<ConversationMetrics>();
    }

    public IQueryable<RefundRequest> GetRefundRequestsQuery()
    {
        return _context.Set<RefundRequest>();
    }

    #endregion
}