# JSON Metadata Indexes Documentation

This document describes the optimized indexes created in the `AddJsonMetadataIndexes` migration.

## Overview

The migration adds comprehensive database indexes optimized for PostgreSQL to improve query performance, especially for JSON metadata queries and common access patterns.

## Index Categories

### 1. GIN Indexes for JSON Metadata Queries

GIN (Generalized Inverted Index) indexes are optimized for PostgreSQL's JSONB data type. We use `jsonb_path_ops` for better performance when using containment operators (`@>`).

#### Key JSON Indexes:
- **Agents.Metadata**: General queries and specific paths (tags, modelProvider)
- **Conversations.Metadata**: Including pinned conversations filter
- **Orders.Metadata**: Order metadata queries
- **TokenUsages.Metadata**: Usage tracking metadata
- **ApiKeys**: Scopes, IP whitelist, allowed domains, and general metadata
- **SystemEvents.EventData**: Event data queries
- **UserConsents.Metadata**: Consent tracking
- **DataExportRequests.IncludedDataTypes**: Export configuration
- **KnowledgeBase**: Vector DB config, chunking config, metadata
- **Documents/DocumentChunks.Metadata**: Document metadata
- **AuditLogs**: Properties, old/new values, security context
- **AgentVersions**: Model config, prompt template, knowledge base config

### 2. Frequently Filtered Fields

Standard B-tree indexes for columns frequently used in WHERE clauses:
- **Conversations**: Status, LastMessageAt (with NULL filter)
- **Agents**: PublishedAt (with NULL filter), IsPublished (partial index)

### 3. Composite Indexes for Common Query Patterns

Multi-column indexes optimized for specific query patterns:
- **Conversations**: User+Status+LastMessageAt, Agent+Status+CreatedAt
- **Orders**: User+PaymentStatus+CreatedAt
- **TokenUsages**: User+Model+CreatedAt

### 4. Partial Indexes for Soft Delete Queries

Indexes filtered on `IsDeleted = false` to optimize queries on active records:
- **Agents**: Status+Category+Rating for active agents
- **CustomerUsers/AdminUsers**: Status+CreatedAt for active users
- **Subscriptions**: User+Status+ExpiresAt for active subscriptions

### 5. Expression Indexes for Case-Insensitive Searches

Function-based indexes using `LOWER()` for case-insensitive queries:
- **Email searches**: CustomerUsers, AdminUsers
- **Username searches**: CustomerUsers, AdminUsers
- **Agent name search**: With soft delete filter
- **Conversation title search**: With NULL and soft delete filters

### 6. Additional Performance Indexes

Specialized indexes for specific use cases:
- **ApiKeys.KeyHash**: For API key lookup (active and not deleted)
- **Session tokens**: Customer and admin sessions with expiry
- **Notifications**: User+IsRead+CreatedAt for notification delivery
- **DocumentChunks**: KnowledgeBase+IsEmbedded for vector operations
- **EventStore**: AggregateId+Version, EventType+Timestamp

## Performance Considerations

1. **CONCURRENTLY**: All indexes are created with `CONCURRENTLY` to avoid table locks during creation.

2. **IF NOT EXISTS**: Prevents errors if indexes already exist from previous migrations.

3. **Partial Indexes**: Used to reduce index size and improve performance for filtered queries.

4. **jsonb_path_ops**: Chosen over default GIN operator class for better performance with containment queries.

5. **ANALYZE**: Tables are analyzed after index creation to update statistics.

## Query Examples

### JSON Containment Query
```sql
-- Find agents with specific tag
SELECT * FROM "Agents" 
WHERE "Metadata" @> '{"tags": ["ai", "chatbot"]}';

-- Find pinned conversations
SELECT * FROM "Conversations" 
WHERE "Metadata" @> '{"IsPinned": true}';
```

### Case-Insensitive Search
```sql
-- Find user by email (case-insensitive)
SELECT * FROM "CustomerUsers" 
WHERE LOWER("Email") = LOWER('<EMAIL>');
```

### Soft Delete Aware Query
```sql
-- Get active agents by category
SELECT * FROM "Agents" 
WHERE "IsDeleted" = false 
  AND "Status" = 'Published' 
  AND "Category" = 'Assistant'
ORDER BY "Rating" DESC;
```

## Maintenance

1. **Monitor Index Usage**: Use `pg_stat_user_indexes` to track index usage.
2. **Rebuild If Needed**: Indexes may need periodic rebuilding if they become bloated.
3. **Update Statistics**: Run `ANALYZE` periodically or rely on autovacuum.

## Rollback

The migration includes a complete `Down` method that drops all created indexes in the correct order.