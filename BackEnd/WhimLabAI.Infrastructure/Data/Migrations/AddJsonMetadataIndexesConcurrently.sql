-- Production script to create JSON metadata indexes with CONCURRENTLY
-- This should be run directly against the database, not through EF Core migrations
-- The CONCURRENTLY option allows indexes to be created without locking the table

-- ==================================
-- GIN Indexes for JSON Metadata Queries
-- ==================================

-- Agent.Metadata - General GIN index for all metadata queries
CREATE INDEX CONCURRENTLY IF NOT EXISTS "IX_Agents_Metadata_GIN" 
ON "Agents" USING GIN ("Metadata" jsonb_path_ops);

-- Agent.Metadata - Specific indexes for common queries
-- Index for querying by agent tags
CREATE INDEX CONCURRENTLY IF NOT EXISTS "IX_Agents_Metadata_Tags" 
ON "Agents" USING GIN (("Metadata"->'tags'));

-- Index for querying by model provider
CREATE INDEX CONCURRENTLY IF NOT EXISTS "IX_Agents_Metadata_ModelProvider" 
ON "Agents" (("Metadata"->>'modelProvider'))
WHERE "Metadata"->>'modelProvider' IS NOT NULL;

-- Conversation.Metadata - General GIN index
CREATE INDEX CONCURRENTLY IF NOT EXISTS "IX_Conversations_Metadata_GIN" 
ON "Conversations" USING GIN ("Metadata" jsonb_path_ops);

-- Conversation.Metadata - Pinned conversations
CREATE INDEX CONCURRENTLY IF NOT EXISTS "IX_Conversations_Metadata_IsPinned" 
ON "Conversations" USING GIN ("Metadata" jsonb_path_ops)
WHERE "Metadata" @> '{"IsPinned": true}';

-- Order.Metadata - General GIN index
CREATE INDEX CONCURRENTLY IF NOT EXISTS "IX_Orders_Metadata_GIN" 
ON "Orders" USING GIN ("Metadata" jsonb_path_ops);

-- TokenUsage.Metadata - General GIN index
CREATE INDEX CONCURRENTLY IF NOT EXISTS "IX_TokenUsages_Metadata_GIN" 
ON "TokenUsages" USING GIN ("Metadata" jsonb_path_ops);

-- ApiKey JSON columns
CREATE INDEX CONCURRENTLY IF NOT EXISTS "IX_ApiKeys_Scopes_GIN" 
ON "ApiKeys" USING GIN ("Scopes" jsonb_path_ops);

CREATE INDEX CONCURRENTLY IF NOT EXISTS "IX_ApiKeys_IpWhitelist_GIN" 
ON "ApiKeys" USING GIN ("IpWhitelist" jsonb_path_ops);

CREATE INDEX CONCURRENTLY IF NOT EXISTS "IX_ApiKeys_AllowedDomains_GIN" 
ON "ApiKeys" USING GIN ("AllowedDomains" jsonb_path_ops);

CREATE INDEX CONCURRENTLY IF NOT EXISTS "IX_ApiKeys_Metadata_GIN" 
ON "ApiKeys" USING GIN ("Metadata" jsonb_path_ops);

-- SystemEvent.EventData
CREATE INDEX CONCURRENTLY IF NOT EXISTS "IX_SystemEvents_EventData_GIN" 
ON "SystemEvents" USING GIN ("EventData" jsonb_path_ops);

-- UserConsent.Metadata
CREATE INDEX CONCURRENTLY IF NOT EXISTS "IX_UserConsents_Metadata_GIN" 
ON "UserConsents" USING GIN ("Metadata" jsonb_path_ops);

-- DataExportRequest.IncludedDataTypes
CREATE INDEX CONCURRENTLY IF NOT EXISTS "IX_DataExportRequests_IncludedDataTypes_GIN" 
ON "DataExportRequests" USING GIN ("IncludedDataTypes" jsonb_path_ops);

-- KnowledgeBase JSON columns
CREATE INDEX CONCURRENTLY IF NOT EXISTS "IX_KnowledgeBases_VectorDbConfig_GIN" 
ON "KnowledgeBases" USING GIN ("VectorDbConfig" jsonb_path_ops);

CREATE INDEX CONCURRENTLY IF NOT EXISTS "IX_KnowledgeBases_ChunkingConfig_GIN" 
ON "KnowledgeBases" USING GIN ("ChunkingConfig" jsonb_path_ops);

CREATE INDEX CONCURRENTLY IF NOT EXISTS "IX_KnowledgeBases_Metadata_GIN" 
ON "KnowledgeBases" USING GIN ("Metadata" jsonb_path_ops);

-- Document.Metadata
CREATE INDEX CONCURRENTLY IF NOT EXISTS "IX_Documents_Metadata_GIN" 
ON "Documents" USING GIN ("Metadata" jsonb_path_ops);

-- DocumentChunk.Metadata
CREATE INDEX CONCURRENTLY IF NOT EXISTS "IX_DocumentChunks_Metadata_GIN" 
ON "DocumentChunks" USING GIN ("Metadata" jsonb_path_ops);

-- AuditLog JSON columns
CREATE INDEX CONCURRENTLY IF NOT EXISTS "IX_AuditLogs_Properties_GIN" 
ON "AuditLogs" USING GIN ("Properties" jsonb_path_ops);

CREATE INDEX CONCURRENTLY IF NOT EXISTS "IX_AuditLogs_OldValues_GIN" 
ON "AuditLogs" USING GIN ("OldValues" jsonb_path_ops);

CREATE INDEX CONCURRENTLY IF NOT EXISTS "IX_AuditLogs_NewValues_GIN" 
ON "AuditLogs" USING GIN ("NewValues" jsonb_path_ops);

CREATE INDEX CONCURRENTLY IF NOT EXISTS "IX_AuditLogs_SecurityContext_GIN" 
ON "AuditLogs" USING GIN ("SecurityContext" jsonb_path_ops);

-- AgentVersion JSON columns
CREATE INDEX CONCURRENTLY IF NOT EXISTS "IX_AgentVersions_ModelConfig_GIN" 
ON "AgentVersions" USING GIN ("ModelConfig" jsonb_path_ops);

CREATE INDEX CONCURRENTLY IF NOT EXISTS "IX_AgentVersions_PromptTemplate_GIN" 
ON "AgentVersions" USING GIN ("PromptTemplate" jsonb_path_ops);

CREATE INDEX CONCURRENTLY IF NOT EXISTS "IX_AgentVersions_KnowledgeBaseConfig_GIN" 
ON "AgentVersions" USING GIN ("KnowledgeBaseConfig" jsonb_path_ops);

-- ==================================
-- Indexes for Frequently Filtered Fields
-- ==================================

-- Conversation status and activity indexes
CREATE INDEX CONCURRENTLY IF NOT EXISTS "IX_Conversations_Status" 
ON "Conversations" ("Status");

CREATE INDEX CONCURRENTLY IF NOT EXISTS "IX_Conversations_LastMessageAt" 
ON "Conversations" ("LastMessageAt" DESC)
WHERE "LastMessageAt" IS NOT NULL;

-- Agent publishing and status indexes
CREATE INDEX CONCURRENTLY IF NOT EXISTS "IX_Agents_PublishedAt" 
ON "Agents" ("PublishedAt" DESC)
WHERE "PublishedAt" IS NOT NULL;

CREATE INDEX CONCURRENTLY IF NOT EXISTS "IX_Agents_IsPublished" 
ON "Agents" ("IsPublished")
WHERE "IsPublished" = true;

-- ==================================
-- Composite Indexes for Common Query Patterns
-- ==================================

-- Conversation search patterns
-- User's active conversations
CREATE INDEX CONCURRENTLY IF NOT EXISTS "IX_Conversations_UserId_Status_LastMessageAt" 
ON "Conversations" ("UserId", "Status", "LastMessageAt" DESC)
WHERE "IsDeleted" = false;

-- Agent's conversations
CREATE INDEX CONCURRENTLY IF NOT EXISTS "IX_Conversations_AgentId_Status_CreatedAt" 
ON "Conversations" ("AgentId", "Status", "CreatedAt" DESC)
WHERE "IsDeleted" = false;

-- Order search patterns
-- User's recent orders by payment status
CREATE INDEX CONCURRENTLY IF NOT EXISTS "IX_Orders_UserId_PaymentStatus_CreatedAt" 
ON "Orders" ("CustomerUserId", "PaymentStatus", "CreatedAt" DESC)
WHERE "IsDeleted" = false;

-- Token usage patterns
-- User's token usage by model
CREATE INDEX CONCURRENTLY IF NOT EXISTS "IX_TokenUsages_UserId_Model_CreatedAt" 
ON "TokenUsages" ("UserId", "Model", "CreatedAt" DESC);

-- ==================================
-- Partial Indexes for Soft Delete Queries
-- ==================================

-- Active records only (IsDeleted = false)
CREATE INDEX CONCURRENTLY IF NOT EXISTS "IX_Agents_Active" 
ON "Agents" ("Status", "Category", "Rating" DESC)
WHERE "IsDeleted" = false;

CREATE INDEX CONCURRENTLY IF NOT EXISTS "IX_CustomerUsers_Active" 
ON "CustomerUsers" ("Status", "CreatedAt" DESC)
WHERE "IsDeleted" = false;

CREATE INDEX CONCURRENTLY IF NOT EXISTS "IX_AdminUsers_Active" 
ON "AdminUsers" ("Status", "CreatedAt" DESC)
WHERE "IsDeleted" = false;

CREATE INDEX CONCURRENTLY IF NOT EXISTS "IX_Subscriptions_Active" 
ON "Subscriptions" ("CustomerUserId", "Status", "ExpiresAt")
WHERE "IsDeleted" = false;

-- ==================================
-- Expression Indexes for Case-Insensitive Searches
-- ==================================

-- Case-insensitive email search
CREATE INDEX CONCURRENTLY IF NOT EXISTS "IX_CustomerUsers_Email_Lower" 
ON "CustomerUsers" (LOWER("Email"));

CREATE INDEX CONCURRENTLY IF NOT EXISTS "IX_AdminUsers_Email_Lower" 
ON "AdminUsers" (LOWER("Email"));

-- Case-insensitive username search
CREATE INDEX CONCURRENTLY IF NOT EXISTS "IX_CustomerUsers_Username_Lower" 
ON "CustomerUsers" (LOWER("Username"));

CREATE INDEX CONCURRENTLY IF NOT EXISTS "IX_AdminUsers_Username_Lower" 
ON "AdminUsers" (LOWER("Username"));

-- Case-insensitive agent name search
CREATE INDEX CONCURRENTLY IF NOT EXISTS "IX_Agents_Name_Lower" 
ON "Agents" (LOWER("Name"))
WHERE "IsDeleted" = false;

-- Case-insensitive conversation title search
CREATE INDEX CONCURRENTLY IF NOT EXISTS "IX_Conversations_Title_Lower" 
ON "Conversations" (LOWER("Title"))
WHERE "Title" IS NOT NULL AND "IsDeleted" = false;

-- ==================================
-- Additional Performance Indexes
-- ==================================

-- API Key lookup optimization
CREATE INDEX CONCURRENTLY IF NOT EXISTS "IX_ApiKeys_KeyHash" 
ON "ApiKeys" ("KeyHash")
WHERE "IsActive" = true AND "IsDeleted" = false;

-- Session management
CREATE INDEX CONCURRENTLY IF NOT EXISTS "IX_CustomerUserSessions_Token_ExpiresAt" 
ON "CustomerUserSessions" ("Token", "ExpiresAt")
WHERE "IsActive" = true;

CREATE INDEX CONCURRENTLY IF NOT EXISTS "IX_AdminUserSessions_Token_ExpiresAt" 
ON "AdminUserSessions" ("Token", "ExpiresAt")
WHERE "IsActive" = true;

-- Notification delivery
CREATE INDEX CONCURRENTLY IF NOT EXISTS "IX_Notifications_UserId_IsRead_CreatedAt" 
ON "Notifications" ("UserId", "IsRead", "CreatedAt" DESC)
WHERE "IsDeleted" = false;

-- ==================================
-- Specialized Indexes
-- ==================================

-- Vector search optimization (if using pgvector)
-- Ensure btree index exists for non-vector queries
CREATE INDEX CONCURRENTLY IF NOT EXISTS "IX_DocumentChunks_KnowledgeBaseId_IsEmbedded" 
ON "DocumentChunks" ("KnowledgeBaseId", "IsEmbedded");

-- Event sourcing performance
CREATE INDEX CONCURRENTLY IF NOT EXISTS "IX_EventStores_AggregateId_Version" 
ON "EventStores" ("AggregateId", "Version" DESC);

CREATE INDEX CONCURRENTLY IF NOT EXISTS "IX_EventStores_EventType_Timestamp" 
ON "EventStores" ("EventType", "Timestamp" DESC);

-- Analyze tables to update statistics
ANALYZE "Agents";
ANALYZE "Conversations";
ANALYZE "ConversationMessages";
ANALYZE "Orders";
ANALYZE "TokenUsages";
ANALYZE "ApiKeys";
ANALYZE "CustomerUsers";
ANALYZE "AdminUsers";
ANALYZE "Subscriptions";
ANALYZE "AuditLogs";
ANALYZE "KnowledgeBases";
ANALYZE "Documents";
ANALYZE "DocumentChunks";