using Microsoft.EntityFrameworkCore.Storage.ValueConversion;
using WhimLabAI.Abstractions.Infrastructure;
using WhimLabAI.Infrastructure.Security;

namespace WhimLabAI.Infrastructure.Data.ValueConverters;

/// <summary>
/// 加密值转换器 - 自动加密/解密数据库字段
/// </summary>
public class EncryptedValueConverter : ValueConverter<string, string>
{
    public EncryptedValueConverter(IDataEncryptionService encryptionService, string fieldName)
        : base(
            v => encryptionService.EncryptField(v, fieldName),
            v => encryptionService.DecryptField(v, fieldName))
    {
    }
}

/// <summary>
/// 加密值转换器工厂
/// </summary>
public class EncryptedValueConverterFactory
{
    private readonly IDataEncryptionService _encryptionService;

    public EncryptedValueConverterFactory(IDataEncryptionService encryptionService)
    {
        _encryptionService = encryptionService;
    }

    /// <summary>
    /// 创建字符串加密转换器
    /// </summary>
    public ValueConverter<string, string> CreateStringConverter(string fieldName)
    {
        return new EncryptedValueConverter(_encryptionService, fieldName);
    }

    /// <summary>
    /// 创建可空字符串加密转换器
    /// </summary>
    public ValueConverter<string?, string?> CreateNullableStringConverter(string fieldName)
    {
        return new ValueConverter<string?, string?>(
            v => v == null ? null : _encryptionService.EncryptField(v, fieldName),
            v => v == null ? null : _encryptionService.DecryptField(v, fieldName));
    }
}

/// <summary>
/// 哈希值转换器 - 单向加密，不可逆
/// </summary>
public class HashedValueConverter : ValueConverter<string, string>
{
    public HashedValueConverter(IDataEncryptionService encryptionService)
        : base(
            v => encryptionService.ComputeHash(v),
            v => v) // 哈希不可逆，返回原值
    {
    }
}