using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using WhimLabAI.Domain.Entities.Subscription;

namespace WhimLabAI.Infrastructure.Data.Configurations;

/// <summary>
/// Token包实体配置
/// </summary>
public class TokenPackageConfiguration : IEntityTypeConfiguration<TokenPackage>
{
    public void Configure(EntityTypeBuilder<TokenPackage> builder)
    {
        builder.ToTable("token_packages");
        
        builder.HasKey(x => x.Id);
        
        builder.Property(x => x.Name)
            .IsRequired()
            .HasMaxLength(100);
            
        builder.Property(x => x.Description)
            .HasMaxLength(500);
            
        builder.Property(x => x.TokenAmount)
            .IsRequired();
            
        builder.Property(x => x.Price)
            .IsRequired()
            .HasPrecision(18, 2);
            
        builder.Property(x => x.OriginalPrice)
            .HasPrecision(18, 2);
            
        builder.Property(x => x.IsActive)
            .IsRequired()
            .HasDefaultValue(true);
            
        builder.Property(x => x.IsLimited)
            .IsRequired()
            .HasDefaultValue(false);
            
        builder.Property(x => x.SoldCount)
            .IsRequired()
            .HasDefaultValue(0);
            
        builder.Property(x => x.SortOrder)
            .IsRequired()
            .HasDefaultValue(0);
            
        builder.Property(x => x.Features)
            .HasMaxLength(1000);
            
        // 添加索引
        builder.HasIndex(x => x.IsActive)
            .HasDatabaseName("IX_TokenPackages_IsActive");
            
        builder.HasIndex(x => new { x.IsActive, x.SortOrder })
            .HasDatabaseName("IX_TokenPackages_IsActive_SortOrder");
            
        builder.HasIndex(x => x.ValidFrom)
            .HasDatabaseName("IX_TokenPackages_ValidFrom");
            
        builder.HasIndex(x => x.ValidTo)
            .HasDatabaseName("IX_TokenPackages_ValidTo");
            
        // No soft delete for TokenPackage
    }
}