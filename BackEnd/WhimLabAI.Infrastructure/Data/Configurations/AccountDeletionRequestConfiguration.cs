using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using WhimLabAI.Domain.Entities.User;

namespace WhimLabAI.Infrastructure.Data.Configurations;

/// <summary>
/// 账号注销请求实体配置
/// </summary>
public class AccountDeletionRequestConfiguration : IEntityTypeConfiguration<AccountDeletionRequest>
{
    public void Configure(EntityTypeBuilder<AccountDeletionRequest> builder)
    {
        builder.ToTable("account_deletion_requests");

        builder.HasKey(x => x.Id);

        builder.Property(x => x.CustomerUserId)
            .IsRequired();

        builder.Property(x => x.Reason)
            .IsRequired()
            .HasMaxLength(500);

        builder.Property(x => x.ReasonCategory)
            .IsRequired()
            .HasConversion<string>()
            .HasMaxLength(50);

        builder.Property(x => x.IpAddress)
            .IsRequired()
            .HasMaxLength(45); // IPv6 max length

        builder.Property(x => x.UserAgent)
            .HasMaxLength(500);

        builder.Property(x => x.Status)
            .IsRequired()
            .HasConversion<string>()
            .HasMaxLength(50);

        builder.Property(x => x.RequestedAt)
            .IsRequired();

        builder.Property(x => x.CoolingOffEndAt)
            .IsRequired();

        builder.Property(x => x.PlannedDeletionAt)
            .IsRequired();

        builder.Property(x => x.ActualDeletionAt);

        builder.Property(x => x.CancelledAt);

        builder.Property(x => x.CancellationReason)
            .HasMaxLength(500);

        builder.Property(x => x.CoolingOffReminderSent)
            .IsRequired()
            .HasDefaultValue(false);

        builder.Property(x => x.AccountFrozenNotificationSent)
            .IsRequired()
            .HasDefaultValue(false);

        builder.Property(x => x.VerificationCode)
            .HasMaxLength(10);

        builder.Property(x => x.IsVerified)
            .IsRequired()
            .HasDefaultValue(false);

        builder.Property(x => x.VerifiedAt);

        // Relationships
        builder.HasOne(x => x.CustomerUser)
            .WithMany()
            .HasForeignKey(x => x.CustomerUserId)
            .OnDelete(DeleteBehavior.Restrict);

        // Indexes
        builder.HasIndex(x => x.CustomerUserId)
            .HasDatabaseName("IX_AccountDeletionRequests_CustomerUserId");

        builder.HasIndex(x => x.Status)
            .HasDatabaseName("IX_AccountDeletionRequests_Status");

        builder.HasIndex(x => x.RequestedAt)
            .HasDatabaseName("IX_AccountDeletionRequests_RequestedAt");

        builder.HasIndex(x => x.VerificationCode)
            .HasDatabaseName("IX_AccountDeletionRequests_VerificationCode")
            .IsUnique()
            .HasFilter("[VerificationCode] IS NOT NULL");

        builder.HasIndex(x => new { x.Status, x.CoolingOffEndAt })
            .HasDatabaseName("IX_AccountDeletionRequests_Status_CoolingOffEndAt");

        builder.HasIndex(x => new { x.Status, x.PlannedDeletionAt })
            .HasDatabaseName("IX_AccountDeletionRequests_Status_PlannedDeletionAt");
    }
}