using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using WhimLabAI.Domain.Entities.Conversation;
using WhimLabAI.Shared.Enums;

namespace WhimLabAI.Infrastructure.Data.Configurations;

public class ConversationMessageConfiguration : IEntityTypeConfiguration<ConversationMessage>
{
    public void Configure(EntityTypeBuilder<ConversationMessage> builder)
    {
        builder.ToTable("conversation_messages");

        builder.HasKey(m => m.Id);

        builder.Property(m => m.Id)
            .HasColumnName("id")
            .ValueGeneratedNever();

        builder.Property(m => m.ConversationId)
            .HasColumnName("conversation_id")
            .IsRequired();

        builder.Property(m => m.Role)
            .HasColumnName("role")
            .HasMaxLength(20)
            .IsRequired();

        builder.Property(m => m.Content)
            .HasColumnName("content")
            .HasColumnType("text")
            .IsRequired();

        builder.Property(m => m.TokenCount)
            .HasColumnName("token_count")
            .IsRequired();

        builder.Property(m => m.SequenceNumber)
            .HasColumnName("sequence_number")
            .IsRequired();
            
        builder.Property(m => m.IsDeleted)
            .HasColumnName("is_deleted")
            .IsRequired();
            
        builder.Property(m => m.DeletedAt)
            .HasColumnName("deleted_at");

        builder.Property(m => m.CreatedAt)
            .HasColumnName("created_at")
            .IsRequired();

        builder.Property(m => m.UpdatedAt)
            .HasColumnName("updated_at");

        // Relationships
        builder.HasOne<Conversation>()
            .WithMany(c => c.Messages)
            .HasForeignKey(m => m.ConversationId)
            .OnDelete(DeleteBehavior.Cascade);
            
        builder.HasMany(m => m.Attachments)
            .WithOne()
            .OnDelete(DeleteBehavior.Cascade);

        // Rating is a value object, not an entity
        builder.OwnsOne(m => m.Rating, rating =>
        {
            rating.Property(r => r.Score)
                .HasColumnName("rating_score");
            rating.Property(r => r.Feedback)
                .HasColumnName("rating_feedback")
                .HasMaxLength(500);
            rating.Property(r => r.RatedAt)
                .HasColumnName("sent_at");
        });

        // Indexes
        builder.HasIndex(m => m.ConversationId);
        builder.HasIndex(m => m.SequenceNumber);
        builder.HasIndex(m => m.CreatedAt);
    }
}