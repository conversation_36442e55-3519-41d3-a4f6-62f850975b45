using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using WhimLabAI.Domain.Entities.EventSourcing;

namespace WhimLabAI.Infrastructure.Data.Configurations.EventSourcing;

public class SnapshotConfiguration : IEntityTypeConfiguration<Snapshot>
{
    public void Configure(EntityTypeBuilder<Snapshot> builder)
    {
        builder.ToTable("snapshots");

        builder.<PERSON><PERSON><PERSON>(s => s.Id);

        builder.Property(s => s.AggregateId)
            .IsRequired();

        builder.Property(s => s.AggregateType)
            .IsRequired()
            .HasMaxLength(200);

        builder.Property(s => s.SnapshotData)
            .IsRequired()
            .HasColumnType("jsonb");

        builder.Property(s => s.Version)
            .IsRequired();

        builder.Property(s => s.CreatedAt)
            .IsRequired();

        // Indexes
        builder.HasIndex(s => s.AggregateId);
        builder.HasIndex(s => new { s.AggregateId, s.Version });
        builder.HasIndex(s => s.CreatedAt);
    }
}