using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using WhimLabAI.Domain.Entities.Notification;

namespace WhimLabAI.Infrastructure.Data.Configurations;

/// <summary>
/// 短信日志实体配置
/// </summary>
public class SmsLogConfiguration : IEntityTypeConfiguration<SmsLog>
{
    public void Configure(EntityTypeBuilder<SmsLog> builder)
    {
        builder.ToTable("sms_logs");

        builder.HasKey(s => s.Id);

        builder.Property(s => s.PhoneNumber)
            .IsRequired()
            .HasMaxLength(500); // 加密后的长度

        builder.Property(s => s.PhoneNumberMask)
            .IsRequired()
            .HasMaxLength(50);

        builder.Property(s => s.CountryCode)
            .IsRequired()
            .HasMaxLength(10);

        builder.Property(s => s.Type)
            .IsRequired()
            .HasMaxLength(50);

        builder.Property(s => s.Purpose)
            .IsRequired()
            .HasMaxLength(50);

        builder.Property(s => s.TemplateId)
            .HasMaxLength(100);

        builder.Property(s => s.TemplateParams)
            .HasColumnType("jsonb");

        builder.Property(s => s.Content)
            .IsRequired()
            .HasMaxLength(1000);

        builder.Property(s => s.Provider)
            .IsRequired()
            .HasMaxLength(50);

        builder.Property(s => s.ProviderMessageId)
            .HasMaxLength(100);

        builder.Property(s => s.Status)
            .IsRequired()
            .HasMaxLength(20);

        builder.Property(s => s.ErrorCode)
            .HasMaxLength(50);

        builder.Property(s => s.ErrorMessage)
            .HasMaxLength(500);

        builder.Property(s => s.VerificationCode)
            .HasMaxLength(500); // 加密后的长度

        builder.Property(s => s.IpAddress)
            .HasMaxLength(45); // 支持IPv6

        builder.Property(s => s.UserAgent)
            .HasMaxLength(500);

        builder.Property(s => s.RequestId)
            .HasMaxLength(100);

        builder.Property(s => s.UserType)
            .HasMaxLength(20);

        builder.Property(s => s.BlockReason)
            .HasMaxLength(500);

        builder.Property(s => s.AdditionalData)
            .HasColumnType("jsonb");

        // 索引
        builder.HasIndex(s => s.PhoneNumber)
            .HasDatabaseName("IX_SmsLogs_PhoneNumber");

        builder.HasIndex(s => s.ProviderMessageId)
            .HasDatabaseName("IX_SmsLogs_ProviderMessageId");

        builder.HasIndex(s => new { s.SendTime, s.Status })
            .HasDatabaseName("IX_SmsLogs_SendTime_Status");

        builder.HasIndex(s => new { s.PhoneNumber, s.Purpose, s.Type, s.CodeExpiryTime })
            .HasDatabaseName("IX_SmsLogs_VerificationCode")
            .HasFilter("\"Type\" = 'VerificationCode' AND \"IsVerified\" = false");

        builder.HasIndex(s => s.IpAddress)
            .HasDatabaseName("IX_SmsLogs_IpAddress");

        builder.HasIndex(s => new { s.Status, s.RetryCount })
            .HasDatabaseName("IX_SmsLogs_Retry")
            .HasFilter("\"Status\" = 'Failed' AND \"IsBlocked\" = false");

        builder.HasIndex(s => s.UserId)
            .HasDatabaseName("IX_SmsLogs_UserId")
            .HasFilter("\"UserId\" IS NOT NULL");

        // 审计字段
        builder.Property(s => s.CreatedAt)
            .IsRequired();

        builder.Property(s => s.UpdatedAt);

        builder.Property(s => s.UpdatedBy)
            .HasMaxLength(100);
    }
}