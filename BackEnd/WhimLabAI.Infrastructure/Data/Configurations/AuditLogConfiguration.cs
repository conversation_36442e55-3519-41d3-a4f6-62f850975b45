using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using WhimLabAI.Domain.Entities.Audit;

namespace WhimLabAI.Infrastructure.Data.Configurations;

/// <summary>
/// 审计日志实体配置
/// </summary>
public class AuditLogConfiguration : IEntityTypeConfiguration<AuditLog>
{
    public void Configure(EntityTypeBuilder<AuditLog> builder)
    {
        builder.ToTable("audit_logs");

        builder.HasKey(e => e.Id);

        // 性能优化索引配置
        
        // 主要查询索引 - 按时间降序，包含常用字段（复合索引）
        builder.HasIndex(e => new { e.CreatedAt, e.UserId, e.Action, e.Module })
            .HasDatabaseName("IX_AuditLogs_CreatedAt_UserId_Action_Module")
            .IsDescending(true, false, false, false);

        // 用户活动查询索引
        builder.HasIndex(e => new { e.UserId, e.CreatedAt })
            .HasDatabaseName("IX_AuditLogs_UserId_CreatedAt")
            .IsDescending(false, true);

        // 模块和操作组合索引
        builder.HasIndex(e => new { e.Module, e.Action, e.CreatedAt })
            .HasDatabaseName("IX_AuditLogs_Module_Action_CreatedAt")
            .IsDescending(false, false, true);

        // 风险级别索引（用于安全审计）
        builder.HasIndex(e => new { e.RiskLevel, e.CreatedAt })
            .HasDatabaseName("IX_AuditLogs_RiskLevel_CreatedAt")
            .IsDescending(false, true);

        // 成功/失败状态索引
        builder.HasIndex(e => new { e.IsSuccess, e.CreatedAt })
            .HasDatabaseName("IX_AuditLogs_IsSuccess_CreatedAt")
            .IsDescending(false, true);

        // 实体查询索引
        builder.HasIndex(e => new { e.EntityType, e.EntityId, e.CreatedAt })
            .HasDatabaseName("IX_AuditLogs_EntityType_EntityId_CreatedAt")
            .IsDescending(false, false, true);

        // 敏感操作索引（部分索引，只索引敏感操作）
        builder.HasIndex(e => new { e.IsSensitive, e.CreatedAt })
            .HasDatabaseName("IX_AuditLogs_IsSensitive_CreatedAt")
            .IsDescending(false, true)
            .HasFilter("[IsSensitive] = 1");

        // IP地址索引（用于异常检测）
        builder.HasIndex(e => new { e.IpAddress, e.UserId, e.CreatedAt })
            .HasDatabaseName("IX_AuditLogs_IpAddress_UserId_CreatedAt")
            .IsDescending(false, false, true);

        // 请求ID索引（用于追踪，部分索引）
        builder.HasIndex(e => e.RequestId)
            .HasDatabaseName("IX_AuditLogs_RequestId")
            .HasFilter("[RequestId] IS NOT NULL");

        // 关联ID索引（用于业务流程追踪，部分索引）
        builder.HasIndex(e => e.CorrelationId)
            .HasDatabaseName("IX_AuditLogs_CorrelationId")
            .HasFilter("[CorrelationId] IS NOT NULL");

        // 字符串长度配置
        builder.Property(e => e.UserName)
            .IsRequired()
            .HasMaxLength(100);

        builder.Property(e => e.UserType)
            .IsRequired()
            .HasMaxLength(50);

        builder.Property(e => e.Action)
            .IsRequired()
            .HasMaxLength(100);

        builder.Property(e => e.Module)
            .IsRequired()
            .HasMaxLength(100);

        builder.Property(e => e.Description)
            .IsRequired()
            .HasMaxLength(500);

        builder.Property(e => e.EntityType)
            .HasMaxLength(100);

        builder.Property(e => e.EntityId)
            .HasMaxLength(100);

        builder.Property(e => e.IpAddress)
            .IsRequired()
            .HasMaxLength(50);

        builder.Property(e => e.UserAgent)
            .HasMaxLength(500);

        builder.Property(e => e.RequestId)
            .HasMaxLength(100);

        builder.Property(e => e.CorrelationId)
            .HasMaxLength(100);
            
        builder.Property(e => e.JwtId)
            .HasMaxLength(100);

        builder.Property(e => e.ControllerName)
            .HasMaxLength(100);

        builder.Property(e => e.ActionName)
            .HasMaxLength(100);

        builder.Property(e => e.HttpMethod)
            .HasMaxLength(10);

        builder.Property(e => e.RequestUrl)
            .HasMaxLength(2000);

        builder.Property(e => e.RiskLevel)
            .IsRequired()
            .HasMaxLength(20)
            .HasDefaultValue("Low");

        builder.Property(e => e.ClientInfo)
            .HasMaxLength(500);

        builder.Property(e => e.GeoLocation)
            .HasMaxLength(200);

        builder.Property(e => e.ErrorMessage)
            .HasMaxLength(2000);

        // JSON 字段配置
        builder.Property(e => e.OldValues)
            .HasColumnType("jsonb");

        builder.Property(e => e.NewValues)
            .HasColumnType("jsonb");

        builder.Property(e => e.ChangedProperties)
            .HasMaxLength(2000);

        builder.Property(e => e.RequestParameters)
            .HasColumnType("jsonb");

        builder.Property(e => e.AdditionalData)
            .HasColumnType("jsonb");

        // 大文本字段
        builder.Property(e => e.ExceptionStack)
            .HasColumnType("text");

        // 分区配置（如果需要）
        // builder.HasPartitionKey(e => e.CreatedAt);
    }
}