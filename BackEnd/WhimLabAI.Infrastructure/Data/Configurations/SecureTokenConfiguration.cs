using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using WhimLabAI.Domain.Entities.Security;

namespace WhimLabAI.Infrastructure.Data.Configurations;

/// <summary>
/// Entity configuration for SecureToken
/// </summary>
public class SecureTokenConfiguration : IEntityTypeConfiguration<SecureToken>
{
    public void Configure(EntityTypeBuilder<SecureToken> builder)
    {
        builder.ToTable("secure_tokens");
        
        builder.HasKey(t => t.Id);
        
        // Create unique index on TokenId
        builder.HasIndex(t => t.TokenId)
            .IsUnique()
            .HasDatabaseName("IX_SecureTokens_TokenId");
        
        // Create index on UserId for quick user token lookups
        builder.HasIndex(t => t.UserId)
            .HasDatabaseName("IX_SecureTokens_UserId");
        
        // Create composite index for user and token type
        builder.HasIndex(t => new { t.UserId, t.TokenType })
            .HasDatabaseName("IX_SecureTokens_UserId_TokenType");
        
        // Create index on expiration date for cleanup operations
        builder.HasIndex(t => t.ExpiresAt)
            .HasDatabaseName("IX_SecureTokens_ExpiresAt")
            .HasFilter("\"ExpiresAt\" IS NOT NULL");
        
        // Create index on token type and revocation status
        builder.HasIndex(t => new { t.TokenType, t.IsRevoked })
            .HasDatabaseName("IX_SecureTokens_TokenType_IsRevoked");
        
        // Property configurations
        builder.Property(t => t.TokenId)
            .IsRequired();
        
        builder.Property(t => t.UserId)
            .IsRequired();
        
        builder.Property(t => t.TokenType)
            .IsRequired()
            .HasMaxLength(50);
        
        builder.Property(t => t.EncryptedToken)
            .IsRequired()
            .HasColumnType("text");
        
        builder.Property(t => t.TokenHash)
            .IsRequired()
            .HasMaxLength(500);
        
        builder.Property(t => t.Salt)
            .IsRequired()
            .HasMaxLength(500);
        
        builder.Property(t => t.InitializationVector)
            .IsRequired()
            .HasMaxLength(500);
        
        builder.Property(t => t.RevocationReason)
            .HasMaxLength(500);
        
        builder.Property(t => t.Metadata)
            .HasColumnType("jsonb");
        
        builder.Property(t => t.CreatedFromIp)
            .HasMaxLength(50);
        
        builder.Property(t => t.CreatedFromUserAgent)
            .HasMaxLength(500);
        
        // Configure soft delete filter
        builder.HasQueryFilter(t => !t.IsDeleted);
    }
}