using Microsoft.Extensions.Caching.Distributed;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using System.Net;
using System.Text.Json;
using System.Text.Json.Serialization;
using WhimLabAI.Abstractions.Infrastructure;

namespace WhimLabAI.Infrastructure.ExternalServices;

/// <summary>
/// IP地理位置解析服务实现
/// </summary>
public class IpGeolocationService : IIpGeolocationService
{
    private readonly IHttpClientFactory _httpClientFactory;
    private readonly IDistributedCache _cache;
    private readonly IConfiguration _configuration;
    private readonly ILogger<IpGeolocationService> _logger;
    private const string CacheKeyPrefix = "ipgeo:";
    private const int CacheExpirationMinutes = 60; // 1小时缓存以避免超过速率限制
    private const string IpApiBaseUrl = "http://ip-api.com/json/";

    public IpGeolocationService(
        IHttpClientFactory httpClientFactory,
        IDistributedCache cache,
        IConfiguration configuration,
        ILogger<IpGeolocationService> logger)
    {
        _httpClientFactory = httpClientFactory;
        _cache = cache;
        _configuration = configuration;
        _logger = logger;
    }

    public async Task<IpGeolocationInfo?> GetLocationAsync(string ipAddress, CancellationToken cancellationToken = default)
    {
        try
        {
            // 验证IP地址格式
            if (string.IsNullOrWhiteSpace(ipAddress))
            {
                _logger.LogWarning("提供的IP地址为空");
                return GetUnknownLocation(ipAddress);
            }

            // 检查缓存
            var cacheKey = $"{CacheKeyPrefix}{ipAddress}";
            var cachedData = await _cache.GetStringAsync(cacheKey, cancellationToken);
            if (!string.IsNullOrEmpty(cachedData))
            {
                _logger.LogDebug("从缓存获取IP地址 {IpAddress} 的地理位置信息", ipAddress);
                return JsonSerializer.Deserialize<IpGeolocationInfo>(cachedData);
            }

            // 对于本地IP地址，返回默认位置
            if (IsLocalIpAddress(ipAddress))
            {
                var localInfo = new IpGeolocationInfo
                {
                    IpAddress = ipAddress,
                    City = "Local",
                    Country = "Local Network",
                    CountryCode = "LO",
                    Region = "Local",
                    Latitude = 0,
                    Longitude = 0,
                    TimeZone = TimeZoneInfo.Local.Id,
                    Isp = "Local Network",
                    IsVpn = false,
                    IsProxy = false
                };
                
                await CacheLocationAsync(cacheKey, localInfo, cancellationToken);
                return localInfo;
            }

            // 调用ip-api.com API
            var httpClient = _httpClientFactory.CreateClient();
            httpClient.Timeout = TimeSpan.FromSeconds(5); // 5秒超时
            
            var url = $"{IpApiBaseUrl}{ipAddress}?fields=status,message,country,countryCode,region,regionName,city,zip,lat,lon,timezone,isp,org,as,proxy,hosting,query";
            
            _logger.LogDebug("正在查询IP地址 {IpAddress} 的地理位置信息", ipAddress);
            var response = await httpClient.GetAsync(url, cancellationToken);
            
            if (!response.IsSuccessStatusCode)
            {
                _logger.LogWarning("IP地理位置API返回错误状态码: {StatusCode}", response.StatusCode);
                return GetUnknownLocation(ipAddress);
            }

            var json = await response.Content.ReadAsStringAsync(cancellationToken);
            var apiResponse = JsonSerializer.Deserialize<IpApiResponse>(json, new JsonSerializerOptions
            {
                PropertyNameCaseInsensitive = true
            });

            if (apiResponse == null || apiResponse.Status != "success")
            {
                _logger.LogWarning("IP地理位置API返回失败状态: {Message}", apiResponse?.Message ?? "未知错误");
                return GetUnknownLocation(ipAddress);
            }

            // 转换为我们的模型
            var geolocationInfo = new IpGeolocationInfo
            {
                IpAddress = apiResponse.Query ?? ipAddress,
                City = apiResponse.City,
                Country = apiResponse.Country,
                CountryCode = apiResponse.CountryCode,
                Region = apiResponse.RegionName ?? apiResponse.Region,
                Latitude = apiResponse.Lat,
                Longitude = apiResponse.Lon,
                TimeZone = apiResponse.Timezone,
                Isp = apiResponse.Isp ?? apiResponse.Org,
                IsVpn = apiResponse.Proxy ?? false,
                IsProxy = apiResponse.Proxy ?? false
            };
            
            // 缓存结果
            await CacheLocationAsync(cacheKey, geolocationInfo, cancellationToken);
            _logger.LogInformation("成功获取IP地址 {IpAddress} 的地理位置: {City}, {Country}", 
                ipAddress, geolocationInfo.City, geolocationInfo.Country);
            
            return geolocationInfo;
        }
        catch (HttpRequestException ex)
        {
            _logger.LogError(ex, "调用IP地理位置API时发生网络错误");
            return GetUnknownLocation(ipAddress);
        }
        catch (TaskCanceledException ex)
        {
            _logger.LogError(ex, "调用IP地理位置API时超时");
            return GetUnknownLocation(ipAddress);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "解析IP地址 {IpAddress} 的地理位置时发生错误", ipAddress);
            return GetUnknownLocation(ipAddress);
        }
    }

    public double CalculateDistance(double lat1, double lon1, double lat2, double lon2)
    {
        const double R = 6371; // 地球半径（公里）
        var dLat = ToRadians(lat2 - lat1);
        var dLon = ToRadians(lon2 - lon1);
        
        var a = Math.Sin(dLat / 2) * Math.Sin(dLat / 2) +
                Math.Cos(ToRadians(lat1)) * Math.Cos(ToRadians(lat2)) *
                Math.Sin(dLon / 2) * Math.Sin(dLon / 2);
        
        var c = 2 * Math.Atan2(Math.Sqrt(a), Math.Sqrt(1 - a));
        return R * c;
    }

    public bool IsAnomalousLocation(IpGeolocationInfo currentLocation, IEnumerable<IpGeolocationInfo> previousLocations, double thresholdKm = 500)
    {
        if (currentLocation?.Latitude == null || currentLocation.Longitude == null)
            return false;

        var recentLocations = previousLocations
            .Where(l => l.Latitude.HasValue && l.Longitude.HasValue)
            .Take(5) // 只考虑最近5个位置
            .ToList();

        if (!recentLocations.Any())
            return false;

        // 检查当前位置是否远离所有历史位置
        foreach (var location in recentLocations)
        {
            var distance = CalculateDistance(
                currentLocation.Latitude.Value,
                currentLocation.Longitude.Value,
                location.Latitude!.Value,
                location.Longitude!.Value);

            if (distance <= thresholdKm)
                return false; // 如果接近任何一个历史位置，则不是异常
        }

        return true; // 远离所有历史位置，标记为异常
    }

    private bool IsLocalIpAddress(string ipAddress)
    {
        if (string.IsNullOrWhiteSpace(ipAddress))
            return false;

        // IPv6 loopback
        if (ipAddress == "::1" || ipAddress.Equals("0:0:0:0:0:0:0:1", StringComparison.OrdinalIgnoreCase))
            return true;

        // IPv4 loopback
        if (ipAddress == "127.0.0.1" || ipAddress.StartsWith("127."))
            return true;

        // IPv4 private ranges
        if (ipAddress.StartsWith("192.168.") ||
            ipAddress.StartsWith("10.") ||
            ipAddress.StartsWith("172."))
        {
            // For 172.x.x.x, check if it's in the private range (********** - **************)
            if (ipAddress.StartsWith("172."))
            {
                var parts = ipAddress.Split('.');
                if (parts.Length >= 2 && int.TryParse(parts[1], out var secondOctet))
                {
                    return secondOctet >= 16 && secondOctet <= 31;
                }
            }
            return true;
        }

        // IPv6 private/local ranges
        if (ipAddress.StartsWith("fe80:", StringComparison.OrdinalIgnoreCase) || // Link-local
            ipAddress.StartsWith("fc00:", StringComparison.OrdinalIgnoreCase) || // Unique local
            ipAddress.StartsWith("fd00:", StringComparison.OrdinalIgnoreCase))   // Unique local
        {
            return true;
        }

        return false;
    }

    private double ToRadians(double degrees)
    {
        return degrees * Math.PI / 180;
    }

    private async Task CacheLocationAsync(string key, IpGeolocationInfo info, CancellationToken cancellationToken)
    {
        var json = JsonSerializer.Serialize(info);
        await _cache.SetStringAsync(key, json, new DistributedCacheEntryOptions
        {
            AbsoluteExpirationRelativeToNow = TimeSpan.FromMinutes(CacheExpirationMinutes)
        }, cancellationToken);
    }

    private IpGeolocationInfo GetUnknownLocation(string ipAddress)
    {
        return new IpGeolocationInfo
        {
            IpAddress = ipAddress,
            City = "Unknown",
            Country = "Unknown",
            CountryCode = "XX",
            Region = "Unknown",
            Latitude = null,
            Longitude = null,
            TimeZone = null,
            Isp = "Unknown",
            IsVpn = false,
            IsProxy = false
        };
    }

    /// <summary>
    /// ip-api.com API响应模型
    /// </summary>
    private class IpApiResponse
    {
        [JsonPropertyName("status")]
        public string Status { get; set; } = string.Empty;
        
        [JsonPropertyName("message")]
        public string? Message { get; set; }
        
        [JsonPropertyName("country")]
        public string? Country { get; set; }
        
        [JsonPropertyName("countryCode")]
        public string? CountryCode { get; set; }
        
        [JsonPropertyName("region")]
        public string? Region { get; set; }
        
        [JsonPropertyName("regionName")]
        public string? RegionName { get; set; }
        
        [JsonPropertyName("city")]
        public string? City { get; set; }
        
        [JsonPropertyName("zip")]
        public string? Zip { get; set; }
        
        [JsonPropertyName("lat")]
        public double? Lat { get; set; }
        
        [JsonPropertyName("lon")]
        public double? Lon { get; set; }
        
        [JsonPropertyName("timezone")]
        public string? Timezone { get; set; }
        
        [JsonPropertyName("isp")]
        public string? Isp { get; set; }
        
        [JsonPropertyName("org")]
        public string? Org { get; set; }
        
        [JsonPropertyName("as")]
        public string? As { get; set; }
        
        [JsonPropertyName("proxy")]
        public bool? Proxy { get; set; }
        
        [JsonPropertyName("hosting")]
        public bool? Hosting { get; set; }
        
        [JsonPropertyName("query")]
        public string? Query { get; set; }
    }
}