using WhimLabAI.Shared.Dtos.Finance;
using WhimLabAI.Shared.Results;

namespace WhimLabAI.Abstractions.Application;

/// <summary>
/// 财务报表服务接口
/// </summary>
public interface IFinancialReportService
{
    /// <summary>
    /// 获取收入报表
    /// </summary>
    /// <param name="startDate">开始日期</param>
    /// <param name="endDate">结束日期</param>
    /// <param name="granularity">时间粒度（日、周、月、季、年）</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>收入报表</returns>
    Task<Result<RevenueReportDto>> GetRevenueReportAsync(
        DateTime startDate, 
        DateTime endDate, 
        TimeGranularity granularity = TimeGranularity.Monthly,
        CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 获取成本分析报表
    /// </summary>
    /// <param name="startDate">开始日期</param>
    /// <param name="endDate">结束日期</param>
    /// <param name="granularity">时间粒度</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>成本分析报表</returns>
    Task<Result<CostAnalysisReportDto>> GetCostAnalysisReportAsync(
        DateTime startDate, 
        DateTime endDate, 
        TimeGranularity granularity = TimeGranularity.Monthly,
        CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 获取利润分析报表
    /// </summary>
    /// <param name="startDate">开始日期</param>
    /// <param name="endDate">结束日期</param>
    /// <param name="granularity">时间粒度</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>利润分析报表</returns>
    Task<Result<ProfitAnalysisReportDto>> GetProfitAnalysisReportAsync(
        DateTime startDate, 
        DateTime endDate, 
        TimeGranularity granularity = TimeGranularity.Monthly,
        CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 获取客户价值分析报表
    /// </summary>
    /// <param name="startDate">开始日期</param>
    /// <param name="endDate">结束日期</param>
    /// <param name="topN">获取前N个高价值客户</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>客户价值分析报表</returns>
    Task<Result<CustomerValueReportDto>> GetCustomerValueReportAsync(
        DateTime startDate, 
        DateTime endDate, 
        int topN = 100,
        CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 获取退款分析报表
    /// </summary>
    /// <param name="startDate">开始日期</param>
    /// <param name="endDate">结束日期</param>
    /// <param name="granularity">时间粒度</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>退款分析报表</returns>
    Task<Result<RefundAnalysisReportDto>> GetRefundAnalysisReportAsync(
        DateTime startDate, 
        DateTime endDate, 
        TimeGranularity granularity = TimeGranularity.Monthly,
        CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 获取现金流报表
    /// </summary>
    /// <param name="startDate">开始日期</param>
    /// <param name="endDate">结束日期</param>
    /// <param name="granularity">时间粒度</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>现金流报表</returns>
    Task<Result<CashFlowReportDto>> GetCashFlowReportAsync(
        DateTime startDate, 
        DateTime endDate, 
        TimeGranularity granularity = TimeGranularity.Monthly,
        CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 获取综合财务报表
    /// </summary>
    /// <param name="startDate">开始日期</param>
    /// <param name="endDate">结束日期</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>综合财务报表</returns>
    Task<Result<ComprehensiveFinancialReportDto>> GetComprehensiveReportAsync(
        DateTime startDate, 
        DateTime endDate,
        CancellationToken cancellationToken = default);
}