using WhimLabAI.Shared.Dtos;
using WhimLabAI.Shared.Dtos.Customer.Security;
using WhimLabAI.Shared.Dtos.Admin.Customer;
using WhimLabAI.Shared.Results;

namespace WhimLabAI.Abstractions.Application;

public interface ICustomerUserService
{
    // 基本信息
    Task<Result<CustomerProfileDto>> GetProfileAsync(Guid userId, CancellationToken cancellationToken = default);
    Task<Result> UpdateProfileAsync(Guid userId, UpdateProfileDto request, CancellationToken cancellationToken = default);
    Task<Result<string>> UploadAvatarAsync(Guid userId, FileUploadDto file, CancellationToken cancellationToken = default);
    
    // OAuth绑定
    Task<Result> BindOAuthAccountAsync(Guid userId, BindOAuthDto request, CancellationToken cancellationToken = default);
    Task<Result> UnbindOAuthAccountAsync(Guid userId, string provider, CancellationToken cancellationToken = default);
    
    // 登录历史
    Task<Result<PagedResult<LoginHistoryDto>>> GetLoginHistoryAsync(Guid userId, int pageNumber, int pageSize, CancellationToken cancellationToken = default);
    
    // 通知设置
    Task<Result> UpdateNotificationSettingsAsync(Guid userId, NotificationSettingsDto settings, CancellationToken cancellationToken = default);
    Task<Result<NotificationSettingsDto>> GetNotificationSettingsAsync(Guid userId, CancellationToken cancellationToken = default);
    
    // 账户安全
    Task<Result<AccountSecurityDto>> GetAccountSecurityAsync(Guid userId, CancellationToken cancellationToken = default);
    Task<Result> UpdateEmailAsync(Guid userId, UpdateEmailDto request, CancellationToken cancellationToken = default);
    Task<Result> UpdatePhoneAsync(Guid userId, UpdatePhoneDto request, CancellationToken cancellationToken = default);
    Task<Result> VerifyEmailAsync(Guid userId, VerifyEmailDto request, CancellationToken cancellationToken = default);
    Task<Result> VerifyPhoneAsync(Guid userId, VerifyPhoneDto request, CancellationToken cancellationToken = default);
    Task<Result> SendEmailVerificationCodeAsync(Guid userId, CancellationToken cancellationToken = default);
    Task<Result> SendPhoneVerificationCodeAsync(Guid userId, CancellationToken cancellationToken = default);
    
    // 双因素认证
    Task<Result<TwoFactorSetupDto>> EnableTwoFactorAsync(Guid userId, EnableTwoFactorDto request, CancellationToken cancellationToken = default);
    Task<Result> ConfirmTwoFactorAsync(Guid userId, ConfirmTwoFactorDto request, CancellationToken cancellationToken = default);
    Task<Result> DisableTwoFactorAsync(Guid userId, DisableTwoFactorDto request, CancellationToken cancellationToken = default);
    
    // 设备管理
    Task<Result<DeviceListDto>> GetDevicesAsync(Guid userId, CancellationToken cancellationToken = default);
    Task<Result> RevokeDeviceAsync(Guid userId, string deviceId, CancellationToken cancellationToken = default);
    Task<Result> RevokeAllDevicesAsync(Guid userId, string? currentDeviceId, CancellationToken cancellationToken = default);
    
    // Admin管理功能
    Task<Result<PagedResult<AdminCustomerListDto>>> SearchCustomersAsync(AdminCustomerSearchDto request, CancellationToken cancellationToken = default);
    Task<Result<AdminCustomerDetailDto>> GetCustomerDetailAsync(Guid customerId, CancellationToken cancellationToken = default);
    Task<Result> UpdateCustomerStatusAsync(Guid customerId, AdminUpdateCustomerStatusDto request, Guid adminId, CancellationToken cancellationToken = default);
    Task<Result<string>> ResetCustomerPasswordAsync(Guid customerId, AdminResetCustomerPasswordDto request, Guid adminId, CancellationToken cancellationToken = default);
    Task<Result> UnlockCustomerAccountAsync(Guid customerId, Guid adminId, CancellationToken cancellationToken = default);
    Task<Result> ResendVerificationEmailAsync(Guid customerId, Guid adminId, CancellationToken cancellationToken = default);
    Task<Result> ResendVerificationSmsAsync(Guid customerId, Guid adminId, CancellationToken cancellationToken = default);
    
    // MFA管理
    Task<Result<AdminCustomerMfaStatusDto>> GetCustomerMfaStatusAsync(Guid customerId, CancellationToken cancellationToken = default);
    Task<Result> ResetCustomerMfaAsync(Guid customerId, AdminResetCustomerMfaDto request, Guid adminId, CancellationToken cancellationToken = default);
    
    // 登录历史查看
    Task<Result<PagedResult<AdminCustomerLoginHistoryDto>>> GetCustomerLoginHistoryAsync(Guid customerId, int pageNumber, int pageSize, CancellationToken cancellationToken = default);
}

public class CustomerProfileDto
{
    public Guid Id { get; set; }
    public string Username { get; set; } = string.Empty;
    public string? Nickname { get; set; }
    public string? Avatar { get; set; }
    public string? Email { get; set; }
    public string? Phone { get; set; }
    public string? Gender { get; set; }
    public DateTime? Birthday { get; set; }
    public string? Region { get; set; }
    public string? Industry { get; set; }
    public string? Position { get; set; }
    public string? Bio { get; set; }
    public bool IsEmailVerified { get; set; }
    public bool IsPhoneVerified { get; set; }
    public DateTime CreatedAt { get; set; }
    public List<OAuthBindingDto> OAuthBindings { get; set; } = new();
}

public class UpdateProfileDto
{
    public string? Nickname { get; set; }
    public string? Gender { get; set; }
    public DateTime? Birthday { get; set; }
    public string? Region { get; set; }
    public string? Industry { get; set; }
    public string? Position { get; set; }
    public string? Bio { get; set; }
}

public class BindOAuthDto
{
    public string Provider { get; set; } = string.Empty;
    public string Code { get; set; } = string.Empty;
    public string? State { get; set; }
}

public class OAuthBindingDto
{
    public string Provider { get; set; } = string.Empty;
    public string? ProviderUserId { get; set; }
    public string? DisplayName { get; set; }
    public DateTime BindAt { get; set; }
}

public class LoginHistoryDto
{
    public Guid Id { get; set; }
    public DateTime LoginAt { get; set; }
    public string? LoginIp { get; set; }
    public string? Location { get; set; }
    public string? Device { get; set; }
    public string? Browser { get; set; }
    public string LoginMethod { get; set; } = string.Empty;
    public bool IsSuccess { get; set; }
}

public class NotificationSettingsDto
{
    public bool EmailNotification { get; set; }
    public bool SmsNotification { get; set; }
    public bool SystemNotification { get; set; }
    public bool PromotionNotification { get; set; }
    public bool SecurityAlert { get; set; }
    public bool QuotaAlert { get; set; }
    public bool? NewFeatureNotification { get; set; }
    public bool? NewsletterSubscription { get; set; }
}