using WhimLabAI.Shared.Dtos;

namespace WhimLabAI.Abstractions.Application;

public interface IDashboardService
{
    Task<DashboardStatsDto> GetDashboardStatsAsync();
    Task<IEnumerable<RecentOrderDto>> GetRecentOrdersAsync(int limit = 10);
    Task<IEnumerable<DashboardPopularAgentDto>> GetPopularAgentsAsync(int limit = 10);
    Task<Dictionary<string, int>> GetSubscriptionDistributionAsync();
    Task<RevenueChartDto> GetRevenueChartAsync(string period = "month");
    Task<CustomerStatsDto> GetCustomerStatsAsync();
    Task<DashboardAgentStatsDto> GetAgentStatsAsync();
    Task<DashboardConversationStatsDto> GetConversationStatsAsync();
    Task<TokenUsageStatsDto> GetTokenUsageStatsAsync();
}