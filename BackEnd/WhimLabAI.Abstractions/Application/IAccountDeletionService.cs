using System;
using System.Threading;
using System.Threading.Tasks;
using WhimLabAI.Shared.Dtos.Account;
using WhimLabAI.Shared.Results;

namespace WhimLabAI.Abstractions.Application;

/// <summary>
/// 账号注销服务接口
/// </summary>
public interface IAccountDeletionService
{
    /// <summary>
    /// 创建账号注销请求
    /// </summary>
    /// <param name="userId">用户ID</param>
    /// <param name="request">注销请求</param>
    /// <param name="ipAddress">IP地址</param>
    /// <param name="userAgent">用户代理</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>注销请求信息</returns>
    Task<Result<AccountDeletionRequestDto>> CreateDeletionRequestAsync(
        Guid userId,
        CreateAccountDeletionRequestDto request,
        string ipAddress,
        string? userAgent = null,
        CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 验证注销请求
    /// </summary>
    /// <param name="verificationCode">验证码</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>验证结果</returns>
    Task<Result> VerifyDeletionRequestAsync(
        string verificationCode,
        CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 撤销注销请求
    /// </summary>
    /// <param name="userId">用户ID</param>
    /// <param name="reason">撤销原因</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>撤销结果</returns>
    Task<Result> CancelDeletionRequestAsync(
        Guid userId,
        string reason,
        CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 获取用户的注销请求状态
    /// </summary>
    /// <param name="userId">用户ID</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>注销请求信息</returns>
    Task<Result<AccountDeletionRequestDto?>> GetDeletionRequestStatusAsync(
        Guid userId,
        CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 处理冷静期结束的请求（冻结账号）
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>处理的请求数量</returns>
    Task<Result<int>> ProcessCoolingOffCompletedRequestsAsync(
        CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 处理待匿名化的请求
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>处理的请求数量</returns>
    Task<Result<int>> ProcessPendingAnonymizationRequestsAsync(
        CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 发送冷静期即将结束提醒
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>发送的提醒数量</returns>
    Task<Result<int>> SendCoolingOffRemindersAsync(
        CancellationToken cancellationToken = default);
}