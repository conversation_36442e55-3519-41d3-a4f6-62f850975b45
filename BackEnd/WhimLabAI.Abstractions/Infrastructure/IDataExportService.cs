using System;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using WhimLabAI.Shared.Results;

namespace WhimLabAI.Abstractions.Infrastructure;

/// <summary>
/// 数据导出服务接口
/// </summary>
public interface IDataExportService
{
    /// <summary>
    /// 导出用户数据
    /// </summary>
    /// <param name="userId">用户ID</param>
    /// <param name="format">导出格式（json/zip）</param>
    /// <param name="dataTypes">要导出的数据类型列表</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>导出结果</returns>
    Task<Result<DataExportResult>> ExportUserDataAsync(
        Guid userId, 
        string format, 
        List<string> dataTypes, 
        CancellationToken cancellationToken = default);
}

/// <summary>
/// 数据导出结果
/// </summary>
public class DataExportResult
{
    /// <summary>
    /// 文件URL
    /// </summary>
    public string FileUrl { get; set; } = string.Empty;
    
    /// <summary>
    /// 文件大小（字节）
    /// </summary>
    public long FileSize { get; set; }
    
    /// <summary>
    /// 文件哈希值
    /// </summary>
    public string FileHash { get; set; } = string.Empty;
}