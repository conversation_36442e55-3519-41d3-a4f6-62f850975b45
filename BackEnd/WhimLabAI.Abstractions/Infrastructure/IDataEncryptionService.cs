using System.Threading.Tasks;

namespace WhimLabAI.Abstractions.Infrastructure;

/// <summary>
/// Interface for data encryption service
/// Provides AES-256 encryption for sensitive data including API keys
/// </summary>
public interface IDataEncryptionService
{
    /// <summary>
    /// Encrypts a plain text string using AES-256 encryption
    /// </summary>
    /// <param name="plainText">The text to encrypt</param>
    /// <returns>Base64 encoded encrypted string</returns>
    string EncryptString(string plainText);
    
    /// <summary>
    /// Decrypts a cipher text string
    /// </summary>
    /// <param name="cipherText">The Base64 encoded encrypted string</param>
    /// <returns>The decrypted plain text</returns>
    string DecryptString(string cipherText);
    
    /// <summary>
    /// Encrypts a byte array
    /// </summary>
    /// <param name="plainBytes">The bytes to encrypt</param>
    /// <returns>Encrypted byte array</returns>
    byte[] EncryptBytes(byte[] plainBytes);
    
    /// <summary>
    /// Decrypts a byte array
    /// </summary>
    /// <param name="cipherBytes">The encrypted bytes</param>
    /// <returns>Decrypted byte array</returns>
    byte[] DecryptBytes(byte[] cipherBytes);
    
    /// <summary>
    /// Encrypts a file
    /// </summary>
    /// <param name="inputPath">Path to the file to encrypt</param>
    /// <param name="outputPath">Path where the encrypted file will be saved</param>
    Task EncryptFileAsync(string inputPath, string outputPath);
    
    /// <summary>
    /// Decrypts a file
    /// </summary>
    /// <param name="inputPath">Path to the encrypted file</param>
    /// <param name="outputPath">Path where the decrypted file will be saved</param>
    Task DecryptFileAsync(string inputPath, string outputPath);
    
    /// <summary>
    /// Computes a SHA-256 hash of the input string
    /// </summary>
    /// <param name="input">The string to hash</param>
    /// <returns>Base64 encoded hash</returns>
    string ComputeHash(string input);
    
    /// <summary>
    /// Verifies if the input matches the provided hash
    /// </summary>
    /// <param name="input">The string to verify</param>
    /// <param name="hash">The hash to compare against</param>
    /// <returns>True if the hash matches, false otherwise</returns>
    bool VerifyHash(string input, string hash);
    
    /// <summary>
    /// Generates a cryptographically secure random key
    /// </summary>
    /// <param name="length">The length of the key in bytes (default: 32)</param>
    /// <returns>Base64 encoded secure key</returns>
    string GenerateSecureKey(int length = 32);
    
    /// <summary>
    /// Encrypts a specific field value with field-specific IV for database storage
    /// </summary>
    /// <param name="fieldValue">The value to encrypt</param>
    /// <param name="fieldName">The name of the field (used for IV generation)</param>
    /// <returns>Base64 encoded encrypted value</returns>
    string EncryptField(string fieldValue, string fieldName);
    
    /// <summary>
    /// Decrypts a specific field value
    /// </summary>
    /// <param name="encryptedValue">The encrypted value</param>
    /// <param name="fieldName">The name of the field (used for IV generation)</param>
    /// <returns>The decrypted value</returns>
    string DecryptField(string encryptedValue, string fieldName);
}