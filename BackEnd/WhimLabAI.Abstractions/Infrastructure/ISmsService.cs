using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;

namespace WhimLabAI.Abstractions.Infrastructure;

/// <summary>
/// SMS服务接口
/// </summary>
public interface ISmsService
{
    /// <summary>
    /// 发送验证码短信
    /// </summary>
    /// <param name="phoneNumber">手机号码（含国际区号，如+86）</param>
    /// <param name="code">验证码</param>
    /// <param name="purpose">用途（如：登录、注册、找回密码）</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>短信发送结果</returns>
    Task<SmsSendResult> SendVerificationCodeAsync(string phoneNumber, string code, string purpose, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 发送通知短信
    /// </summary>
    /// <param name="phoneNumber">手机号码（含国际区号）</param>
    /// <param name="templateId">短信模板ID</param>
    /// <param name="templateParams">模板参数</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>短信发送结果</returns>
    Task<SmsSendResult> SendNotificationAsync(string phoneNumber, string templateId, Dictionary<string, string> templateParams, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 批量发送短信
    /// </summary>
    /// <param name="phoneNumbers">手机号码列表</param>
    /// <param name="templateId">短信模板ID</param>
    /// <param name="templateParams">模板参数</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>批量发送结果</returns>
    Task<SmsBatchSendResult> SendBatchAsync(List<string> phoneNumbers, string templateId, Dictionary<string, string> templateParams, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 查询短信发送状态
    /// </summary>
    /// <param name="messageId">短信ID</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>短信状态</returns>
    Task<SmsStatusResult> GetStatusAsync(string messageId, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 验证手机号格式是否有效
    /// </summary>
    /// <param name="phoneNumber">手机号码</param>
    /// <returns>是否有效</returns>
    bool ValidatePhoneNumber(string phoneNumber);
    
    /// <summary>
    /// 获取手机号所属地区
    /// </summary>
    /// <param name="phoneNumber">手机号码</param>
    /// <returns>地区信息</returns>
    PhoneRegionInfo GetPhoneRegion(string phoneNumber);
}

/// <summary>
/// 短信发送结果
/// </summary>
public class SmsSendResult
{
    /// <summary>
    /// 是否成功
    /// </summary>
    public bool Success { get; set; }
    
    /// <summary>
    /// 短信ID（用于查询状态）
    /// </summary>
    public string MessageId { get; set; } = string.Empty;
    
    /// <summary>
    /// 错误代码
    /// </summary>
    public string? ErrorCode { get; set; }
    
    /// <summary>
    /// 错误消息
    /// </summary>
    public string? ErrorMessage { get; set; }
    
    /// <summary>
    /// 提供商名称
    /// </summary>
    public string Provider { get; set; } = string.Empty;
    
    /// <summary>
    /// 发送时间
    /// </summary>
    public DateTime SendTime { get; set; }
    
    /// <summary>
    /// 费用（分）
    /// </summary>
    public int Cost { get; set; }
}

/// <summary>
/// 批量短信发送结果
/// </summary>
public class SmsBatchSendResult
{
    /// <summary>
    /// 总数
    /// </summary>
    public int TotalCount { get; set; }
    
    /// <summary>
    /// 成功数
    /// </summary>
    public int SuccessCount { get; set; }
    
    /// <summary>
    /// 失败数
    /// </summary>
    public int FailureCount { get; set; }
    
    /// <summary>
    /// 各个手机号的发送结果
    /// </summary>
    public List<SmsIndividualResult> Results { get; set; } = new();
}

/// <summary>
/// 单个短信发送结果
/// </summary>
public class SmsIndividualResult
{
    /// <summary>
    /// 手机号码
    /// </summary>
    public string PhoneNumber { get; set; } = string.Empty;
    
    /// <summary>
    /// 短信ID
    /// </summary>
    public string MessageId { get; set; } = string.Empty;
    
    /// <summary>
    /// 是否成功
    /// </summary>
    public bool Success { get; set; }
    
    /// <summary>
    /// 错误信息
    /// </summary>
    public string? ErrorMessage { get; set; }
}

/// <summary>
/// 短信状态结果
/// </summary>
public class SmsStatusResult
{
    /// <summary>
    /// 短信ID
    /// </summary>
    public string MessageId { get; set; } = string.Empty;
    
    /// <summary>
    /// 状态（Pending/Sent/Delivered/Failed）
    /// </summary>
    public string Status { get; set; } = string.Empty;
    
    /// <summary>
    /// 送达时间
    /// </summary>
    public DateTime? DeliveredTime { get; set; }
    
    /// <summary>
    /// 失败原因
    /// </summary>
    public string? FailureReason { get; set; }
}

/// <summary>
/// 手机号地区信息
/// </summary>
public class PhoneRegionInfo
{
    /// <summary>
    /// 国家代码
    /// </summary>
    public string CountryCode { get; set; } = string.Empty;
    
    /// <summary>
    /// 国家名称
    /// </summary>
    public string CountryName { get; set; } = string.Empty;
    
    /// <summary>
    /// 地区（如省份）
    /// </summary>
    public string? Region { get; set; }
    
    /// <summary>
    /// 运营商
    /// </summary>
    public string? Carrier { get; set; }
    
    /// <summary>
    /// 是否为中国大陆号码
    /// </summary>
    public bool IsMainlandChina { get; set; }
}