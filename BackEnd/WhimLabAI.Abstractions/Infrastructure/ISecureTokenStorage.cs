using System;
using System.Threading.Tasks;

namespace WhimLabAI.Abstractions.Infrastructure;

/// <summary>
/// Interface for secure storage of sensitive tokens (refresh tokens, API keys, etc.)
/// </summary>
public interface ISecureTokenStorage
{
    /// <summary>
    /// Stores a token securely with encryption
    /// </summary>
    /// <param name="tokenId">Unique identifier for the token</param>
    /// <param name="token">The token value to store</param>
    /// <param name="userId">User ID associated with the token</param>
    /// <param name="tokenType">Type of token (RefreshToken, ApiKey, etc.)</param>
    /// <param name="expiresAt">Optional expiration date</param>
    /// <param name="metadata">Optional metadata</param>
    Task StoreTokenAsync(
        Guid tokenId,
        string token,
        Guid userId,
        string tokenType,
        DateTime? expiresAt = null,
        Dictionary<string, string>? metadata = null);

    /// <summary>
    /// Retrieves a token securely with decryption
    /// </summary>
    /// <param name="tokenId">The token identifier</param>
    /// <returns>The decrypted token value or null if not found/expired</returns>
    Task<string?> GetTokenAsync(Guid tokenId);

    /// <summary>
    /// Validates a token without retrieving it
    /// </summary>
    /// <param name="tokenId">The token identifier</param>
    /// <param name="tokenHash">Hash of the token to validate</param>
    /// <returns>True if valid, false otherwise</returns>
    Task<bool> ValidateTokenAsync(Guid tokenId, string tokenHash);

    /// <summary>
    /// Revokes a token
    /// </summary>
    /// <param name="tokenId">The token identifier</param>
    Task RevokeTokenAsync(Guid tokenId);

    /// <summary>
    /// Revokes all tokens for a user
    /// </summary>
    /// <param name="userId">The user identifier</param>
    /// <param name="tokenType">Optional token type filter</param>
    Task RevokeAllUserTokensAsync(Guid userId, string? tokenType = null);

    /// <summary>
    /// Cleans up expired tokens
    /// </summary>
    Task CleanupExpiredTokensAsync();
}