using System;
using System.Threading;
using System.Threading.Tasks;

namespace WhimLabAI.Abstractions.Infrastructure;

/// <summary>
/// 数据匿名化服务接口
/// </summary>
public interface IDataAnonymizationService
{
    /// <summary>
    /// 匿名化用户数据
    /// </summary>
    /// <param name="userId">用户ID</param>
    /// <param name="cancellationToken">取消令牌</param>
    Task AnonymizeUserAsync(Guid userId, CancellationToken cancellationToken = default);
}