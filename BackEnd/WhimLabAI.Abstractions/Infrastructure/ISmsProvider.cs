using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;

namespace WhimLabAI.Abstractions.Infrastructure;

/// <summary>
/// SMS提供商接口
/// </summary>
public interface ISmsProvider
{
    /// <summary>
    /// 提供商名称
    /// </summary>
    string Name { get; }
    
    /// <summary>
    /// 是否支持该地区
    /// </summary>
    /// <param name="countryCode">国家代码</param>
    /// <returns>是否支持</returns>
    bool SupportsRegion(string countryCode);
    
    /// <summary>
    /// 发送短信
    /// </summary>
    /// <param name="phoneNumber">手机号码</param>
    /// <param name="message">短信内容</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>发送结果</returns>
    Task<SmsSendResult> SendAsync(string phoneNumber, string message, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 发送模板短信
    /// </summary>
    /// <param name="phoneNumber">手机号码</param>
    /// <param name="templateId">模板ID</param>
    /// <param name="templateParams">模板参数</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>发送结果</returns>
    Task<SmsSendResult> SendTemplateAsync(string phoneNumber, string templateId, Dictionary<string, string> templateParams, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 批量发送短信
    /// </summary>
    /// <param name="phoneNumbers">手机号码列表</param>
    /// <param name="templateId">模板ID</param>
    /// <param name="templateParams">模板参数</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>批量发送结果</returns>
    Task<SmsBatchSendResult> SendBatchAsync(List<string> phoneNumbers, string templateId, Dictionary<string, string> templateParams, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 查询短信状态
    /// </summary>
    /// <param name="messageId">短信ID</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>状态结果</returns>
    Task<SmsStatusResult> GetStatusAsync(string messageId, CancellationToken cancellationToken = default);
}