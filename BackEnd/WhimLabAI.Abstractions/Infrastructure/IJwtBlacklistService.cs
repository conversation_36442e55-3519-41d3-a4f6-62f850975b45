namespace WhimLabAI.Abstractions.Infrastructure;

/// <summary>
/// JWT黑名单服务接口，用于管理被撤销的JWT令牌
/// </summary>
public interface IJwtBlacklistService
{
    /// <summary>
    /// 将JWT添加到黑名单
    /// </summary>
    /// <param name="jti">JWT ID</param>
    /// <param name="userId">用户ID</param>
    /// <param name="expiry">令牌过期时间</param>
    /// <param name="reason">撤销原因</param>
    /// <param name="cancellationToken">取消令牌</param>
    Task RevokeTokenAsync(string jti, Guid userId, DateTime expiry, string reason, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 批量撤销用户的所有令牌
    /// </summary>
    /// <param name="userId">用户ID</param>
    /// <param name="reason">撤销原因</param>
    /// <param name="cancellationToken">取消令牌</param>
    Task RevokeAllUserTokensAsync(Guid userId, string reason, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 检查令牌是否已被撤销
    /// </summary>
    /// <param name="jti">JWT ID</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>如果令牌已被撤销返回true</returns>
    Task<bool> IsTokenRevokedAsync(string jti, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 清理过期的黑名单记录
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    Task CleanupExpiredTokensAsync(CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 获取用户的撤销令牌列表
    /// </summary>
    /// <param name="userId">用户ID</param>
    /// <param name="cancellationToken">取消令牌</param>
    Task<List<RevokedTokenInfo>> GetUserRevokedTokensAsync(Guid userId, CancellationToken cancellationToken = default);
}

/// <summary>
/// 撤销令牌信息
/// </summary>
public class RevokedTokenInfo
{
    public string Jti { get; set; } = string.Empty;
    public Guid UserId { get; set; }
    public DateTime RevokedAt { get; set; }
    public DateTime ExpiresAt { get; set; }
    public string Reason { get; set; } = string.Empty;
    public string? RevokedBy { get; set; }
}