using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;

namespace WhimLabAI.Abstractions.Infrastructure;

/// <summary>
/// 短信模板服务接口
/// </summary>
public interface ISmsTemplateService
{
    /// <summary>
    /// 获取模板
    /// </summary>
    /// <param name="templateKey">模板键（如：verification_code_login）</param>
    /// <param name="provider">提供商</param>
    /// <param name="region">地区</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>模板信息</returns>
    Task<SmsTemplate?> GetTemplateAsync(string templateKey, string provider, string region, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 渲染模板内容
    /// </summary>
    /// <param name="template">模板</param>
    /// <param name="parameters">参数</param>
    /// <returns>渲染后的内容</returns>
    string RenderTemplate(SmsTemplate template, Dictionary<string, string> parameters);
    
    /// <summary>
    /// 验证模板参数
    /// </summary>
    /// <param name="template">模板</param>
    /// <param name="parameters">参数</param>
    /// <returns>验证结果</returns>
    bool ValidateParameters(SmsTemplate template, Dictionary<string, string> parameters);
}

/// <summary>
/// 短信模板
/// </summary>
public class SmsTemplate
{
    /// <summary>
    /// 模板ID（提供商的模板ID）
    /// </summary>
    public string TemplateId { get; set; } = string.Empty;
    
    /// <summary>
    /// 模板键（系统内部标识）
    /// </summary>
    public string TemplateKey { get; set; } = string.Empty;
    
    /// <summary>
    /// 模板名称
    /// </summary>
    public string Name { get; set; } = string.Empty;
    
    /// <summary>
    /// 模板内容
    /// </summary>
    public string Content { get; set; } = string.Empty;
    
    /// <summary>
    /// 签名
    /// </summary>
    public string Signature { get; set; } = string.Empty;
    
    /// <summary>
    /// 必需参数列表
    /// </summary>
    public List<string> RequiredParameters { get; set; } = new();
    
    /// <summary>
    /// 提供商
    /// </summary>
    public string Provider { get; set; } = string.Empty;
    
    /// <summary>
    /// 地区
    /// </summary>
    public string Region { get; set; } = string.Empty;
    
    /// <summary>
    /// 是否启用
    /// </summary>
    public bool IsEnabled { get; set; }
}