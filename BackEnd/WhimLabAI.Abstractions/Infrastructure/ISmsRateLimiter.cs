using System.Threading;
using System.Threading.Tasks;

namespace WhimLabAI.Abstractions.Infrastructure;

/// <summary>
/// 短信频率限制器接口
/// </summary>
public interface ISmsRateLimiter
{
    /// <summary>
    /// 检查是否可以发送短信
    /// </summary>
    /// <param name="phoneNumber">手机号码</param>
    /// <param name="ipAddress">IP地址</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>速率限制结果</returns>
    Task<RateLimitResult> CheckRateLimitAsync(string phoneNumber, string? ipAddress, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 记录短信发送
    /// </summary>
    /// <param name="phoneNumber">手机号码</param>
    /// <param name="ipAddress">IP地址</param>
    /// <param name="cancellationToken">取消令牌</param>
    Task RecordSendAsync(string phoneNumber, string? ipAddress, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 重置手机号的限制
    /// </summary>
    /// <param name="phoneNumber">手机号码</param>
    /// <param name="cancellationToken">取消令牌</param>
    Task ResetPhoneLimitAsync(string phoneNumber, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 屏蔽手机号
    /// </summary>
    /// <param name="phoneNumber">手机号码</param>
    /// <param name="reason">屏蔽原因</param>
    /// <param name="durationMinutes">屏蔽时长（分钟），null表示永久</param>
    /// <param name="cancellationToken">取消令牌</param>
    Task BlockPhoneAsync(string phoneNumber, string reason, int? durationMinutes = null, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 解除手机号屏蔽
    /// </summary>
    /// <param name="phoneNumber">手机号码</param>
    /// <param name="cancellationToken">取消令牌</param>
    Task UnblockPhoneAsync(string phoneNumber, CancellationToken cancellationToken = default);
}

/// <summary>
/// 速率限制结果
/// </summary>
public class RateLimitResult
{
    /// <summary>
    /// 是否允许发送
    /// </summary>
    public bool IsAllowed { get; set; }
    
    /// <summary>
    /// 限制原因
    /// </summary>
    public string? Reason { get; set; }
    
    /// <summary>
    /// 下次可发送时间
    /// </summary>
    public DateTime? RetryAfter { get; set; }
    
    /// <summary>
    /// 剩余发送次数
    /// </summary>
    public int? RemainingCount { get; set; }
    
    /// <summary>
    /// 创建允许结果
    /// </summary>
    public static RateLimitResult Allowed(int remainingCount)
    {
        return new RateLimitResult
        {
            IsAllowed = true,
            RemainingCount = remainingCount
        };
    }
    
    /// <summary>
    /// 创建拒绝结果
    /// </summary>
    public static RateLimitResult Denied(string reason, DateTime? retryAfter = null)
    {
        return new RateLimitResult
        {
            IsAllowed = false,
            Reason = reason,
            RetryAfter = retryAfter,
            RemainingCount = 0
        };
    }
}