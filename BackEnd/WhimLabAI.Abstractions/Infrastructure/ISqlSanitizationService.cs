using System.Collections.Generic;

namespace WhimLabAI.Abstractions.Infrastructure
{
    /// <summary>
    /// Service for SQL injection prevention and query sanitization
    /// </summary>
    public interface ISqlSanitizationService
    {
        /// <summary>
        /// Sanitize a SQL parameter value
        /// </summary>
        /// <param name="value">Value to sanitize</param>
        /// <returns>Sanitized value</returns>
        string SanitizeParameter(string value);

        /// <summary>
        /// Validate if a column name is safe to use in queries
        /// </summary>
        /// <param name="columnName">Column name to validate</param>
        /// <param name="allowedColumns">List of allowed column names</param>
        /// <returns>True if column name is safe</returns>
        bool IsValidColumnName(string columnName, IEnumerable<string> allowedColumns);

        /// <summary>
        /// Validate if a table name is safe to use in queries
        /// </summary>
        /// <param name="tableName">Table name to validate</param>
        /// <param name="allowedTables">List of allowed table names</param>
        /// <returns>True if table name is safe</returns>
        bool IsValidTableName(string tableName, IEnumerable<string> allowedTables);

        /// <summary>
        /// Check if a query contains potential SQL injection patterns
        /// </summary>
        /// <param name="query">SQL query to check</param>
        /// <returns>True if query appears safe</returns>
        bool IsSafeQuery(string query);

        /// <summary>
        /// Escape special characters in LIKE pattern
        /// </summary>
        /// <param name="pattern">LIKE pattern to escape</param>
        /// <returns>Escaped pattern</returns>
        string EscapeLikePattern(string pattern);

        /// <summary>
        /// Build safe ORDER BY clause
        /// </summary>
        /// <param name="column">Column to order by</param>
        /// <param name="direction">Sort direction (ASC/DESC)</param>
        /// <param name="allowedColumns">List of allowed columns</param>
        /// <returns>Safe ORDER BY clause or empty string if invalid</returns>
        string BuildSafeOrderByClause(string column, string direction, IEnumerable<string> allowedColumns);
    }
}