namespace WhimLabAI.Abstractions.Infrastructure;

/// <summary>
/// 支付签名服务接口，用于验证支付请求的完整性和真实性
/// </summary>
public interface IPaymentSignatureService
{
    /// <summary>
    /// 生成支付请求签名
    /// </summary>
    /// <param name="parameters">支付参数字典</param>
    /// <param name="secretKey">密钥</param>
    /// <returns>签名字符串</returns>
    string GenerateSignature(Dictionary<string, string> parameters, string secretKey);
    
    /// <summary>
    /// 验证支付回调签名
    /// </summary>
    /// <param name="parameters">回调参数字典</param>
    /// <param name="signature">待验证的签名</param>
    /// <param name="secretKey">密钥</param>
    /// <returns>签名是否有效</returns>
    bool VerifySignature(Dictionary<string, string> parameters, string signature, string secretKey);
    
    /// <summary>
    /// 生成请求时间戳
    /// </summary>
    /// <returns>Unix时间戳</returns>
    long GenerateTimestamp();
    
    /// <summary>
    /// 生成随机字符串
    /// </summary>
    /// <param name="length">长度</param>
    /// <returns>随机字符串</returns>
    string GenerateNonce(int length = 32);
    
    /// <summary>
    /// 验证请求时间戳是否在有效期内
    /// </summary>
    /// <param name="timestamp">时间戳</param>
    /// <param name="maxAgeSeconds">最大有效期（秒）</param>
    /// <returns>是否有效</returns>
    bool IsTimestampValid(long timestamp, int maxAgeSeconds = 300);
    
    /// <summary>
    /// 获取支付网关特定的签名算法
    /// </summary>
    /// <param name="gateway">支付网关名称</param>
    /// <returns>签名算法实现</returns>
    IPaymentSignatureAlgorithm GetAlgorithm(string gateway);
    
    /// <summary>
    /// 生成支付宝签名
    /// </summary>
    /// <param name="parameters">参数字典</param>
    /// <param name="privateKey">私钥</param>
    /// <returns>签名</returns>
    string GenerateAlipaySignature(Dictionary<string, string> parameters, string privateKey);
    
    /// <summary>
    /// 验证支付宝签名
    /// </summary>
    /// <param name="parameters">参数字典</param>
    /// <param name="signature">签名</param>
    /// <param name="publicKey">公钥</param>
    /// <returns>是否有效</returns>
    bool VerifyAlipaySignature(Dictionary<string, string> parameters, string signature, string publicKey);
    
    /// <summary>
    /// 生成微信支付签名
    /// </summary>
    /// <param name="parameters">参数字典</param>
    /// <param name="apiKey">API密钥</param>
    /// <returns>签名</returns>
    string GenerateWeChatSignature(Dictionary<string, string> parameters, string apiKey);
    
    /// <summary>
    /// 验证微信支付签名
    /// </summary>
    /// <param name="parameters">参数字典</param>
    /// <param name="signature">签名</param>
    /// <param name="apiKey">API密钥</param>
    /// <returns>是否有效</returns>
    bool VerifyWeChatSignature(Dictionary<string, string> parameters, string signature, string apiKey);
}

/// <summary>
/// 支付签名算法接口
/// </summary>
public interface IPaymentSignatureAlgorithm
{
    /// <summary>
    /// 算法名称
    /// </summary>
    string AlgorithmName { get; }
    
    /// <summary>
    /// 生成签名
    /// </summary>
    /// <param name="data">待签名数据</param>
    /// <param name="secretKey">密钥</param>
    /// <returns>签名</returns>
    string Sign(string data, string secretKey);
    
    /// <summary>
    /// 构建待签名字符串
    /// </summary>
    /// <param name="parameters">参数字典</param>
    /// <returns>待签名字符串</returns>
    string BuildSignString(Dictionary<string, string> parameters);
}