using System.Threading.Tasks;

namespace WhimLabAI.Abstractions.Infrastructure
{
    /// <summary>
    /// Service for secure management of secrets and API keys
    /// </summary>
    public interface ISecretManagementService
    {
        /// <summary>
        /// Retrieve a secret value by key
        /// </summary>
        /// <param name="key">Secret key</param>
        /// <returns>Secret value</returns>
        Task<string?> GetSecretAsync(string key);

        /// <summary>
        /// Store or update a secret
        /// </summary>
        /// <param name="key">Secret key</param>
        /// <param name="value">Secret value</param>
        /// <returns>True if successfully stored</returns>
        Task<bool> SetSecretAsync(string key, string value);

        /// <summary>
        /// Delete a secret
        /// </summary>
        /// <param name="key">Secret key</param>
        /// <returns>True if successfully deleted</returns>
        Task<bool> DeleteSecretAsync(string key);

        /// <summary>
        /// Check if a secret exists
        /// </summary>
        /// <param name="key">Secret key</param>
        /// <returns>True if secret exists</returns>
        Task<bool> SecretExistsAsync(string key);

        /// <summary>
        /// Rotate a secret (generate new value and update)
        /// </summary>
        /// <param name="key">Secret key</param>
        /// <returns>New secret value</returns>
        Task<string> RotateSecretAsync(string key);
    }
}