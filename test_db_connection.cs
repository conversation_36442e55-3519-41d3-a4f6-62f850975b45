using System;
using System.Threading.Tasks;
using Npgsql;

class Program
{
    static async Task Main(string[] args)
    {
        Console.WriteLine("Testing database connection...");
        
        // 使用默认的开发环境连接字符串
        var connectionString = "Host=localhost;Port=5432;Database=whimlabai;Username=postgres;Password=***********;Pooling=true;MinPoolSize=5;MaxPoolSize=100;ConnectionLifetime=300;CommandTimeout=30;Timeout=30";
        
        try
        {
            Console.WriteLine("Attempting to connect to database...");
            using var connection = new NpgsqlConnection(connectionString);
            
            Console.WriteLine("Opening connection...");
            await connection.OpenAsync();
            
            Console.WriteLine("Connection opened successfully!");
            Console.WriteLine($"Database: {connection.Database}");
            Console.WriteLine($"Server version: {connection.PostgreSqlVersion}");
            
            // 测试简单查询
            using var command = new NpgsqlCommand("SELECT 1", connection);
            var result = await command.ExecuteScalarAsync();
            Console.WriteLine($"Test query result: {result}");
            
            Console.WriteLine("Database connection test PASSED!");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Database connection test FAILED: {ex.Message}");
            Console.WriteLine($"Exception type: {ex.GetType().Name}");
            if (ex.InnerException != null)
            {
                Console.WriteLine($"Inner exception: {ex.InnerException.Message}");
            }
        }
    }
}
