企业级AI智能体平台完整业务需求文档
引言
本文档在项目初始需求的基础上，进行了深度的业务分析、边界划分和功能补充。核心目标是为"WhimlabAI企业级AI智能体平台"的开发提供一份清晰、完整、无歧义的业务蓝图,后端全部都基于.net 9.0实现,其中Admin前端(后台管理)UI/UX基于.net 9.0 Blazor Auto 渲染模式 & MudBlazor(文档:https://mudblazor.com/docs/overview),而Customer前端UI/UX基于.net 9.0 MASA Blazor MAUI App(文档:https://www.fluentui-blazor.net )。
架构说明
•	单体应用架构：整个平台采用单体应用架构进行部署，但在开发时采用多项目分层组织
•	多项目分层：通过多个独立项目（WebApi、Application、Domain、Infrastructure等）来组织代码，每个项目负责特定的职责
•	接口抽象隔离：引入独立的接口抽象层项目（Abstractions），定义各层之间的契约，实现层间完全解耦
•	模块文件夹组织：在每个项目内部，不同的业务模块通过文件夹结构进行组织和区分
核心优化点：
•	边界明确化：每个功能点都明确其主要用户是 [Customer]（客户端用户）还是 [Admin]（管理端用户），或两者皆有（[Shared]）
•	流程闭环：将离散的功能点串联成完整的用户旅程和业务流程，例如从注册、试用、选择套餐到支付成功的完整闭环
•	需求深化：补充了原始文档中隐含的或缺失的业务规则、异常流程和非功能性需求
•	结构优化：采用多项目分层架构，通过独立的接口抽象层实现各层解耦，在每个项目内部按业务模块组织文件夹结构
I. 核心业务域：身份与访问管理 (IAM)
此域负责管理平台所有用户的身份、认证、授权和个人信息。
1.1 Customer端用户系统 [Customer]
1.1.1 Customer用户注册
•	注册方式：
◦	用户名注册：用户输入用户名 -> 系统实时验证格式合法性与唯一性 -> 用户设置密码 -> 完成注册
◦	邮箱注册：用户输入邮箱 -> 系统实时验证格式合法性（RFC 5322标准）与唯一性 -> 向邮箱发送6位数字验证码（验证码5分钟内有效） -> 用户输入验证码进行校验（最多允许3次错误尝试） -> 用户设置密码 -> 完成注册
◦	手机号注册：用户选择国家/地区代码 -> 输入手机号码 -> 系统验证手机号格式与唯一性 -> 发送6位短信验证码（含60秒防刷重发限制，验证码3分钟有效） -> 后续流程同邮箱注册
◦	第三方OAuth注册：支持微信扫码、GitHub、Google账号
▪	首次登录：若第三方账号的关联邮箱在平台未注册，则自动创建新用户，并引导用户设置一个初始密码（用于未来可能的密码登录方式），或直接进入平台
▪	账号关联：若第三方账号的关联邮箱已在平台注册，系统将提示用户"该邮箱已注册，请登录后在个人中心绑定"，以避免创建重复账号
•	通用规则与流程：
◦	密码策略：长度8-20位，必须包含大写字母、小写字母、数字和特殊字符。注册页面提供实时密码强度指示器（弱、中、强）
◦	自动激活Free订阅：注册成功后，系统自动为用户激活"Free套餐"（无时间限制）
◦	初始化Free配额：Free订阅激活的同时，系统为用户账户初始化5,000 Tokens的月度配额
◦	欢迎流程：注册成功后，系统应自动将用户登录，并跳转至新手引导页面。同时，向注册邮箱发送一封欢迎邮件
1.1.2 Customer用户登录
•	登录方式：
◦	账号密码登录：输入框支持用户名、邮箱或手机号作为用户名。密码连续输入错误5次后，该账号将被锁定15分钟，并向其绑定的邮箱或手机发送安全提醒
◦	验证码登录：用户可通过邮箱或手机号接收验证码进行快捷登录，同样有发送频率限制
◦	扫码登录：PC端网页生成一个动态二维码（有效期2分钟，过期后可点击刷新），已登录的移动端App扫描后，在移动端上确认授权，PC端网页即可自动登录
◦	第三方OAuth登录：用户点击微信、GitHub、Google图标，跳转至相应服务进行授权，成功后自动登录平台
•	安全与会话管理：
◦	提供"记住我"（或"自动登录"）选项，通过长效Refresh Token实现7天或30天的免密登录
◦	用户登录成功后，在个人中心或欢迎提示中显示本次及上次登录的时间与IP地理位置
◦	检测到异地或不常用设备登录时，必须向用户绑定的邮箱或手机发送安全警报
1.1.3 Customer个人中心
•	基本信息管理：
◦	头像上传（支持JPG/PNG/GIF，最大5MB，提供在线裁剪功能）
◦	昵称修改（2-20个字符，为防止滥用，30天内仅可修改一次）
◦	个人简介、性别、生日、地区、行业、职位等可选信息
•	账户安全管理：
◦	修改登录密码（必须验证原密码）
◦	绑定、更换、解绑手机号和邮箱地址（所有变更操作均需通过原渠道或密码进行安全验证）
◦	管理已绑定的第三方OAuth账号（可进行解绑）
◦	登录设备管理：清晰列出所有已授权登录的设备（设备类型、操作系统、最近活动时间、IP地址），并提供"远程下线"功能
◦	登录日志查看：提供详细的登录历史记录，包括时间、IP、地点、设备、登录方式（密码/OAuth/扫码）、登录结果（成功/失败）
◦	两步验证（MFA）：支持启用/禁用基于时间的一次性密码（TOTP），如Google Authenticator或Microsoft Authenticator。启用时需引导用户完成绑定流程
•	通知设置：
◦	用户可精细化设置各类通知的接收渠道和开关
◦	通知类型：系统公告、账单与支付通知、安全提醒、产品更新、营销活动邮件
◦	通知渠道：站内信、邮件、短信、浏览器推送
◦	支持设置"免打扰时段"
1.1.4 Customer账号注销
•	用户可在个人中心发起注销申请，申请前需通过密码或MFA进行身份验证
•	系统会明确告知用户注销的后果，包括数据清除范围、余额处理等
•	系统提供7天的"冷静期"，在此期间用户可以随时登录并撤销注销申请
•	冷静期过后，账号将被标记为"已注销"并被冻结，所有关联数据（如对话历史、订阅信息）将在30天后被匿名化处理或永久、不可逆地删除
1.2 Admin端管理系统 [Admin]
1.2.1 Customer用户与管理员用户管理
•	客户用户管理 (Customer User Management)：
◦	提供所有客户用户的列表，支持通过用户ID、昵称、邮箱、手机号、订阅状态等条件进行搜索、筛选和排序
◦	可查看任一客户的详细信息：基本资料、注册时间、当前订阅套餐、配额使用详情、完整的登录历史和关键操作日志
◦	可对用户账号进行管理操作：手动激活、禁用/封禁（被封禁的用户将无法登录和使用服务）。所有操作必须记录操作人、时间和原因
◦	可为特定用户手动调整订阅或配额（例如，作为客户支持补偿），同样需要记录在案
•	管理员用户管理 (Admin User Management) [仅限超级管理员]：
◦	创建、编辑和删除其他管理员账号
◦	为管理员分配一个或多个角色（见1.2.2）
◦	在管理员忘记密码时，提供安全的密码重置功能
◦	查看所有管理员的操作日志
◦	超级管理员账号由系统初始化时创建，不可被删除
1.2.2 角色与权限管理 (RBAC)
•	权限 (Permission)：平台所有操作被定义为原子化的权限点，具有层级结构，例如：
◦	users:customers:read (查看客户列表)
◦	users:customers:update_status (修改客户状态)
◦	users:admins:read (查看Admin列表)
◦	users:admins:update_status (修改Admin状态)
◦	agents:create (创建智能体)
◦	subscriptions:plans:edit (编辑订阅套餐)
◦	权限覆盖到菜单、按钮、API等各个层级
•	角色 (Role)：角色是权限的集合
◦	系统预设角色：超级管理员 (拥有所有权限)、运营管理员 (管理用户、智能体市场、订阅)、财务管理员 (管理订单、支付、退款)、技术支持 (查看用户信息、处理工单)、只读观察员 (只能查看数据，不能修改)
◦	自定义角色：超级管理员可以创建新的角色，并从权限列表中自由勾选权限组合
•	分配：超级管理员将角色分配给其他管理员用户
1.2.3 管理员安全机制
•	强制所有管理员使用强密码，并设置密码有效期（如90天强制修改）
•	支持配置IP白名单，只允许从受信任的IP地址访问Admin后台
•	对所有涉及数据修改、权限变更、资金流动的敏感操作，强制要求操作者进行二次验证（如输入密码或MFA验证码）
•	管理员会话设有超时时间（如30分钟无操作自动退出登录）
1.2.4 管理员用户登录
•	登录方式：
◦	账号密码登录：输入框支持用户名、邮箱或手机号作为用户名。密码连续输入错误5次后，该账号将被锁定15分钟，并向其绑定的邮箱或手机发送安全提醒
◦	验证码登录：用户可通过邮箱或手机号接收验证码进行快捷登录，同样有发送频率限制
◦	扫码登录：PC端网页生成一个动态二维码（有效期2分钟，过期后可点击刷新），已登录的移动端App扫描后，在移动端上确认授权，PC端网页即可自动登录
•	安全与会话管理：
◦	提供"记住我"（或"自动登录"）选项，通过长效Refresh Token实现7天或30天的免密登录
◦	用户登录成功后，在个人中心或欢迎提示中显示本次及上次登录的时间与IP地理位置
◦	检测到异地或不常用设备登录时，必须向用户绑定的邮箱或手机发送安全警报
1.2.5 管理员用户个人中心
•	基本信息管理：
◦	头像上传（支持JPG/PNG/GIF，最大5MB，提供在线裁剪功能）
◦	昵称修改（2-20个字符，为防止滥用，30天内仅可修改一次）
◦	个人简介、性别、生日、地区、行业、职位等可选信息
•	账户安全管理：
◦	修改登录密码（必须验证原密码）
◦	绑定、更换、解绑手机号和邮箱地址（所有变更操作均需通过原渠道或密码进行安全验证）
◦	登录设备管理：清晰列出所有已授权登录的设备（设备类型、操作系统、最近活动时间、IP地址），并提供"远程下线"功能
◦	登录日志查看：提供详细的登录历史记录，包括时间、IP、地点、设备、登录方式（密码/扫码）、登录结果（成功/失败）
•	通知设置：
◦	用户可精细化设置各类通知的接收渠道和开关
◦	通知类型：系统公告、账单与支付通知、安全提醒、产品更新、营销活动邮件
◦	通知渠道：站内信、邮件、短信、浏览器推送
◦	支持设置"免打扰时段"
II. 核心业务域：AI智能体
此域负责智能体的创建、配置、版本控制和市场化管理。
2.1 智能体生命周期管理 [Admin]
2.1.1 智能体创建
•	基础信息设置：
◦	名称：智能体的显示名称（2-50字符，需经过敏感词过滤和唯一性检测），支持多语言
◦	唯一标识符：系统自动生成一个UUID，也可由管理员自定义一个易读的ID
◦	描述：简短描述（最多100字，用于卡片展示）和详细介绍（支持Markdown，最多2000字，用于详情页）
◦	分类和标签：从预设的分类树中选择（如"文案写作"、"数据分析"），并可添加自定义标签（最多10个）
◦	图标和封面：可从预设的图标库中选择，或上传自定义图片（有格式和尺寸限制，系统支持自动调整）
•	AI模型配置 (核心)：
◦	类型一：自定义AI智能体 (基于 Semantic Kernel)
▪	模型选择：从系统已配置的LLM模型列表中选择（如GPT-4o, Claude 3 Opus）。模型列表本身在系统配置中维护
▪	API Key管理：Semantic Kernel类型的智能体使用模型供应商级别的API Key管理策略：
▪	每个模型供应商（如OpenAI、Anthropic、Google Gemini等）在系统配置中只需配置一个API Key
▪	所有使用同一供应商模型的智能体共享该供应商的API Key
▪	API Key在系统级别进行统一管理和加密存储，智能体配置中只需选择模型即可
▪	核心参数：温度(0-2)、Top-P(0-1)、最大令牌数、频率惩罚(-2到2)、存在惩罚(-2到2)、停止序列。均提供滑块或输入框进行设置
▪	系统提示词 (System Prompt)：提供一个富文本编辑器，支持提示词模板和变量定义
▪	用户提示词 (User Prompt)：提供一个富文本编辑器，支持提示词模板和变量定义
▪	插件与函数调用：可从平台已注册的插件库中勾选并启用插件（如网络搜索、计算器、代码解释器）
◦	类型二：Dify集成智能体
▪	Dify应用类型与智能体关系：
▪	Dify提供多种应用类型：对话型应用（Chat）、Agent、工作流（Workflow）、补全型应用（Completion）等
▪	每个应用类型下可以创建多个智能体实例
▪	例如：在"对话型应用"类型下，可以创建"客服助手"、"产品咨询"、"技术支持"等多个智能体
▪	API Key管理：Dify类型的智能体采用一对一的API Key管理策略：
▪	每创建一个Dify智能体实例，都需要配置一个独立的Dify API Key
▪	该API Key专属于该智能体实例，不与其他智能体共享
▪	即使多个智能体属于同一Dify应用类型，它们的API Key也是相互独立的
▪	API Key必须加密存储，并在智能体配置中进行单独管理
▪	会话管理：Dify智能体的会话管理具有特殊性：
▪	首次对话时，Dify侧不存在conversation_id，需要在第一次请求后从Dify响应中获取
▪	获取到的Dify conversation_id需要与平台内的对话ID进行关联存储
▪	后续对话需要携带此conversation_id以保持会话上下文
▪	平台需要维护一个映射表：平台对话ID <-> Dify conversation_id
▪	应用类型选择：创建智能体时，首先选择要对接的Dify应用类型（对话型应用、Agent、工作流等）
▪	应用ID配置：根据所选类型，填入对应的Dify应用ID，此ID标识了在Dify平台上的具体应用
▪	参数配置：配置输入参数映射、响应模式（流式/阻塞）、超时和重试策略
•	高级功能配置：
◦	知识库：
▪	可关联一个或多个知识库。知识库本身是独立管理的资源，支持上传文档（PDF, TXT, MD）、网页抓取等
▪	可配置检索策略，如相似度阈值、返回最相关块数等
◦	对话管理：
▪	可为该智能体单独设置上下文消息数量限制、对话超时策略等
•	初始状态：新创建的智能体，其状态为 "草稿 (Draft)"
2.1.2 智能体编辑与版本控制
•	草稿编辑：对处于"草稿"状态的智能体进行配置修改，会直接保存在当前草稿上
•	版本管理：
◦	当一个"已发布 (Published)"的智能体需要修改时，系统不会直接修改线上版本。而是自动创建一个新的版本（如从 v1.0 -> v1.1），此新版本为"草稿"状态
◦	管理员可在此新版本草稿上自由修改和测试，不影响线上v1.0的正常服务
◦	版本对比：提供一个可视化界面，清晰地对比任意两个版本之间在提示词、模型参数、插件配置等方面的差异
◦	版本回滚：可以一键将线上服务回滚到任一历史的"已发布"版本
2.1.3 智能体测试
•	对话测试沙箱：在智能体编辑页面内嵌一个对话窗口，管理员可以实时与当前正在编辑的草稿版本进行对话，以调试和验证其效果
•	A/B测试：
◦	可创建一个A/B测试方案，选择两个不同的智能体版本（如v1.1和v1.2）
◦	设置流量分配比例（如50%用户看到v1.1，50%看到v1.2）
◦	定义评估指标（如用户满意度评分、任务完成率）
◦	发布测试后，系统将自动收集数据并生成对比分析报告
2.1.4 智能体发布与状态管理
•	发布 (Publish)：当一个草稿版本测试满意后，管理员可将其"发布"。发布后，该版本将成为新的线上版本，对所有客户可见
•	归档 (Archive)：管理员可将一个已发布的智能体"归档"。归档后，该智能体将从市场下架，客户无法再搜索到或发起新的对话，但已有的对话历史不受影响
•	状态流转：Draft -> Published -> Archived。一个智能体可以有多个版本，但同时只能有一个版本处于Published状态
2.2 智能体市场 [Shared]
2.2.1 市场浏览与发现 [Customer]
•	客户在智能体市场中只能看到和使用状态为"已发布"的智能体
•	提供强大的搜索功能，支持关键词模糊搜索、按分类和标签进行多重筛选
•	提供多种排序方式：按最新发布、按使用热度（调用次数）、按用户评分
•	每个智能体以统一的卡片样式展示，包含名称、图标、简短描述、标签和平均评分
•	点击卡片进入智能体详情页，可查看其详细介绍、功能特点、使用示例和所有用户的公开评价。用户可在此页面直接"开始对话"
◦	试用机制：对于某些高级智能体，可以配置有限次数或时长或一定数量的Token的免费试用
2.2.2 市场管理 [Admin]
•	管理员可以控制智能体是否在市场上"公开可见"
•	可以对特定的智能体进行"精选推荐"标记，使其在市场首页或分类列表的顶部突出显示
•	负责维护整个市场的分类体系和标签库
•	可以查看和管理用户对智能体的所有评价和反馈
III. 核心业务域：对话交互
此域是平台的核心用户体验，负责处理客户与智能体之间的所有交互。
3.1 对话会话管理 [Customer]
3.1.1 对话生命周期
•	发起对话：用户从智能体市场或历史记录中选择一个智能体后，系统即为此用户和智能体创建一个唯一的对话会话（Conversation）
•	历史记录管理：
◦	所有对话（包括用户输入、AI回复、时间戳、Token消耗等）都将被自动、实时地保存
◦	用户可在个人中心的"历史对话"区域查看所有对话列表
◦	列表支持按关键词搜索对话内容，或按智能体进行分组筛选
◦	用户可以随时继续之前的任何对话
•	对话操作：用户可以对单个对话进行重命名（方便识别）、删除（放入回收站，7天内可恢复）、归档（不显示在主列表，但可被搜索到）和导出（支持导出为Markdown、JSON或TXT格式）
3.1.2 消息交互
用户输入 (Request)：
•	文本消息：支持纯文本和Markdown语法输入。提供快捷短语和历史输入记录功能
•	富媒体消息：
◦	支持上传图片（JPG/PNG）、文档（PDF/TXT/DOCX），智能体是否能处理附件取决于其配置
◦	支持语音消息输入，系统自动进行语音转文字处理
•	消息操作：已发送的消息在2分钟内可以被撤回或编辑。输入框内容在发送前若切换页面，应自动保存为草稿
AI响应 (Response)：
•	流式输出 (Streaming)：AI的响应必须以流式方式返回，前端以平滑的打字机动画效果逐字或逐词显示，以提升交互体验和感知速度
•	格式化渲染：
◦	完美渲染Markdown，包括标题、列表、引用、粗体、斜体等
◦	对代码块进行语法高亮，并提供"一键复制"按钮
◦	支持数学公式的LaTeX渲染
◦	支持将数据渲染为美观的表格
◦	支持思考内容展示和隐藏
•	响应交互：
◦	对AI的每一条完整回复，用户都可以进行复制内容、分享（生成一个可公开访问的对话快照链接）、评分（赞/踩），以及反馈错误（提交反馈给管理员）
3.1.3 对话控制
•	流程控制：
◦	在AI响应过程中，用户可随时点击"停止生成"按钮，中断当前的流式输出
◦	对于不满意的回答，用户可以点击"重新生成"，让AI就同样的问题给出新的答案
•	上下文管理：
◦	提供"清空上下文"功能，让用户可以在当前对话中开启一个全新的话题，不受之前聊天内容的影响
◦	界面应以某种方式提示用户当前对话的上下文长度或Token消耗情况，帮助用户管理成本
IV. 核心业务域：订阅、订单、支付与配额
此域是平台的商业化基础，管理套餐、支付流程和资源使用。
4.1 订阅管理 [Shared]
4.1.1 套餐体系
•	[Admin] 套餐设计与管理：
◦	管理员可以创建、编辑、定价和发布/下架订阅套餐
◦	可配置套餐的名称、描述、价格（月付/年付）、包含的每月Token配额、API调用次数限制、可使用的智能体范围、并发请求数等各项权益
•	[Customer] 套餐展示与选择：
◦	Free套餐：新用户自动获得。权益包括：永久有效、5,000 Tokens/月、可使用基础智能体、最多保留10个对话历史
◦	基础版 (￥99/月)：100,000 Tokens/月，无限对话历史，邮件支持
◦	Pro版 (￥199/月)：500,000 Tokens/月，可使用所有高级智能体，API访问权限，优先技术支持
◦	Ultra版 (￥299/月)：1,000,000 Tokens/月，无限自定义智能体，优先功能体验，专属技术支持
◦	按需付费：提供一次性的Token补充包（如10万、50万、100万Tokens），购买后无时间限制，可与套餐配额叠加使用
◦	在定价页面，以清晰的表格形式对比各套餐权益
4.1.2 订阅生命周期 [Customer]
•	购买/升级：用户可随时选择购买或升级套餐。系统会清晰计算需要补交的差价，用户可选择"立即生效"或"下个账期生效"
•	降级/取消：用户可申请降级套餐或取消订阅，此变更将在当前账期结束后自动生效。系统会明确提示用户降级/取消后将失去的权益
•	续订：付费订阅默认开启"自动续费"，用户可在个人中心的订阅管理页面随时关闭。关闭后，订阅将在当前账期结束时自动过期
4.2 支付系统 [Shared]
4.2.1 支付流程 [Customer]
•	用户选择要购买的套餐或Token包后，系统生成一个待支付订单
•	在订单确认页面，用户可以使用优惠券码进行抵扣
•	选择支付方式：支付宝（扫码/网站支付）、微信支付（扫码/公众号支付）
•	跳转至支付网关完成支付。支付成功后，系统应在数秒内处理订单，为用户激活相应订阅或充值Token
•	用户可在"订单管理"页面查看所有历史订单的状态（待支付、已完成、已取消、已退款）
4.2.2 支付与订单管理 [Admin]
•	管理员可查看平台所有的支付流水和订单记录，支持按用户、时间、金额、状态等进行筛选查询
•	提供退款处理功能。当客户申请退款时，管理员审核通过后可在此处执行原路退款操作
•	优惠券管理：
◦	创建：可创建折扣券、满减券、代金券
◦	配置：可设置优惠券的使用门槛、有效期、适用范围（特定套餐）、可叠加规则等
◦	发放：可生成优惠券码用于批量发放，或配置为新用户自动发放、活动领取等
4.3 配额管理 [Shared]
4.3.1 配额类型与计算
•	[Admin] 配置：管理员负责配置不同AI模型的Token计算规则（例如，输入与输出的计费系数）
•	核心配额类型：
◦	Token配额：平台最核心的计量单位，用于衡量大语言模型的使用量
◦	功能配额：如每月API调用次数、可创建的自定义智能体数量等，由订阅套餐决定
4.3.2 配额监控与预警 [Customer]
•	在用户界面的显眼位置（如导航栏、个人中心），实时显示当前账期剩余的Token数量和过期日期
•	提供可视化的配额使用历史图表，帮助用户分析其消耗模式
•	预警机制：当用户配额使用达到80%、95%以及完全耗尽时，系统必须通过站内信和邮件向用户发送预警通知。配额耗尽后，用户将无法再使用消耗Token的服务，直到下个周期重置或购买新的配额
V. 核心业务域：分析与系统运维 [Admin]
此域为平台管理者提供决策支持和系统维护能力。
5.1 数据分析
•	实时仪表盘 (Dashboard)：
◦	关键指标：总用户数、今日新增用户、活跃用户数（DAU/MAU）、当前付费用户数、总收入、今日收入、今日Token总消耗
◦	图表：最近30天用户增长曲线、收入趋势图、各套餐收入占比饼图
•	用户分析：
◦	提供用户增长、留存率（次日、7日、30日）、流失分析报告
◦	用户画像分析（地域分布、行业分布等）
◦	转化漏斗：分析"访问 -> 注册 -> 激活Free -> 首次付费 -> 续费"各环节的转化率
•	智能体分析：
◦	提供所有智能体的综合使用报告，包括总调用次数、日均调用量、总Token消耗、用户平均评分等
◦	可下钻到单个智能体，查看其详细的性能指标和用户反馈
•	财务分析：
◦	详细的收入报告，支持按时间、套餐、支付方式进行筛选
◦	成本分析，估算API调用、服务器、带宽等成本
◦	提供LTV（用户生命周期价值）和CAC（用户获取成本）的关键指标
5.2 系统管理与运维
•	系统配置：
◦	提供一个集中的配置管理界面，用于设置站点名称、Logo、系统公告、平台维护模式开关
◦	管理第三方服务的API密钥和配置（如邮件服务、支付网关、对象存储等）
•	日志管理：
◦	操作日志：记录所有管理员和用户的关键操作，支持按操作人、时间、IP、操作类型进行查询审计
◦	系统日志：聚合应用的所有运行时日志（错误、警告、信息），提供全文搜索和筛选功能，用于故障排查
•	监控告警：
◦	基础设施监控：监控服务器的CPU、内存、磁盘和网络使用率
◦	应用性能监控 (APM)：监控API接口的平均响应时间、P95/P99响应时间、错误率、QPS等
◦	业务监控：监控核心业务流程的健康度，如支付成功率、注册成功率、智能体可用性
◦	告警管理：可配置告警规则和阈值，当监控指标异常时，通过邮件、钉钉或企业微信等渠道向指定人员发送告警通知
•	备份与恢复：
◦	数据库必须配置每日自动全量/增量备份策略
◦	备份数据应存储在异地容灾位置
◦	提供清晰的灾难恢复预案和流程文档
VI. 非功能性需求
•	性能要求：
◦	核心API（登录、获取用户信息等）平均响应时间小于200ms
◦	页面加载性能：主要页面首次内容绘制（FCP）时间小于 2s
◦	AI流式响应首个Token返回时间（TTFT）小于1s
•	安全性要求：
◦	遵循OWASP Top 10安全实践，防止SQL注入、XSS、CSRF等常见攻击
◦	所有敏感数据（如用户密码、API密钥）在数据库中必须加密存储
◦	所有网络通信必须使用HTTPS/WSS加密
•	可扩展性要求：
◦	单体应用部署架构应支持水平扩展，能够通过增加应用服务器实例来应对用户量的增长
◦	数据库和缓存等基础设施也应支持集群和扩展
◦	多项目分层架构设计应充分考虑未来可能的功能扩展和架构演进需求
•	可用性要求：
◦	核心系统可用性目标 > 99.9%
◦	应具备优雅的降级能力，在非核心服务（如分析、日志）出现故障时，不影响主流程（如对话、支付）的正常使用
VII. 分层架构设计
7.1 架构概述
平台采用清晰的分层架构设计，每一层都是独立的项目，层与层之间通过接口抽象层进行解耦。这种设计既保持了单体应用的部署简单性，又具备了良好的代码组织和可维护性。
7.2 项目分层结构
7.2.1 展现层 (WhimlabAI.WebApi)
负责处理HTTP请求、路由、认证授权、请求验证等
•	/Controllers
◦	/AdminAuth - Admin用户认证相关控制器
◦	/CustomerAuth - Customer用户认证相关控制器
◦	/AdminUser - Admin用户管理控制器
◦	/CustomerUser - Customer用户管理控制器
◦	/Agent - 智能体管理控制器
◦	/Conversation - 对话管理控制器
◦	/Subscription - 订阅管理控制器
◦	/Payment - 支付相关控制器
◦	/Admin - 管理后台控制器
•	/Filters - 过滤器（认证、异常处理、日志等）
•	/Middleware - 中间件（跨域、限流、监控等）
•	/Models - 请求/响应模型
7.2.2 应用层 (WhimlabAI.Application)
负责协调领域层完成业务用例，实现业务流程编排
•	/Services
◦	/AdminAuth - Admin认证服务实现
◦	/CustomerAuth - Customer认证服务实现
◦	/CustomerUser - Customer用户服务实现
◦	/AdminUser - Admin用户服务实现
◦	/Agent - 智能体服务实现
◦	/Conversation - 对话服务实现
◦	/Subscription - 订阅服务实现
◦	/Payment - 支付服务实现
◦	/Analytics - 数据分析服务实现
•	/DTOs - 数据传输对象
•	/Mappers - 对象映射配置
•	/Validators - 业务验证器
7.2.3 领域层 (WhimlabAI.Domain)
包含核心业务逻辑、领域模型、业务规则
•	/Entities
◦	/Customer User - Customer用户聚合根及相关实体
◦	/AdminUser - Admin用户聚合根及相关实体
◦	/Agent - 智能体聚合根及相关实体
◦	/Conversation - 对话聚合根及相关实体
◦	/Subscription - 订阅聚合根及相关实体
◦	/Payment - 支付聚合根及相关实体
•	/ValueObjects - 值对象（如Token配额、订阅状态等）
•	/DomainServices - 领域服务
•	/DomainEvents - 领域事件
•	/Specifications - 业务规则规约
7.2.4 基础设施层 (WhimlabAI.Infrastructure)
负责数据持久化、外部服务集成、技术基础设施
•	/Persistence
◦	/Repositories - 仓储实现
◦	/DbContext - 数据库上下文(pgsql)
◦	/Migrations - 数据库迁移(pgsql)
•	/ExternalServices
◦	/AI - AI模型服务集成（OpenAI、Dify等）
◦	/Payment - 支付网关集成（支付宝、微信支付）
◦	/Messaging - 消息服务（邮件、短信）
◦	/Storage - 对象存储服务
•	/Caching - 缓存实现
•	/Logging - 日志实现
•	/Security - 安全相关实现（加密、令牌等）
7.2.5 接口抽象层 (WhimlabAI.Abstractions)
定义各层之间的契约接口，实现层间解耦
•	/Application
◦	/ICustomerAuthService
◦	/IAdminAuthService
◦	/ICustomerUserService
◦	/IAdminUserService
◦	/IAgentService
◦	/IConversationService
◦	/ISubscriptionService
◦	/IPaymentService
•	/Domain
◦	/IRepository
◦	/IUnitOfWork
◦	/ICustomerUserRepository
◦	/IAdminUserRepository
◦	/IAgentRepository
◦	/IConversationRepository
◦	/ISubscriptionRepository
•	/Infrastructure
◦	/IAIProvider
◦	/IPaymentGateway
◦	/IMessageService
◦	/IStorageService
◦	/ICacheService
7.2.6 共享层 (WhimlabAI.Shared)
包含各层都可能使用的通用功能
•	/Constants - 常量定义
•	/Dtos - 数据传输对象定义
•	/Enums - 枚举定义
•	/Extensions - 扩展方法
•	/Utilities - 工具类
•	/Exceptions - 自定义异常
•	/Results - 通用结果包装类
7.3 层间依赖关系
7.3.1 依赖方向
•	WebApi层 → 依赖 → Application层、Abstractions层
•	Application层 → 依赖 → Domain层、Abstractions层
•	Domain层 → 依赖 → Abstractions层（仅领域接口）
•	Infrastructure层 → 依赖 → Domain层、Abstractions层
•	所有层 → 都可依赖 → Shared层
7.3.2 依赖注入配置
•	在WebApi层的启动配置中，通过依赖注入容器将具体实现注册到对应的接口
•	Infrastructure层的具体实现通过接口注册到容器中
•	Application层通过构造函数注入所需的接口
7.4 架构优势
7.4.1 高内聚低耦合
•	每一层职责明确，只负责特定的功能
•	通过接口抽象层隔离，层与层之间没有直接依赖
7.4.2 可测试性
•	可以独立测试每一层
•	通过接口模拟（Mock）轻松实现单元测试
7.4.3 可维护性
•	修改某一层的实现不会影响其他层
•	业务逻辑集中在领域层，便于理解和维护
7.4.4 技术无关性
•	可以独立替换基础设施层的技术实现
•	领域层不依赖任何特定技术框架
通过这种分层架构设计，虽然是单体应用部署，但代码组织清晰、职责分明，为系统的长期演进和维护奠定了良好基础。
